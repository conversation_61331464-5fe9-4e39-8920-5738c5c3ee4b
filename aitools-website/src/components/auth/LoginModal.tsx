'use client';

import { useState } from 'react';
import { signIn } from 'next-auth/react';
import { FaGoogle, FaGithub, FaEnvelope, FaTimes } from 'react-icons/fa';

interface LoginModalProps {
  isOpen: boolean;
  onClose: () => void;
}

type LoginStep = 'method' | 'email' | 'code';

export default function LoginModal({ isOpen, onClose }: LoginModalProps) {
  const [step, setStep] = useState<LoginStep>('method');
  const [email, setEmail] = useState('');
  const [verificationToken, setVerificationToken] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [emailError, setEmailError] = useState('');

  const showToast = (message: string, type: 'success' | 'error' = 'success') => {
    // Simple toast implementation
    const toast = document.createElement('div');
    toast.className = `fixed top-4 right-4 p-4 rounded-lg text-white z-50 ${
      type === 'success' ? 'bg-green-500' : 'bg-red-500'
    }`;
    toast.textContent = message;
    document.body.appendChild(toast);
    setTimeout(() => document.body.removeChild(toast), 3000);
  };

  const handleClose = () => {
    setStep('method');
    setEmail('');
    setVerificationToken('');
    setEmailError('');
    onClose();
  };

  const handleOAuthLogin = async (provider: 'google' | 'github') => {
    try {
      setIsLoading(true);
      await signIn(provider, { callbackUrl: '/' });
    } catch (error) {
      showToast('登录失败，请稍后重试', 'error');
    } finally {
      setIsLoading(false);
    }
  };

  const handleEmailSubmit = async () => {
    if (!email) {
      setEmailError('请输入邮箱地址');
      return;
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      setEmailError('请输入有效的邮箱地址');
      return;
    }

    setEmailError('');
    setIsLoading(true);

    try {
      const response = await fetch('/api/auth/send-code', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      });

      const data = await response.json();

      if (data.success) {
        setVerificationToken(data.token);
        setStep('code');
        showToast('验证码已发送，请查看您的邮箱');
      } else {
        showToast(data.error || '发送失败，请稍后重试', 'error');
      }
    } catch (error) {
      showToast('网络错误，请检查网络连接', 'error');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCodeVerify = async (code: string) => {
    if (code.length !== 6) return;

    setIsLoading(true);

    try {
      const result = await signIn('email-code', {
        email,
        code,
        token: verificationToken,
        redirect: false,
      });

      if (result?.ok) {
        showToast('登录成功，欢迎回来！');
        handleClose();
        // NextAuth会自动更新session，不需要手动刷新页面
      } else {
        showToast(result?.error || '验证码错误', 'error');
      }
    } catch (error) {
      showToast('网络错误，请检查网络连接', 'error');
    } finally {
      setIsLoading(false);
    }
  };

  const renderMethodStep = () => (
    <div className="space-y-4">
      <p className="text-gray-600 text-center">
        选择登录方式
      </p>

      <div className="space-y-3">
        <button
          className="w-full flex items-center justify-center gap-3 px-4 py-3 border border-red-300 rounded-lg text-red-700 hover:bg-red-50 transition-colors disabled:opacity-50"
          onClick={() => handleOAuthLogin('google')}
          disabled={isLoading}
        >
          <FaGoogle />
          使用 Google 登录
        </button>

        <button
          className="w-full flex items-center justify-center gap-3 px-4 py-3 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors disabled:opacity-50"
          onClick={() => handleOAuthLogin('github')}
          disabled={isLoading}
        >
          <FaGithub />
          使用 GitHub 登录
        </button>
      </div>

      <div className="relative">
        <div className="absolute inset-0 flex items-center">
          <div className="w-full border-t border-gray-300" />
        </div>
        <div className="relative flex justify-center text-sm">
          <span className="px-2 bg-white text-gray-500">或</span>
        </div>
      </div>

      <button
        className="w-full flex items-center justify-center gap-3 px-4 py-3 border border-blue-300 rounded-lg text-blue-700 hover:bg-blue-50 transition-colors"
        onClick={() => setStep('email')}
      >
        <FaEnvelope />
        使用邮箱登录
      </button>
    </div>
  );

  const renderEmailStep = () => (
    <div className="space-y-4">
      <p className="text-gray-600 text-center">
        输入您的邮箱地址，我们将发送验证码
      </p>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          邮箱地址
        </label>
        <input
          type="email"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          placeholder="请输入邮箱地址"
          onKeyPress={(e) => e.key === 'Enter' && handleEmailSubmit()}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        />
        {emailError && (
          <p className="mt-1 text-sm text-red-600">{emailError}</p>
        )}
      </div>

      <div className="space-y-3">
        <button
          className="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50"
          onClick={handleEmailSubmit}
          disabled={isLoading}
        >
          {isLoading ? '发送中...' : '发送验证码'}
        </button>

        <button
          className="w-full px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
          onClick={() => setStep('method')}
        >
          返回
        </button>
      </div>
    </div>
  );

  const handleCodeInputChange = (index: number, value: string) => {
    if (value.length > 1) return;

    const inputs = document.querySelectorAll('.code-input') as NodeListOf<HTMLInputElement>;
    inputs[index].value = value;

    // Auto-focus next input
    if (value && index < 5) {
      inputs[index + 1]?.focus();
    }

    // Check if all inputs are filled
    const code = Array.from(inputs).map(input => input.value).join('');
    if (code.length === 6) {
      handleCodeVerify(code);
    }
  };

  const renderCodeStep = () => (
    <div className="space-y-4">
      <p className="text-gray-600 text-center">
        请输入发送到 {email} 的6位验证码
      </p>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          验证码
        </label>
        <div className="flex justify-center gap-2">
          {[0, 1, 2, 3, 4, 5].map((index) => (
            <input
              key={index}
              type="text"
              maxLength={1}
              onChange={(e) => handleCodeInputChange(index, e.target.value)}
              disabled={isLoading}
              className="code-input w-12 h-12 text-center text-lg border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:opacity-50"
            />
          ))}
        </div>
      </div>

      <div className="space-y-3">
        <button
          className="w-full px-4 py-2 text-sm text-gray-600 hover:text-gray-800 transition-colors"
          onClick={() => setStep('email')}
        >
          重新发送验证码
        </button>

        <button
          className="w-full px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
          onClick={() => setStep('method')}
        >
          返回
        </button>
      </div>
    </div>
  );

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Overlay */}
      <div
        className="absolute inset-0 bg-black bg-opacity-50"
        onClick={handleClose}
      />

      {/* Modal */}
      <div className="relative bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <h2 className="text-xl font-semibold text-gray-900 text-center flex-1">
            {step === 'method' && '登录 AI Tools Directory'}
            {step === 'email' && '邮箱登录'}
            {step === 'code' && '输入验证码'}
          </h2>
          <button
            onClick={handleClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <FaTimes />
          </button>
        </div>

        {/* Body */}
        <div className="p-6">
          {step === 'method' && renderMethodStep()}
          {step === 'email' && renderEmailStep()}
          {step === 'code' && renderCodeStep()}
        </div>
      </div>
    </div>
  );
}

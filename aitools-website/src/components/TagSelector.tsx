'use client';

import React, { useState } from 'react';
import { Tag, ChevronDown, ChevronUp } from 'lucide-react';
import { TAGS_BY_CATEGORY, MAX_TAGS_COUNT } from '@/constants/tags';

interface TagSelectorProps {
  selectedTags: string[];
  onTagsChange: (tags: string[]) => void;
  maxTags?: number;
}

export default function TagSelector({ 
  selectedTags, 
  onTagsChange, 
  maxTags = MAX_TAGS_COUNT 
}: TagSelectorProps) {
  const [expandedCategories, setExpandedCategories] = useState<string[]>([]);
  const [showAllCategories, setShowAllCategories] = useState(false);

  const toggleCategory = (category: string) => {
    setExpandedCategories(prev => 
      prev.includes(category) 
        ? prev.filter(c => c !== category)
        : [...prev, category]
    );
  };

  const toggleTag = (tag: string) => {
    if (selectedTags.includes(tag)) {
      onTagsChange(selectedTags.filter(t => t !== tag));
    } else if (selectedTags.length < maxTags) {
      onTagsChange([...selectedTags, tag]);
    }
  };

  const removeTag = (tag: string) => {
    onTagsChange(selectedTags.filter(t => t !== tag));
  };

  const categories = Object.keys(TAGS_BY_CATEGORY);
  const displayedCategories = showAllCategories ? categories : categories.slice(0, 5);

  return (
    <div className="space-y-4">
      {/* 标题和计数器 */}
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium text-gray-900">选择标签</h3>
        <span className="text-sm text-gray-500">
          已选择 {selectedTags.length}/{maxTags} 个标签
        </span>
      </div>

      {/* 已选择的标签 */}
      {selectedTags.length > 0 && (
        <div className="space-y-2">
          <h4 className="text-sm font-medium text-gray-700">已选择的标签：</h4>
          <div className="flex flex-wrap gap-2">
            {selectedTags.map((tag) => (
              <span
                key={tag}
                className="inline-flex items-center px-3 py-1 rounded-md text-sm font-medium bg-blue-100 text-blue-800"
              >
                {tag}
                <button
                  type="button"
                  onClick={() => removeTag(tag)}
                  className="ml-2 text-blue-600 hover:text-blue-800"
                >
                  ×
                </button>
              </span>
            ))}
          </div>
        </div>
      )}

      {/* 标签选择区域 */}
      <div className="space-y-3">
        <h4 className="text-sm font-medium text-gray-700">按分类选择标签：</h4>
        
        {displayedCategories.map((category) => {
          const isExpanded = expandedCategories.includes(category);
          const categoryTags = TAGS_BY_CATEGORY[category as keyof typeof TAGS_BY_CATEGORY];
          
          return (
            <div key={category} className="border border-gray-200 rounded-lg">
              <button
                type="button"
                onClick={() => toggleCategory(category)}
                className="w-full px-4 py-3 text-left flex items-center justify-between hover:bg-gray-50"
              >
                <span className="font-medium text-gray-900">{category}</span>
                <div className="flex items-center space-x-2">
                  <span className="text-sm text-gray-500">
                    {categoryTags.filter(tag => selectedTags.includes(tag)).length}/{categoryTags.length}
                  </span>
                  {isExpanded ? (
                    <ChevronUp className="h-4 w-4 text-gray-400" />
                  ) : (
                    <ChevronDown className="h-4 w-4 text-gray-400" />
                  )}
                </div>
              </button>
              
              {isExpanded && (
                <div className="px-4 pb-4">
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                    {categoryTags.map((tag) => {
                      const isSelected = selectedTags.includes(tag);
                      const isDisabled = !isSelected && selectedTags.length >= maxTags;
                      
                      return (
                        <button
                          key={tag}
                          type="button"
                          onClick={() => toggleTag(tag)}
                          disabled={isDisabled}
                          className={`
                            px-3 py-2 text-sm rounded-md border transition-colors text-left
                            ${isSelected 
                              ? 'bg-blue-100 border-blue-300 text-blue-800' 
                              : isDisabled
                                ? 'bg-gray-100 border-gray-200 text-gray-400 cursor-not-allowed'
                                : 'bg-white border-gray-200 text-gray-700 hover:bg-gray-50'
                            }
                          `}
                        >
                          <div className="flex items-center">
                            <Tag className="h-3 w-3 mr-1" />
                            {tag}
                          </div>
                        </button>
                      );
                    })}
                  </div>
                </div>
              )}
            </div>
          );
        })}

        {/* 显示更多分类按钮 */}
        {categories.length > 5 && (
          <button
            type="button"
            onClick={() => setShowAllCategories(!showAllCategories)}
            className="w-full px-4 py-2 text-sm text-blue-600 hover:text-blue-800 border border-blue-200 rounded-lg hover:bg-blue-50"
          >
            {showAllCategories ? '收起分类' : `显示更多分类 (${categories.length - 5})`}
          </button>
        )}
      </div>

      {/* 提示信息 */}
      {selectedTags.length >= maxTags && (
        <p className="text-sm text-amber-600">
          最多只能选择{maxTags}个标签
        </p>
      )}
    </div>
  );
}

import mongoose, { Document, Schema } from 'mongoose';

export interface ITool extends Document {
  name: string;
  tagline?: string; // 工具标语/副标题
  description: string;
  longDescription?: string;
  website: string;
  logo?: string;
  category: string;
  tags: string[];
  pricing: 'free' | 'freemium' | 'paid';
  pricingDetails?: string;
  screenshots?: string[];
  submittedBy: string; // User ID who submitted
  submittedAt: Date;
  publishedAt?: Date;
  status: 'pending' | 'approved' | 'rejected';
  reviewNotes?: string;
  reviewedBy?: string; // Admin ID who reviewed
  reviewedAt?: Date;
  views: number;
  likes: number;
  likedBy: string[]; // 点赞用户ID列表
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

const ToolSchema: Schema = new Schema({
  name: {
    type: String,
    required: [true, 'Tool name is required'],
    trim: true,
    maxlength: [100, 'Tool name cannot exceed 100 characters']
  },
  tagline: {
    type: String,
    trim: true,
    maxlength: [200, 'Tagline cannot exceed 200 characters']
  },
  description: {
    type: String,
    required: [true, 'Tool description is required'],
    trim: true,
    maxlength: [500, 'Description cannot exceed 500 characters']
  },
  longDescription: {
    type: String,
    trim: true,
    maxlength: [2000, 'Long description cannot exceed 2000 characters']
  },
  website: {
    type: String,
    required: [true, 'Website URL is required'],
    trim: true,
    validate: {
      validator: function(v: string) {
        return /^https?:\/\/.+/.test(v);
      },
      message: 'Please enter a valid URL'
    }
  },
  logo: {
    type: String,
    trim: true
  },
  category: {
    type: String,
    required: [true, 'Category is required'],
    enum: [
      'text-generation',
      'image-generation', 
      'video-generation',
      'audio-generation',
      'code-generation',
      'data-analysis',
      'productivity',
      'design',
      'marketing',
      'education',
      'research',
      'other'
    ]
  },
  tags: [{
    type: String,
    trim: true,
    lowercase: true
  }],
  pricing: {
    type: String,
    required: [true, 'Pricing model is required'],
    enum: ['free', 'freemium', 'paid']
  },
  pricingDetails: {
    type: String,
    trim: true,
    maxlength: [500, 'Pricing details cannot exceed 500 characters']
  },
  screenshots: [{
    type: String,
    trim: true
  }],
  submittedBy: {
    type: String,
    required: [true, 'Submitter ID is required'],
    trim: true
  },
  submittedAt: {
    type: Date,
    default: Date.now
  },
  publishedAt: {
    type: Date
  },
  status: {
    type: String,
    required: true,
    enum: ['pending', 'approved', 'rejected'],
    default: 'pending'
  },
  reviewNotes: {
    type: String,
    trim: true,
    maxlength: [1000, 'Review notes cannot exceed 1000 characters']
  },
  reviewedBy: {
    type: String,
    trim: true
  },
  reviewedAt: {
    type: Date
  },
  views: {
    type: Number,
    default: 0,
    min: 0
  },
  likes: {
    type: Number,
    default: 0,
    min: 0
  },
  likedBy: [{
    type: String,
    trim: true
  }],
  isActive: {
    type: Boolean,
    default: true
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for better query performance
ToolSchema.index({ status: 1, isActive: 1 });
ToolSchema.index({ category: 1, status: 1 });
ToolSchema.index({ tags: 1, status: 1 });
ToolSchema.index({ submittedBy: 1 });
ToolSchema.index({ publishedAt: -1 });
ToolSchema.index({ views: -1 });
ToolSchema.index({ likes: -1 });

// Text search index
ToolSchema.index({
  name: 'text',
  tagline: 'text',
  description: 'text',
  longDescription: 'text',
  tags: 'text'
});

export default mongoose.models.Tool || mongoose.model<ITool>('Tool', ToolSchema);

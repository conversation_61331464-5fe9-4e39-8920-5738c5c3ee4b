'use client';

import { useState } from 'react';
import { signIn, useSession } from 'next-auth/react';

export default function TestLoginPage() {
  const { data: session, status } = useSession();
  const [email, setEmail] = useState('<EMAIL>');
  const [code, setCode] = useState('');
  const [token, setToken] = useState('');
  const [loading, setLoading] = useState(false);

  const sendCode = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/auth/send-code', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ email }),
      });
      const data = await response.json();
      if (data.success) {
        setToken(data.token);
        alert('验证码已发送！Token: ' + data.token);
      } else {
        alert('发送失败: ' + data.error);
      }
    } catch (error) {
      alert('网络错误');
    } finally {
      setLoading(false);
    }
  };

  const verifyAndLogin = async () => {
    if (!code || !token) {
      alert('请先发送验证码并输入验证码');
      return;
    }

    setLoading(true);
    try {
      // 使用自定义登录API
      const response = await fetch('/api/auth/custom-login', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ email, code, token }),
      });
      const data = await response.json();

      if (data.success) {
        alert('登录成功！');
        // 刷新页面以更新session
        window.location.reload();
      } else {
        alert('验证失败: ' + data.error);
      }
    } catch (error) {
      alert('网络错误: ' + error);
    } finally {
      setLoading(false);
    }
  };

  const testCredentials = async () => {
    console.log('开始测试Credentials provider...');
    try {
      const result = await signIn('credentials', {
        username: 'test',
        password: 'test',
        redirect: false,
      });
      console.log('SignIn result:', result);
      alert('测试结果: ' + JSON.stringify(result));
    } catch (error) {
      console.error('测试错误:', error);
      alert('测试错误: ' + error);
    }
  };

  return (
    <div className="min-h-screen bg-gray-100 py-12 px-4">
      <div className="max-w-md mx-auto bg-white rounded-lg shadow-md p-6">
        <h1 className="text-2xl font-bold mb-6">测试邮件验证码登录</h1>
        
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              邮箱地址
            </label>
            <input
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="输入邮箱地址"
            />
          </div>

          <button
            onClick={sendCode}
            disabled={loading || !email}
            className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 disabled:opacity-50"
          >
            {loading ? '发送中...' : '发送验证码'}
          </button>

          {token && (
            <div className="p-3 bg-gray-100 rounded-md">
              <p className="text-sm text-gray-600">Token: {token}</p>
            </div>
          )}

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              验证码
            </label>
            <input
              type="text"
              value={code}
              onChange={(e) => setCode(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="输入6位验证码"
              maxLength={6}
            />
          </div>

          <button
            onClick={verifyAndLogin}
            disabled={loading || !code || !token}
            className="w-full bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700 disabled:opacity-50"
          >
            {loading ? '验证中...' : '验证并登录'}
          </button>

          <button
            onClick={testCredentials}
            className="w-full bg-purple-600 text-white py-2 px-4 rounded-md hover:bg-purple-700"
          >
            测试Credentials Provider
          </button>
        </div>

        <div className="mt-8 p-4 bg-gray-50 rounded-md">
          <h2 className="text-lg font-semibold mb-2">当前登录状态</h2>
          <p className="text-sm text-gray-600">状态: {status}</p>
          {session ? (
            <div className="mt-2">
              <p className="text-sm"><strong>用户ID:</strong> {session.user?.id}</p>
              <p className="text-sm"><strong>邮箱:</strong> {session.user?.email}</p>
              <p className="text-sm"><strong>姓名:</strong> {session.user?.name}</p>
              <p className="text-sm"><strong>角色:</strong> {session.user?.role}</p>
            </div>
          ) : (
            <p className="text-sm text-gray-500">未登录</p>
          )}
        </div>
      </div>
    </div>
  );
}

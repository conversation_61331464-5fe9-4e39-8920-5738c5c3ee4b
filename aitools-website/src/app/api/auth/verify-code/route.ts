import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '../../../../lib/mongodb';
import User from '../../../../models/User';
import { signIn } from 'next-auth/react';

// POST /api/auth/verify-code - 验证邮件验证码并登录
export async function POST(request: NextRequest) {
  try {
    await dbConnect();

    const { email, code, token } = await request.json();

    // 验证输入
    if (!email || !code || !token) {
      return NextResponse.json(
        { success: false, error: '请提供邮箱、验证码和验证令牌' },
        { status: 400 }
      );
    }

    // 查找用户
    const user = await User.findOne({
      email: email.toLowerCase(),
      emailVerificationExpires: { $gt: new Date() }
    });

    if (!user) {
      return NextResponse.json(
        { success: false, error: '验证码已过期或用户不存在' },
        { status: 400 }
      );
    }

    // 验证token和验证码
    const storedData = user.emailVerificationToken;
    if (!storedData || !storedData.includes(':')) {
      return NextResponse.json(
        { success: false, error: '验证码无效' },
        { status: 400 }
      );
    }

    const [storedToken, storedCode] = storedData.split(':');

    if (storedToken !== token || storedCode !== code) {
      return NextResponse.json(
        { success: false, error: '验证码错误' },
        { status: 400 }
      );
    }

    // 验证成功，更新用户状态
    user.emailVerified = true;
    user.emailVerificationToken = undefined;
    user.emailVerificationExpires = undefined;
    user.lastLoginAt = new Date();

    // 如果用户没有邮箱账户记录，添加一个
    const hasEmailAccount = user.accounts.some(acc => acc.provider === 'email');
    if (!hasEmailAccount) {
      user.accounts.push({
        provider: 'email',
        providerId: 'email',
        providerAccountId: user.email,
      });
    }

    await user.save();

    // 返回成功，前端将使用这个信息来调用NextAuth signIn
    return NextResponse.json({
      success: true,
      message: '验证成功',
      user: {
        id: user._id.toString(),
        email: user.email,
        name: user.name,
        avatar: user.avatar,
        role: user.role,
        emailVerified: user.emailVerified,
      }
    });

  } catch (error) {
    console.error('Verify code error:', error);
    return NextResponse.json(
      { success: false, error: '服务器错误，请稍后重试' },
      { status: 500 }
    );
  }
}

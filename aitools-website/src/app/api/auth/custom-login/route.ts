import { NextRequest, NextResponse } from 'next/server';
import jwt from 'jsonwebtoken';
import dbConnect from '../../../../lib/mongodb';
import User from '../../../../models/User';

export async function POST(request: NextRequest) {
  try {
    const { email, code, token } = await request.json();

    if (!email || !code || !token) {
      return NextResponse.json({
        success: false,
        error: '缺少必要参数'
      }, { status: 400 });
    }

    // 验证token
    let decoded;
    try {
      decoded = jwt.verify(token, process.env.NEXTAUTH_SECRET!) as any;
    } catch (error) {
      return NextResponse.json({
        success: false,
        error: '验证token无效或已过期'
      }, { status: 400 });
    }

    if (decoded.email !== email) {
      return NextResponse.json({
        success: false,
        error: '邮箱不匹配'
      }, { status: 400 });
    }

    // 验证验证码
    if (decoded.code !== code) {
      return NextResponse.json({
        success: false,
        error: '验证码错误'
      }, { status: 400 });
    }

    // 检查验证码是否过期（5分钟）
    if (Date.now() - decoded.timestamp > 5 * 60 * 1000) {
      return NextResponse.json({
        success: false,
        error: '验证码已过期'
      }, { status: 400 });
    }

    // 连接数据库
    await dbConnect();

    // 查找或创建用户
    let user = await User.findOne({ email });
    if (!user) {
      user = new User({
        email,
        name: email.split('@')[0],
        emailVerified: true,
        lastLoginAt: new Date(),
      });
      await user.save();
    } else {
      user.lastLoginAt = new Date();
      await user.save();
    }

    // 创建NextAuth兼容的JWT token
    const nextAuthToken = jwt.sign(
      {
        sub: user._id.toString(),
        email: user.email,
        name: user.name,
        picture: user.avatar,
        iat: Math.floor(Date.now() / 1000),
        exp: Math.floor(Date.now() / 1000) + (30 * 24 * 60 * 60), // 30天
        jti: Math.random().toString(36).substring(2),
      },
      process.env.NEXTAUTH_SECRET!
    );

    // 设置NextAuth session cookie
    const response = NextResponse.json({
      success: true,
      message: '登录成功',
      user: {
        id: user._id.toString(),
        email: user.email,
        name: user.name,
        image: user.avatar,
        role: user.role,
      }
    });

    // 设置NextAuth session cookie
    response.cookies.set('next-auth.session-token', nextAuthToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 30 * 24 * 60 * 60, // 30天
      path: '/',
    });

    return response;

  } catch (error) {
    console.error('Custom login error:', error);
    return NextResponse.json({
      success: false,
      error: '服务器错误'
    }, { status: 500 });
  }
}

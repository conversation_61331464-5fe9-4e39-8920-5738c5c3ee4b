import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/mongodb';
import Tool from '@/models/Tool';
import mongoose from 'mongoose';

// GET /api/tools/[id] - 获取单个工具详情
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await dbConnect();

    const { id } = params;

    // 验证ID格式
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return NextResponse.json(
        { success: false, error: '无效的工具ID' },
        { status: 400 }
      );
    }

    // 查找工具
    const tool = await Tool.findById(id)
      .select('-submittedBy -reviewNotes -reviewedBy')
      .lean();

    if (!tool) {
      return NextResponse.json(
        { success: false, error: '工具不存在' },
        { status: 404 }
      );
    }

    // 增加浏览量
    await Tool.findByIdAndUpdate(id, { $inc: { views: 1 } });

    return NextResponse.json({
      success: true,
      data: tool
    });

  } catch (error) {
    console.error('Error fetching tool:', error);
    return NextResponse.json(
      { success: false, error: '获取工具详情失败' },
      { status: 500 }
    );
  }
}

// PUT /api/tools/[id] - 更新工具信息
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await dbConnect();

    const { id } = params;
    const body = await request.json();

    // 验证ID格式
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return NextResponse.json(
        { success: false, error: '无效的工具ID' },
        { status: 400 }
      );
    }

    // 查找工具
    const tool = await Tool.findById(id);
    if (!tool) {
      return NextResponse.json(
        { success: false, error: '工具不存在' },
        { status: 404 }
      );
    }

    // 更新允许的字段
    const allowedUpdates = [
      'name', 'description', 'longDescription', 'website', 'logo',
      'category', 'pricing', 'pricingDetails', 'tags', 'features',
      'screenshots', 'publishedAt', 'isActive'
    ];

    const updates: any = {};
    for (const field of allowedUpdates) {
      if (body[field] !== undefined) {
        updates[field] = body[field];
      }
    }

    // 如果更新了名称，检查是否重复
    if (updates.name && updates.name !== tool.name) {
      const existingTool = await Tool.findOne({ 
        name: updates.name, 
        _id: { $ne: id } 
      });
      if (existingTool) {
        return NextResponse.json(
          { success: false, error: '该工具名称已存在' },
          { status: 400 }
        );
      }
    }

    // 执行更新
    const updatedTool = await Tool.findByIdAndUpdate(
      id,
      { $set: updates },
      { new: true, runValidators: true }
    ).select('-submittedBy -reviewNotes -reviewedBy');

    return NextResponse.json({
      success: true,
      data: updatedTool,
      message: '工具更新成功'
    });

  } catch (error) {
    console.error('Error updating tool:', error);
    
    if (error.name === 'ValidationError') {
      const validationErrors = Object.values(error.errors).map((err: any) => err.message);
      return NextResponse.json(
        { success: false, error: '验证失败', details: validationErrors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { success: false, error: '更新工具失败' },
      { status: 500 }
    );
  }
}

// DELETE /api/tools/[id] - 删除工具
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await dbConnect();

    const { id } = params;

    // 验证ID格式
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return NextResponse.json(
        { success: false, error: '无效的工具ID' },
        { status: 400 }
      );
    }

    // 查找并删除工具
    const tool = await Tool.findByIdAndDelete(id);
    if (!tool) {
      return NextResponse.json(
        { success: false, error: '工具不存在' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      message: '工具删除成功'
    });

  } catch (error) {
    console.error('Error deleting tool:', error);
    return NextResponse.json(
      { success: false, error: '删除工具失败' },
      { status: 500 }
    );
  }
}

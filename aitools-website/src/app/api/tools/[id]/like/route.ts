import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import dbConnect from '@/lib/mongodb';
import Tool from '@/models/Tool';
import User from '@/models/User';

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // 检查用户认证
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json(
        { success: false, message: '请先登录' },
        { status: 401 }
      );
    }

    await dbConnect();

    // 获取用户信息
    const user = await User.findOne({ email: session.user.email });
    if (!user) {
      return NextResponse.json(
        { success: false, message: '用户不存在' },
        { status: 404 }
      );
    }

    const toolId = params.id;

    // 查找工具
    const tool = await Tool.findById(toolId);
    if (!tool) {
      return NextResponse.json(
        { success: false, message: '工具不存在' },
        { status: 404 }
      );
    }

    // 检查用户是否已经点赞
    const hasLiked = tool.likedBy.includes(user._id.toString());

    if (hasLiked) {
      // 取消点赞
      tool.likedBy = tool.likedBy.filter((id: string) => id !== user._id.toString());
      tool.likes = Math.max(0, tool.likes - 1);
      
      // 从用户的点赞列表中移除
      user.likedTools = user.likedTools.filter((id: string) => id !== toolId);
    } else {
      // 添加点赞
      tool.likedBy.push(user._id.toString());
      tool.likes += 1;
      
      // 添加到用户的点赞列表
      if (!user.likedTools.includes(toolId)) {
        user.likedTools.push(toolId);
      }
    }

    // 保存更改
    await tool.save();
    await user.save();

    return NextResponse.json({
      success: true,
      data: {
        liked: !hasLiked,
        likes: tool.likes
      }
    });

  } catch (error) {
    console.error('Like tool error:', error);
    return NextResponse.json(
      { success: false, message: '服务器错误' },
      { status: 500 }
    );
  }
}

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    
    await dbConnect();

    const toolId = params.id;
    const tool = await Tool.findById(toolId);
    
    if (!tool) {
      return NextResponse.json(
        { success: false, message: '工具不存在' },
        { status: 404 }
      );
    }

    let liked = false;
    if (session?.user?.email) {
      const user = await User.findOne({ email: session.user.email });
      if (user) {
        liked = tool.likedBy.includes(user._id.toString());
      }
    }

    return NextResponse.json({
      success: true,
      data: {
        liked,
        likes: tool.likes
      }
    });

  } catch (error) {
    console.error('Get like status error:', error);
    return NextResponse.json(
      { success: false, message: '服务器错误' },
      { status: 500 }
    );
  }
}

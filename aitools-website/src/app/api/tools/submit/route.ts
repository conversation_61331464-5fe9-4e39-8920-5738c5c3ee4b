import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import dbConnect from '@/lib/mongodb';
import Tool from '@/models/Tool';
import User from '@/models/User';

export async function POST(request: NextRequest) {
  try {
    // 检查用户认证
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json(
        { success: false, message: '请先登录' },
        { status: 401 }
      );
    }

    await dbConnect();

    // 获取用户信息
    const user = await User.findOne({ email: session.user.email });
    if (!user) {
      return NextResponse.json(
        { success: false, message: '用户不存在' },
        { status: 404 }
      );
    }

    const {
      name,
      description,
      longDescription,
      website,
      logo,
      category,
      tags,
      pricing,
      pricingDetails,
      features,
      screenshots
    } = await request.json();

    // 验证必填字段
    if (!name || !description || !website || !category || !pricing) {
      return NextResponse.json(
        { success: false, message: '请填写所有必填字段' },
        { status: 400 }
      );
    }

    // 验证网站URL格式
    const urlRegex = /^https?:\/\/.+/;
    if (!urlRegex.test(website)) {
      return NextResponse.json(
        { success: false, message: '请输入有效的网站URL' },
        { status: 400 }
      );
    }

    // 验证分类
    const validCategories = [
      'text-generation',
      'image-generation', 
      'video-generation',
      'audio-generation',
      'code-generation',
      'data-analysis',
      'productivity',
      'design',
      'marketing',
      'education',
      'research',
      'other'
    ];

    if (!validCategories.includes(category)) {
      return NextResponse.json(
        { success: false, message: '无效的分类' },
        { status: 400 }
      );
    }

    // 验证定价模式
    const validPricing = ['free', 'freemium', 'paid'];
    if (!validPricing.includes(pricing)) {
      return NextResponse.json(
        { success: false, message: '无效的定价模式' },
        { status: 400 }
      );
    }

    // 检查工具名称是否已存在
    const existingTool = await Tool.findOne({ 
      name: { $regex: new RegExp(`^${name}$`, 'i') },
      isActive: true 
    });

    if (existingTool) {
      return NextResponse.json(
        { success: false, message: '该工具名称已存在' },
        { status: 409 }
      );
    }

    // 创建工具
    const tool = new Tool({
      name: name.trim(),
      description: description.trim(),
      longDescription: longDescription?.trim(),
      website: website.trim(),
      logo: logo?.trim(),
      category,
      tags: tags ? tags.map((tag: string) => tag.trim().toLowerCase()).filter(Boolean) : [],
      pricing,
      pricingDetails: pricingDetails?.trim(),
      features: features ? features.map((feature: string) => feature.trim()).filter(Boolean) : [],
      screenshots: screenshots ? screenshots.map((url: string) => url.trim()).filter(Boolean) : [],
      submittedBy: user._id.toString(),
      submittedAt: new Date(),
      status: 'pending', // 默认为待审核状态
      views: 0,
      likes: 0,
      likedBy: [],
      isActive: true
    });

    await tool.save();

    // 更新用户的提交工具列表
    if (!user.submittedTools.includes(tool._id.toString())) {
      user.submittedTools.push(tool._id.toString());
      await user.save();
    }

    return NextResponse.json({
      success: true,
      data: {
        toolId: tool._id,
        message: '工具提交成功，正在等待管理员审核'
      }
    }, { status: 201 });

  } catch (error) {
    console.error('Submit tool error:', error);
    return NextResponse.json(
      { success: false, message: '服务器错误' },
      { status: 500 }
    );
  }
}

// 获取用户提交的工具列表
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json(
        { success: false, message: '请先登录' },
        { status: 401 }
      );
    }

    await dbConnect();

    const user = await User.findOne({ email: session.user.email });
    if (!user) {
      return NextResponse.json(
        { success: false, message: '用户不存在' },
        { status: 404 }
      );
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const skip = (page - 1) * limit;

    // 获取用户提交的工具
    const tools = await Tool.find({ 
      submittedBy: user._id.toString(),
      isActive: true 
    })
    .sort({ submittedAt: -1 })
    .skip(skip)
    .limit(limit);

    const total = await Tool.countDocuments({ 
      submittedBy: user._id.toString(),
      isActive: true 
    });

    return NextResponse.json({
      success: true,
      data: {
        tools,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      }
    });

  } catch (error) {
    console.error('Get user tools error:', error);
    return NextResponse.json(
      { success: false, message: '服务器错误' },
      { status: 500 }
    );
  }
}

import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/mongodb';
import Tool from '@/models/Tool';

// GET /api/categories - 获取分类列表和统计
export async function GET(request: NextRequest) {
  try {
    await dbConnect();

    // 获取所有分类的工具数量统计
    const categoryStats = await Tool.aggregate([
      { $match: { status: 'approved' } },
      {
        $group: {
          _id: '$category',
          count: { $sum: 1 },
          totalViews: { $sum: '$views' },
          totalLikes: { $sum: '$likes' }
        }
      },
      { $sort: { count: -1 } }
    ]);

    // 分类标签映射
    const categoryLabels: Record<string, string> = {
      'text-generation': '文本生成',
      'image-generation': '图像生成',
      'code-generation': '代码生成',
      'data-analysis': '数据分析',
      'audio-processing': '音频处理',
      'video-editing': '视频编辑',
      'translation': '语言翻译',
      'search-engines': '搜索引擎',
      'education': '教育学习',
      'marketing': '营销工具',
      'productivity': '生产力工具',
      'customer-service': '客户服务'
    };

    // 构建完整的分类列表
    const categories = Object.keys(categoryLabels).map(key => {
      const stats = categoryStats.find(stat => stat._id === key);
      return {
        id: key,
        name: categoryLabels[key],
        count: stats?.count || 0,
        totalViews: stats?.totalViews || 0,
        totalLikes: stats?.totalLikes || 0
      };
    });

    // 按工具数量排序
    categories.sort((a, b) => b.count - a.count);

    // 获取总统计
    const totalStats = await Tool.aggregate([
      { $match: { status: 'approved' } },
      {
        $group: {
          _id: null,
          totalTools: { $sum: 1 },
          totalViews: { $sum: '$views' },
          totalLikes: { $sum: '$likes' }
        }
      }
    ]);

    const overview = totalStats[0] || {
      totalTools: 0,
      totalViews: 0,
      totalLikes: 0
    };

    return NextResponse.json({
      success: true,
      data: {
        categories,
        overview
      }
    });

  } catch (error) {
    console.error('Error fetching categories:', error);
    return NextResponse.json(
      { success: false, error: '获取分类列表失败' },
      { status: 500 }
    );
  }
}

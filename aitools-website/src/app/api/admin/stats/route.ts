import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/mongodb';
import Tool from '@/models/Tool';

// GET /api/admin/stats - 获取管理员统计数据
export async function GET(request: NextRequest) {
  try {
    await dbConnect();

    const { searchParams } = new URL(request.url);
    const timeRange = searchParams.get('timeRange') || '7d';

    // 计算时间范围
    const now = new Date();
    let startDate: Date;
    
    switch (timeRange) {
      case '1d':
        startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000);
        break;
      case '7d':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case '30d':
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        break;
      case '90d':
        startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
        break;
      default:
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    }

    // 基础统计
    const [
      totalTools,
      pendingTools,
      approvedTools,
      rejectedTools,
      totalViews,
      totalLikes,
      recentSubmissions,
      recentApprovals,
      recentRejections
    ] = await Promise.all([
      Tool.countDocuments(),
      Tool.countDocuments({ status: 'pending' }),
      Tool.countDocuments({ status: 'approved' }),
      Tool.countDocuments({ status: 'rejected' }),
      Tool.aggregate([{ $group: { _id: null, total: { $sum: '$views' } } }]),
      Tool.aggregate([{ $group: { _id: null, total: { $sum: '$likes' } } }]),
      Tool.countDocuments({ submittedAt: { $gte: startDate } }),
      Tool.countDocuments({ 
        status: 'approved', 
        reviewedAt: { $gte: startDate } 
      }),
      Tool.countDocuments({ 
        status: 'rejected', 
        reviewedAt: { $gte: startDate } 
      })
    ]);

    // 分类统计
    const categoryStats = await Tool.aggregate([
      { $match: { status: 'approved' } },
      {
        $group: {
          _id: '$category',
          count: { $sum: 1 },
          totalViews: { $sum: '$views' },
          totalLikes: { $sum: '$likes' }
        }
      },
      { $sort: { count: -1 } }
    ]);

    // 热门工具
    const topTools = await Tool.find({ status: 'approved' })
      .sort({ views: -1 })
      .limit(10)
      .select('name category views likes')
      .lean();

    // 最近活动
    const recentActivity = await Tool.find({
      $or: [
        { submittedAt: { $gte: startDate } },
        { reviewedAt: { $gte: startDate } }
      ]
    })
    .sort({ updatedAt: -1 })
    .limit(20)
    .select('name status submittedAt reviewedAt submittedBy reviewedBy')
    .lean();

    // 每日统计（最近7天）
    const dailyStats = await Tool.aggregate([
      {
        $match: {
          submittedAt: { $gte: new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000) }
        }
      },
      {
        $group: {
          _id: {
            date: { $dateToString: { format: '%Y-%m-%d', date: '$submittedAt' } },
            status: '$status'
          },
          count: { $sum: 1 }
        }
      },
      { $sort: { '_id.date': 1 } }
    ]);

    // 处理每日统计数据
    const last7Days = [];
    for (let i = 6; i >= 0; i--) {
      const date = new Date(now.getTime() - i * 24 * 60 * 60 * 1000);
      const dateStr = date.toISOString().split('T')[0];
      const dayName = date.toLocaleDateString('zh-CN', { weekday: 'short' });
      
      const dayStats = dailyStats.filter(stat => stat._id.date === dateStr);
      const submissions = dayStats.find(s => s._id.status === 'pending')?.count || 0;
      const approvals = dayStats.find(s => s._id.status === 'approved')?.count || 0;
      const rejections = dayStats.find(s => s._id.status === 'rejected')?.count || 0;
      
      last7Days.push({
        date: dateStr,
        day: dayName,
        submissions,
        approvals,
        rejections
      });
    }

    // 审核效率统计
    const avgReviewTime = await Tool.aggregate([
      {
        $match: {
          status: { $in: ['approved', 'rejected'] },
          reviewedAt: { $exists: true },
          submittedAt: { $exists: true }
        }
      },
      {
        $project: {
          reviewTime: {
            $divide: [
              { $subtract: ['$reviewedAt', '$submittedAt'] },
              1000 * 60 * 60 // 转换为小时
            ]
          }
        }
      },
      {
        $group: {
          _id: null,
          avgReviewTime: { $avg: '$reviewTime' }
        }
      }
    ]);

    const overview = {
      totalTools,
      pendingTools,
      approvedTools,
      rejectedTools,
      totalViews: totalViews[0]?.total || 0,
      totalLikes: totalLikes[0]?.total || 0,
      recentSubmissions,
      recentApprovals,
      recentRejections,
      avgReviewTime: avgReviewTime[0]?.avgReviewTime || 0
    };

    return NextResponse.json({
      success: true,
      data: {
        overview,
        categoryStats,
        topTools,
        recentActivity,
        dailyStats: last7Days,
        timeRange
      }
    });

  } catch (error) {
    console.error('Error fetching admin stats:', error);
    return NextResponse.json(
      { success: false, error: '获取统计数据失败' },
      { status: 500 }
    );
  }
}

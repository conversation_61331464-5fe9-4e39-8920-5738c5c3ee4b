'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import Layout from '@/components/Layout';
import { 
  Plus, 
  Edit, 
  Eye, 
  Clock, 
  CheckCircle, 
  XCircle, 
  Calendar,
  ExternalLink,
  BarChart3
} from 'lucide-react';

// Mock data - 在实际应用中这些数据会从 API 获取
const submittedTools = [
  {
    _id: '1',
    name: 'ChatGPT',
    description: 'Advanced AI chatbot for conversations and text generation',
    website: 'https://chat.openai.com',
    category: 'text-generation',
    status: 'approved',
    submittedAt: '2024-01-15',
    publishedAt: '2024-01-16',
    views: 1250,
    likes: 89
  },
  {
    _id: '2',
    name: 'AI Writing Assistant',
    description: 'Help users write better content with AI assistance',
    website: 'https://example.com',
    category: 'text-generation',
    status: 'pending',
    submittedAt: '2024-01-20',
    publishedAt: null,
    views: 0,
    likes: 0
  },
  {
    _id: '3',
    name: 'Image Generator Pro',
    description: 'Professional AI image generation tool',
    website: 'https://example.com',
    category: 'image-generation',
    status: 'rejected',
    submittedAt: '2024-01-18',
    publishedAt: null,
    views: 0,
    likes: 0,
    rejectionReason: '工具描述不够详细，请提供更多功能说明'
  }
];

const getStatusColor = (status: string) => {
  switch (status) {
    case 'approved':
      return 'bg-green-100 text-green-800';
    case 'pending':
      return 'bg-yellow-100 text-yellow-800';
    case 'rejected':
      return 'bg-red-100 text-red-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

const getStatusText = (status: string) => {
  switch (status) {
    case 'approved':
      return '已通过';
    case 'pending':
      return '审核中';
    case 'rejected':
      return '已拒绝';
    default:
      return status;
  }
};

const getStatusIcon = (status: string) => {
  switch (status) {
    case 'approved':
      return <CheckCircle className="h-4 w-4" />;
    case 'pending':
      return <Clock className="h-4 w-4" />;
    case 'rejected':
      return <XCircle className="h-4 w-4" />;
    default:
      return null;
  }
};

export default function DashboardPage() {
  const [selectedStatus, setSelectedStatus] = useState('all');

  const filteredTools = submittedTools.filter(tool => 
    selectedStatus === 'all' || tool.status === selectedStatus
  );

  const stats = {
    total: submittedTools.length,
    approved: submittedTools.filter(t => t.status === 'approved').length,
    pending: submittedTools.filter(t => t.status === 'pending').length,
    rejected: submittedTools.filter(t => t.status === 'rejected').length,
    totalViews: submittedTools.reduce((sum, t) => sum + t.views, 0),
    totalLikes: submittedTools.reduce((sum, t) => sum + t.likes, 0)
  };

  return (
    <Layout>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">开发者仪表板</h1>
            <p className="text-lg text-gray-600">管理您提交的 AI 工具</p>
          </div>
          <Link
            href="/submit"
            className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 transition-colors"
          >
            <Plus className="mr-2 h-5 w-5" />
            提交新工具
          </Link>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <BarChart3 className="h-8 w-8 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">总提交数</p>
                <p className="text-2xl font-bold text-gray-900">{stats.total}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <CheckCircle className="h-8 w-8 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">已通过</p>
                <p className="text-2xl font-bold text-gray-900">{stats.approved}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Eye className="h-8 w-8 text-purple-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">总浏览量</p>
                <p className="text-2xl font-bold text-gray-900">{stats.totalViews}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="h-8 w-8 text-red-600">❤️</div>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">总点赞数</p>
                <p className="text-2xl font-bold text-gray-900">{stats.totalLikes}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Filters */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
          <div className="flex flex-wrap gap-2">
            <button
              onClick={() => setSelectedStatus('all')}
              className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                selectedStatus === 'all'
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              全部 ({stats.total})
            </button>
            <button
              onClick={() => setSelectedStatus('approved')}
              className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                selectedStatus === 'approved'
                  ? 'bg-green-600 text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              已通过 ({stats.approved})
            </button>
            <button
              onClick={() => setSelectedStatus('pending')}
              className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                selectedStatus === 'pending'
                  ? 'bg-yellow-600 text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              审核中 ({stats.pending})
            </button>
            <button
              onClick={() => setSelectedStatus('rejected')}
              className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                selectedStatus === 'rejected'
                  ? 'bg-red-600 text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              已拒绝 ({stats.rejected})
            </button>
          </div>
        </div>

        {/* Tools List */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          {filteredTools.length > 0 ? (
            <div className="divide-y divide-gray-200">
              {filteredTools.map((tool) => (
                <div key={tool._id} className="p-6">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-2">
                        <h3 className="text-lg font-semibold text-gray-900">
                          {tool.name}
                        </h3>
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(tool.status)}`}>
                          {getStatusIcon(tool.status)}
                          <span className="ml-1">{getStatusText(tool.status)}</span>
                        </span>
                      </div>
                      
                      <p className="text-gray-600 mb-3 line-clamp-2">
                        {tool.description}
                      </p>
                      
                      <div className="flex items-center space-x-6 text-sm text-gray-500">
                        <div className="flex items-center space-x-1">
                          <Calendar className="h-4 w-4" />
                          <span>提交于 {tool.submittedAt}</span>
                        </div>
                        {tool.publishedAt && (
                          <div className="flex items-center space-x-1">
                            <CheckCircle className="h-4 w-4" />
                            <span>发布于 {tool.publishedAt}</span>
                          </div>
                        )}
                        {tool.status === 'approved' && (
                          <>
                            <div className="flex items-center space-x-1">
                              <Eye className="h-4 w-4" />
                              <span>{tool.views} 浏览</span>
                            </div>
                            <div className="flex items-center space-x-1">
                              <span>❤️</span>
                              <span>{tool.likes} 点赞</span>
                            </div>
                          </>
                        )}
                      </div>

                      {tool.status === 'rejected' && tool.rejectionReason && (
                        <div className="mt-3 p-3 bg-red-50 border border-red-200 rounded-lg">
                          <p className="text-sm text-red-800">
                            <strong>拒绝原因：</strong> {tool.rejectionReason}
                          </p>
                        </div>
                      )}
                    </div>
                    
                    <div className="flex items-center space-x-2 ml-4">
                      {tool.status === 'approved' && (
                        <Link
                          href={`/tools/${tool._id}`}
                          className="p-2 text-gray-400 hover:text-blue-600 transition-colors"
                          title="查看详情"
                        >
                          <Eye className="h-5 w-5" />
                        </Link>
                      )}
                      <a
                        href={tool.website}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="p-2 text-gray-400 hover:text-green-600 transition-colors"
                        title="访问网站"
                      >
                        <ExternalLink className="h-5 w-5" />
                      </a>
                      {(tool.status === 'rejected' || tool.status === 'pending') && (
                        <button
                          className="p-2 text-gray-400 hover:text-blue-600 transition-colors"
                          title="编辑"
                        >
                          <Edit className="h-5 w-5" />
                        </button>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <div className="text-gray-400 mb-4">
                <BarChart3 className="h-12 w-12 mx-auto" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                {selectedStatus === 'all' ? '还没有提交任何工具' : `没有${getStatusText(selectedStatus)}的工具`}
              </h3>
              <p className="text-gray-600 mb-4">
                {selectedStatus === 'all' 
                  ? '开始提交您的第一个 AI 工具吧！'
                  : '尝试选择其他状态查看工具'
                }
              </p>
              {selectedStatus === 'all' && (
                <Link
                  href="/submit"
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 transition-colors"
                >
                  <Plus className="mr-2 h-4 w-4" />
                  提交工具
                </Link>
              )}
            </div>
          )}
        </div>
      </div>
    </Layout>
  );
}

'use client';

import { useSession, signIn, signOut } from 'next-auth/react';
import { useState } from 'react';

export default function TestAuth() {
  const { data: session, status } = useSession();
  const [email, setEmail] = useState('');
  const [code, setCode] = useState('');
  const [token, setToken] = useState('');
  const [message, setMessage] = useState('');

  const sendCode = async () => {
    try {
      const response = await fetch('/api/auth/send-code', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ email }),
      });
      const data = await response.json();
      if (data.success) {
        setToken(data.token);
        setMessage('验证码已发送！Token: ' + data.token);
      } else {
        setMessage('发送失败: ' + data.error);
      }
    } catch (error) {
      setMessage('网络错误');
    }
  };

  const verifyCode = async () => {
    try {
      const result = await signIn('email-code', {
        email,
        code,
        token,
        redirect: false,
      });
      
      if (result?.ok) {
        setMessage('登录成功！');
      } else {
        setMessage('登录失败: ' + result?.error);
      }
    } catch (error) {
      setMessage('验证失败');
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4">
      <div className="max-w-md mx-auto bg-white rounded-lg shadow-md p-6">
        <h1 className="text-2xl font-bold mb-6">认证测试页面</h1>
        
        <div className="mb-6">
          <h2 className="text-lg font-semibold mb-2">当前状态</h2>
          <p>Status: {status}</p>
          {session ? (
            <div>
              <p>已登录用户: {session.user?.email}</p>
              <p>用户名: {session.user?.name}</p>
              <p>用户ID: {(session.user as any)?.id}</p>
              <p>用户角色: {(session.user as any)?.role}</p>
              <button 
                onClick={() => signOut()}
                className="mt-2 bg-red-500 text-white px-4 py-2 rounded"
              >
                退出登录
              </button>
            </div>
          ) : (
            <p>未登录</p>
          )}
        </div>

        <div className="mb-6">
          <h2 className="text-lg font-semibold mb-2">邮件验证码登录测试</h2>
          
          <div className="mb-4">
            <label className="block text-sm font-medium mb-1">邮箱</label>
            <input
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="w-full border rounded px-3 py-2"
              placeholder="输入邮箱"
            />
          </div>

          <button
            onClick={sendCode}
            className="w-full bg-blue-500 text-white py-2 rounded mb-4"
          >
            发送验证码
          </button>

          <div className="mb-4">
            <label className="block text-sm font-medium mb-1">验证码</label>
            <input
              type="text"
              value={code}
              onChange={(e) => setCode(e.target.value)}
              className="w-full border rounded px-3 py-2"
              placeholder="输入6位验证码"
            />
          </div>

          <div className="mb-4">
            <label className="block text-sm font-medium mb-1">Token</label>
            <input
              type="text"
              value={token}
              onChange={(e) => setToken(e.target.value)}
              className="w-full border rounded px-3 py-2"
              placeholder="自动填充"
            />
          </div>

          <button
            onClick={verifyCode}
            className="w-full bg-green-500 text-white py-2 rounded mb-4"
          >
            验证登录
          </button>

          {message && (
            <div className="p-3 bg-gray-100 rounded text-sm">
              {message}
            </div>
          )}
        </div>

        <div className="mb-6">
          <h2 className="text-lg font-semibold mb-2">OAuth登录测试</h2>
          <button
            onClick={() => signIn('google')}
            className="w-full bg-red-500 text-white py-2 rounded mb-2"
          >
            Google登录
          </button>
          <button
            onClick={() => signIn('github')}
            className="w-full bg-gray-800 text-white py-2 rounded"
          >
            GitHub登录
          </button>
        </div>
      </div>
    </div>
  );
}

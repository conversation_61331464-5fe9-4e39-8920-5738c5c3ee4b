'use client';

import React, { useState } from 'react';
import Layout from '@/components/Layout';
import { 
  BarChart3,
  TrendingUp,
  Users,
  Clock,
  CheckCircle,
  XCircle,
  Eye,
  Heart,
  Calendar,
  Activity,
  AlertCircle,
  Star
} from 'lucide-react';

// Mock data for admin dashboard
const mockStats = {
  overview: {
    totalTools: 156,
    pendingReview: 12,
    approvedToday: 8,
    rejectedToday: 2,
    totalUsers: 2847,
    activeUsers: 1234,
    totalViews: 45678,
    totalLikes: 8901
  },
  recentActivity: [
    {
      id: '1',
      type: 'submission',
      message: '张三 提交了新工具 "AI写作助手"',
      timestamp: '2024-06-26T10:30:00Z',
      status: 'pending'
    },
    {
      id: '2',
      type: 'approval',
      message: '管理员批准了工具 "CodeAI"',
      timestamp: '2024-06-26T09:15:00Z',
      status: 'approved'
    },
    {
      id: '3',
      type: 'rejection',
      message: '管理员拒绝了工具 "FakeAI" - 功能描述不准确',
      timestamp: '2024-06-26T08:45:00Z',
      status: 'rejected'
    },
    {
      id: '4',
      type: 'submission',
      message: '李四 提交了新工具 "ImageGen Pro"',
      timestamp: '2024-06-25T16:20:00Z',
      status: 'pending'
    },
    {
      id: '5',
      type: 'approval',
      message: '管理员批准了工具 "DataAnalyzer"',
      timestamp: '2024-06-25T14:10:00Z',
      status: 'approved'
    }
  ],
  topTools: [
    {
      id: '1',
      name: 'ChatGPT',
      category: 'text-generation',
      views: 12456,
      likes: 2341,
      status: 'approved'
    },
    {
      id: '2',
      name: 'Midjourney',
      category: 'image-generation',
      views: 9876,
      likes: 1987,
      status: 'approved'
    },
    {
      id: '3',
      name: 'GitHub Copilot',
      category: 'code-generation',
      views: 8765,
      likes: 1654,
      status: 'approved'
    },
    {
      id: '4',
      name: 'Notion AI',
      category: 'productivity',
      views: 7654,
      likes: 1432,
      status: 'approved'
    },
    {
      id: '5',
      name: 'Grammarly',
      category: 'text-generation',
      views: 6543,
      likes: 1234,
      status: 'approved'
    }
  ],
  weeklyStats: [
    { day: '周一', submissions: 5, approvals: 3, rejections: 1 },
    { day: '周二', submissions: 8, approvals: 6, rejections: 2 },
    { day: '周三', submissions: 6, approvals: 4, rejections: 1 },
    { day: '周四', submissions: 9, approvals: 7, rejections: 2 },
    { day: '周五', submissions: 7, approvals: 5, rejections: 1 },
    { day: '周六', submissions: 3, approvals: 2, rejections: 0 },
    { day: '周日', submissions: 4, approvals: 3, rejections: 1 }
  ]
};

const categoryLabels: Record<string, string> = {
  'text-generation': '文本生成',
  'image-generation': '图像生成',
  'code-generation': '代码生成',
  'data-analysis': '数据分析',
  'audio-processing': '音频处理',
  'video-editing': '视频编辑',
  'translation': '语言翻译',
  'search-engines': '搜索引擎',
  'education': '教育学习',
  'marketing': '营销工具',
  'productivity': '生产力工具',
  'customer-service': '客户服务'
};

export default function AdminDashboardPage() {
  const [timeRange, setTimeRange] = useState('7d');

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'submission':
        return <Clock className="w-4 h-4 text-blue-600" />;
      case 'approval':
        return <CheckCircle className="w-4 h-4 text-green-600" />;
      case 'rejection':
        return <XCircle className="w-4 h-4 text-red-600" />;
      default:
        return <Activity className="w-4 h-4 text-gray-600" />;
    }
  };

  const getActivityBgColor = (type: string) => {
    switch (type) {
      case 'submission':
        return 'bg-blue-50 border-blue-200';
      case 'approval':
        return 'bg-green-50 border-green-200';
      case 'rejection':
        return 'bg-red-50 border-red-200';
      default:
        return 'bg-gray-50 border-gray-200';
    }
  };

  return (
    <Layout>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 flex items-center">
                <BarChart3 className="mr-3 h-8 w-8 text-blue-600" />
                管理员统计面板
              </h1>
              <p className="mt-2 text-lg text-gray-600">
                查看网站运营数据和审核统计
              </p>
            </div>
            
            <div className="flex items-center space-x-3">
              <select
                value={timeRange}
                onChange={(e) => setTimeRange(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="1d">今天</option>
                <option value="7d">最近7天</option>
                <option value="30d">最近30天</option>
                <option value="90d">最近90天</option>
              </select>
            </div>
          </div>
        </div>

        {/* Overview Stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">总工具数</p>
                <p className="text-2xl font-bold text-gray-900">{mockStats.overview.totalTools}</p>
              </div>
              <div className="p-3 bg-blue-100 rounded-lg">
                <BarChart3 className="w-6 h-6 text-blue-600" />
              </div>
            </div>
            <div className="mt-4 flex items-center text-sm">
              <TrendingUp className="w-4 h-4 text-green-500 mr-1" />
              <span className="text-green-600">+12%</span>
              <span className="text-gray-500 ml-1">vs 上周</span>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">待审核</p>
                <p className="text-2xl font-bold text-yellow-600">{mockStats.overview.pendingReview}</p>
              </div>
              <div className="p-3 bg-yellow-100 rounded-lg">
                <Clock className="w-6 h-6 text-yellow-600" />
              </div>
            </div>
            <div className="mt-4 flex items-center text-sm">
              <AlertCircle className="w-4 h-4 text-yellow-500 mr-1" />
              <span className="text-yellow-600">需要关注</span>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">活跃用户</p>
                <p className="text-2xl font-bold text-gray-900">{mockStats.overview.activeUsers.toLocaleString()}</p>
              </div>
              <div className="p-3 bg-green-100 rounded-lg">
                <Users className="w-6 h-6 text-green-600" />
              </div>
            </div>
            <div className="mt-4 flex items-center text-sm">
              <TrendingUp className="w-4 h-4 text-green-500 mr-1" />
              <span className="text-green-600">+8%</span>
              <span className="text-gray-500 ml-1">vs 上周</span>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">总浏览量</p>
                <p className="text-2xl font-bold text-gray-900">{mockStats.overview.totalViews.toLocaleString()}</p>
              </div>
              <div className="p-3 bg-purple-100 rounded-lg">
                <Eye className="w-6 h-6 text-purple-600" />
              </div>
            </div>
            <div className="mt-4 flex items-center text-sm">
              <TrendingUp className="w-4 h-4 text-green-500 mr-1" />
              <span className="text-green-600">+15%</span>
              <span className="text-gray-500 ml-1">vs 上周</span>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          {/* Weekly Stats Chart */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">本周审核统计</h3>
            <div className="space-y-4">
              {mockStats.weeklyStats.map((stat, index) => (
                <div key={index} className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-600 w-12">{stat.day}</span>
                  <div className="flex-1 mx-4">
                    <div className="flex space-x-1">
                      <div 
                        className="bg-blue-200 h-6 rounded flex items-center justify-center text-xs text-blue-800"
                        style={{ width: `${(stat.submissions / 10) * 100}%`, minWidth: '20px' }}
                      >
                        {stat.submissions}
                      </div>
                      <div 
                        className="bg-green-200 h-6 rounded flex items-center justify-center text-xs text-green-800"
                        style={{ width: `${(stat.approvals / 10) * 100}%`, minWidth: '20px' }}
                      >
                        {stat.approvals}
                      </div>
                      <div 
                        className="bg-red-200 h-6 rounded flex items-center justify-center text-xs text-red-800"
                        style={{ width: `${(stat.rejections / 10) * 100}%`, minWidth: '20px' }}
                      >
                        {stat.rejections}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
            <div className="flex items-center justify-center space-x-6 mt-6 text-sm">
              <div className="flex items-center">
                <div className="w-3 h-3 bg-blue-200 rounded mr-2"></div>
                <span className="text-gray-600">提交</span>
              </div>
              <div className="flex items-center">
                <div className="w-3 h-3 bg-green-200 rounded mr-2"></div>
                <span className="text-gray-600">批准</span>
              </div>
              <div className="flex items-center">
                <div className="w-3 h-3 bg-red-200 rounded mr-2"></div>
                <span className="text-gray-600">拒绝</span>
              </div>
            </div>
          </div>

          {/* Top Tools */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">热门工具</h3>
            <div className="space-y-4">
              {mockStats.topTools.map((tool, index) => (
                <div key={tool.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <div className="flex items-center justify-center w-8 h-8 bg-blue-100 text-blue-600 rounded-full text-sm font-bold">
                      {index + 1}
                    </div>
                    <div>
                      <div className="font-medium text-gray-900">{tool.name}</div>
                      <div className="text-sm text-gray-500">{categoryLabels[tool.category]}</div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-4 text-sm text-gray-600">
                    <div className="flex items-center">
                      <Eye className="w-4 h-4 mr-1" />
                      {tool.views.toLocaleString()}
                    </div>
                    <div className="flex items-center">
                      <Heart className="w-4 h-4 mr-1" />
                      {tool.likes.toLocaleString()}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Recent Activity */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">最近活动</h3>
          <div className="space-y-4">
            {mockStats.recentActivity.map((activity) => (
              <div 
                key={activity.id} 
                className={`flex items-start space-x-3 p-4 rounded-lg border ${getActivityBgColor(activity.type)}`}
              >
                <div className="flex-shrink-0 mt-0.5">
                  {getActivityIcon(activity.type)}
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm text-gray-900">{activity.message}</p>
                  <p className="text-xs text-gray-500 mt-1">{formatDate(activity.timestamp)}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </Layout>
  );
}

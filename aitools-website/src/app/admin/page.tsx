'use client';

import React, { useState, useEffect } from 'react';
import Layout from '@/components/Layout';
import LoadingSpinner from '@/components/LoadingSpinner';
import ErrorMessage from '@/components/ErrorMessage';
import SuccessMessage from '@/components/SuccessMessage';
import { apiClient, Tool } from '@/lib/api';
import {
  Shield,
  Clock,
  CheckCircle,
  XCircle,
  Eye,
  Search,
  Filter,
  MoreHorizontal,
  Calendar,
  User,
  ExternalLink,
  AlertTriangle
} from 'lucide-react';

// Mock data for pending tools
const mockPendingTools = [
  {
    id: '1',
    name: 'AI写作助手',
    description: '基于GPT技术的智能写作工具，支持多种文体创作，提供实时语法检查和内容优化建议。',
    website: 'https://aiwriter.example.com',
    logo: 'https://via.placeholder.com/64',
    category: 'text-generation',
    pricing: 'freemium',
    tags: ['写作', 'GPT', '内容创作'],
    features: ['智能写作', '语法检查', '多语言支持'],
    submitterName: '张三',
    submitterEmail: 'zhang<PERSON>@example.com',
    submittedAt: '2024-06-25T10:30:00Z',
    status: 'pending',
    publishDate: '2024-06-28'
  },
  {
    id: '2',
    name: 'CodeAI',
    description: '专业的AI代码生成工具，支持多种编程语言，提供代码补全、bug修复和性能优化建议。',
    website: 'https://codeai.example.com',
    logo: 'https://via.placeholder.com/64',
    category: 'code-generation',
    pricing: 'paid',
    tags: ['编程', '代码生成', '开发工具'],
    features: ['代码补全', 'Bug修复', '性能优化'],
    submitterName: '李四',
    submitterEmail: '<EMAIL>',
    submittedAt: '2024-06-24T15:45:00Z',
    status: 'pending',
    publishDate: null
  },
  {
    id: '3',
    name: 'ImageGen Pro',
    description: '高质量AI图像生成工具，支持多种艺术风格，提供批量生成和高分辨率输出功能。',
    website: 'https://imagegen.example.com',
    logo: 'https://via.placeholder.com/64',
    category: 'image-generation',
    pricing: 'freemium',
    tags: ['图像生成', 'AI艺术', '设计'],
    features: ['多种风格', '批量生成', '高分辨率'],
    submitterName: '王五',
    submitterEmail: '<EMAIL>',
    submittedAt: '2024-06-23T09:20:00Z',
    status: 'pending',
    publishDate: '2024-06-30'
  }
];

const categoryLabels: Record<string, string> = {
  'text-generation': '文本生成',
  'image-generation': '图像生成',
  'code-generation': '代码生成',
  'data-analysis': '数据分析',
  'audio-processing': '音频处理',
  'video-editing': '视频编辑',
  'translation': '语言翻译',
  'search-engines': '搜索引擎',
  'education': '教育学习',
  'marketing': '营销工具',
  'productivity': '生产力工具',
  'customer-service': '客户服务'
};

const pricingLabels: Record<string, string> = {
  'free': '免费',
  'freemium': '免费增值',
  'paid': '付费'
};

export default function AdminPage() {
  const [tools, setTools] = useState<Tool[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [successMessage, setSuccessMessage] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [selectedTool, setSelectedTool] = useState<string | null>(null);
  const [showRejectModal, setShowRejectModal] = useState(false);
  const [rejectReason, setRejectReason] = useState('');
  const [actionLoading, setActionLoading] = useState(false);

  useEffect(() => {
    fetchTools();
  }, [statusFilter]);

  const fetchTools = async () => {
    try {
      setLoading(true);
      setError('');

      const response = await apiClient.getAdminTools({
        status: statusFilter === 'all' ? undefined : statusFilter,
        limit: 50
      });

      if (response.success && response.data) {
        setTools(response.data.tools);
      } else {
        setError(response.error || '获取工具列表失败');
      }
    } catch (err) {
      setError('网络错误，请重试');
    } finally {
      setLoading(false);
    }
  };

  const filteredTools = tools.filter(tool => {
    const matchesSearch = tool.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         tool.description.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesStatus = statusFilter === 'all' || tool.status === statusFilter;

    return matchesSearch && matchesStatus;
  });

  const handleApprove = async (toolId: string) => {
    try {
      setActionLoading(true);
      setError('');

      const response = await apiClient.approveTool(toolId, {
        reviewedBy: 'admin', // 在实际应用中应该是当前登录的管理员
        reviewNotes: '审核通过',
        publishedAt: new Date().toISOString()
      });

      if (response.success) {
        setSuccessMessage('工具审核通过！');
        await fetchTools(); // 重新获取数据
      } else {
        setError(response.error || '审核操作失败');
      }
    } catch (err) {
      setError('网络错误，请重试');
    } finally {
      setActionLoading(false);
    }
  };

  const handleReject = (toolId: string, reason: string) => {
    setTools(prev => prev.map(tool => 
      tool.id === toolId 
        ? { ...tool, status: 'rejected', rejectedAt: new Date().toISOString(), rejectReason: reason }
        : tool
    ));
    setShowRejectModal(false);
    setRejectReason('');
    setSelectedTool(null);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
            <Clock className="w-3 h-3 mr-1" />
            待审核
          </span>
        );
      case 'approved':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
            <CheckCircle className="w-3 h-3 mr-1" />
            已批准
          </span>
        );
      case 'rejected':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
            <XCircle className="w-3 h-3 mr-1" />
            已拒绝
          </span>
        );
      default:
        return null;
    }
  };

  return (
    <Layout>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 flex items-center">
                <Shield className="mr-3 h-8 w-8 text-blue-600" />
                管理员审核中心
              </h1>
              <p className="mt-2 text-lg text-gray-600">
                审核和管理用户提交的 AI 工具
              </p>
            </div>
            
            {/* Quick Stats */}
            <div className="flex space-x-6">
              <div className="text-center">
                <div className="text-2xl font-bold text-yellow-600">
                  {tools.filter(t => t.status === 'pending').length}
                </div>
                <div className="text-sm text-gray-500">待审核</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">
                  {tools.filter(t => t.status === 'approved').length}
                </div>
                <div className="text-sm text-gray-500">已批准</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-red-600">
                  {tools.filter(t => t.status === 'rejected').length}
                </div>
                <div className="text-sm text-gray-500">已拒绝</div>
              </div>
            </div>
          </div>
        </div>

        {/* Filters and Search */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
          <div className="flex flex-col sm:flex-row gap-4">
            {/* Search */}
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-2.5 h-5 w-5 text-gray-400" />
                <input
                  type="text"
                  placeholder="搜索工具名称、描述或提交者..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>

            {/* Status Filter */}
            <div className="sm:w-48">
              <div className="relative">
                <Filter className="absolute left-3 top-2.5 h-5 w-5 text-gray-400" />
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent appearance-none"
                >
                  <option value="all">所有状态</option>
                  <option value="pending">待审核</option>
                  <option value="approved">已批准</option>
                  <option value="rejected">已拒绝</option>
                </select>
              </div>
            </div>
          </div>
        </div>

        {/* Tools List */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          {filteredTools.length === 0 ? (
            <div className="text-center py-12">
              <AlertTriangle className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">没有找到工具</h3>
              <p className="mt-1 text-sm text-gray-500">
                {searchTerm || statusFilter !== 'all' ? '尝试调整搜索条件或筛选器' : '暂无待审核的工具'}
              </p>
            </div>
          ) : (
            <div className="divide-y divide-gray-200">
              {filteredTools.map((tool) => (
                <div key={tool.id} className="p-6 hover:bg-gray-50 transition-colors">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-start space-x-4">
                        {/* Logo */}
                        <div className="flex-shrink-0">
                          <img
                            src={tool.logo}
                            alt={tool.name}
                            className="w-12 h-12 rounded-lg object-cover border border-gray-200"
                          />
                        </div>

                        {/* Tool Info */}
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center space-x-3 mb-2">
                            <h3 className="text-lg font-semibold text-gray-900 truncate">
                              {tool.name}
                            </h3>
                            {getStatusBadge(tool.status)}
                            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                              {categoryLabels[tool.category]}
                            </span>
                            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                              {pricingLabels[tool.pricing]}
                            </span>
                          </div>

                          <p className="text-gray-600 text-sm mb-3 line-clamp-2">
                            {tool.description}
                          </p>

                          <div className="flex items-center space-x-6 text-sm text-gray-500">
                            <div className="flex items-center">
                              <User className="w-4 h-4 mr-1" />
                              {tool.submitterName}
                            </div>
                            <div className="flex items-center">
                              <Calendar className="w-4 h-4 mr-1" />
                              {formatDate(tool.submittedAt)}
                            </div>
                            {tool.publishDate && (
                              <div className="flex items-center">
                                <Clock className="w-4 h-4 mr-1" />
                                期望发布: {new Date(tool.publishDate).toLocaleDateString('zh-CN')}
                              </div>
                            )}
                          </div>

                          {/* Tags */}
                          {tool.tags.length > 0 && (
                            <div className="flex flex-wrap gap-1 mt-3">
                              {tool.tags.map((tag, index) => (
                                <span
                                  key={index}
                                  className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-gray-100 text-gray-700"
                                >
                                  {tag}
                                </span>
                              ))}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>

                    {/* Actions */}
                    <div className="flex items-center space-x-2 ml-4">
                      <a
                        href={tool.website}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
                        title="访问网站"
                      >
                        <ExternalLink className="w-4 h-4" />
                      </a>
                      
                      <button
                        className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
                        title="查看详情"
                      >
                        <Eye className="w-4 h-4" />
                      </button>

                      {tool.status === 'pending' && (
                        <>
                          <button
                            onClick={() => handleApprove(tool.id)}
                            className="px-3 py-1 bg-green-600 text-white text-sm rounded-md hover:bg-green-700 transition-colors"
                          >
                            批准
                          </button>
                          <button
                            onClick={() => {
                              setSelectedTool(tool.id);
                              setShowRejectModal(true);
                            }}
                            className="px-3 py-1 bg-red-600 text-white text-sm rounded-md hover:bg-red-700 transition-colors"
                          >
                            拒绝
                          </button>
                        </>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Reject Modal */}
        {showRejectModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
            <div className="bg-white rounded-lg max-w-md w-full p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">拒绝工具</h3>
              <p className="text-sm text-gray-600 mb-4">
                请说明拒绝的原因，这将帮助提交者改进他们的提交。
              </p>
              <textarea
                value={rejectReason}
                onChange={(e) => setRejectReason(e.target.value)}
                rows={4}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="请输入拒绝原因..."
              />
              <div className="flex justify-end space-x-3 mt-6">
                <button
                  onClick={() => {
                    setShowRejectModal(false);
                    setRejectReason('');
                    setSelectedTool(null);
                  }}
                  className="px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
                >
                  取消
                </button>
                <button
                  onClick={() => selectedTool && handleReject(selectedTool, rejectReason)}
                  disabled={!rejectReason.trim()}
                  className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
                >
                  确认拒绝
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </Layout>
  );
}

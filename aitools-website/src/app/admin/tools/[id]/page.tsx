'use client';

import React, { useState } from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import Layout from '@/components/Layout';
import { 
  ArrowLeft,
  ExternalLink,
  Calendar,
  User,
  Mail,
  Tag,
  Star,
  CheckCircle,
  XCircle,
  Clock,
  AlertTriangle,
  Globe,
  DollarSign
} from 'lucide-react';

// Mock data - in real app this would come from API
const mockTool = {
  id: '1',
  name: 'AI写作助手',
  description: '基于GPT技术的智能写作工具，支持多种文体创作，提供实时语法检查和内容优化建议。这是一个功能强大的AI写作助手，能够帮助用户快速生成高质量的文章、报告、邮件等各种文本内容。工具采用最新的自然语言处理技术，具有强大的语言理解和生成能力。',
  website: 'https://aiwriter.example.com',
  logo: 'https://via.placeholder.com/128',
  category: 'text-generation',
  pricing: 'freemium',
  tags: ['写作', 'GPT', '内容创作', '语法检查', '文本生成'],
  features: [
    '智能写作：基于AI的内容生成',
    '语法检查：实时检测和修正语法错误',
    '多语言支持：支持中文、英文等多种语言',
    '模板库：提供丰富的写作模板',
    '协作功能：支持团队协作编辑',
    '导出功能：支持多种格式导出'
  ],
  submitterName: '张三',
  submitterEmail: '<EMAIL>',
  submittedAt: '2024-06-25T10:30:00Z',
  status: 'pending',
  publishDate: '2024-06-28',
  screenshots: [
    'https://via.placeholder.com/600x400',
    'https://via.placeholder.com/600x400',
    'https://via.placeholder.com/600x400'
  ]
};

const categoryLabels: Record<string, string> = {
  'text-generation': '文本生成',
  'image-generation': '图像生成',
  'code-generation': '代码生成',
  'data-analysis': '数据分析',
  'audio-processing': '音频处理',
  'video-editing': '视频编辑',
  'translation': '语言翻译',
  'search-engines': '搜索引擎',
  'education': '教育学习',
  'marketing': '营销工具',
  'productivity': '生产力工具',
  'customer-service': '客户服务'
};

const pricingLabels: Record<string, string> = {
  'free': '免费',
  'freemium': '免费增值',
  'paid': '付费'
};

export default function AdminToolDetailPage() {
  const params = useParams();
  const router = useRouter();
  const [tool] = useState(mockTool);
  const [showRejectModal, setShowRejectModal] = useState(false);
  const [rejectReason, setRejectReason] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);

  const handleApprove = async () => {
    setIsProcessing(true);
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));
      alert('工具已批准！');
      router.push('/admin');
    } catch (error) {
      alert('操作失败，请重试');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleReject = async () => {
    if (!rejectReason.trim()) return;
    
    setIsProcessing(true);
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));
      alert('工具已拒绝！');
      router.push('/admin');
    } catch (error) {
      alert('操作失败，请重试');
    } finally {
      setIsProcessing(false);
      setShowRejectModal(false);
      setRejectReason('');
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return (
          <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-yellow-100 text-yellow-800">
            <Clock className="w-4 h-4 mr-2" />
            待审核
          </span>
        );
      case 'approved':
        return (
          <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
            <CheckCircle className="w-4 h-4 mr-2" />
            已批准
          </span>
        );
      case 'rejected':
        return (
          <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-red-100 text-red-800">
            <XCircle className="w-4 h-4 mr-2" />
            已拒绝
          </span>
        );
      default:
        return null;
    }
  };

  return (
    <Layout>
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <button
            onClick={() => router.back()}
            className="flex items-center text-gray-600 hover:text-gray-900 mb-4 transition-colors"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            返回审核列表
          </button>
          
          <div className="flex items-start justify-between">
            <div className="flex items-start space-x-6">
              <img
                src={tool.logo}
                alt={tool.name}
                className="w-24 h-24 rounded-xl object-cover border border-gray-200 shadow-sm"
              />
              <div>
                <div className="flex items-center space-x-3 mb-2">
                  <h1 className="text-3xl font-bold text-gray-900">{tool.name}</h1>
                  {getStatusBadge(tool.status)}
                </div>
                <div className="flex items-center space-x-4 text-sm text-gray-600 mb-4">
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                    {categoryLabels[tool.category]}
                  </span>
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                    <DollarSign className="w-3 h-3 mr-1" />
                    {pricingLabels[tool.pricing]}
                  </span>
                  <a
                    href={tool.website}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-flex items-center text-blue-600 hover:text-blue-800 transition-colors"
                  >
                    <Globe className="w-4 h-4 mr-1" />
                    访问网站
                    <ExternalLink className="w-3 h-3 ml-1" />
                  </a>
                </div>
                <p className="text-gray-600 max-w-3xl">{tool.description}</p>
              </div>
            </div>

            {/* Action Buttons */}
            {tool.status === 'pending' && (
              <div className="flex space-x-3">
                <button
                  onClick={handleApprove}
                  disabled={isProcessing}
                  className="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors flex items-center"
                >
                  <CheckCircle className="w-4 h-4 mr-2" />
                  {isProcessing ? '处理中...' : '批准'}
                </button>
                <button
                  onClick={() => setShowRejectModal(true)}
                  disabled={isProcessing}
                  className="px-6 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors flex items-center"
                >
                  <XCircle className="w-4 h-4 mr-2" />
                  拒绝
                </button>
              </div>
            )}
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-8">
            {/* Features */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">主要功能</h2>
              <ul className="space-y-3">
                {tool.features.map((feature, index) => (
                  <li key={index} className="flex items-start">
                    <Star className="w-5 h-5 text-blue-600 mr-3 mt-0.5 flex-shrink-0" />
                    <span className="text-gray-700">{feature}</span>
                  </li>
                ))}
              </ul>
            </div>

            {/* Tags */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">标签</h2>
              <div className="flex flex-wrap gap-2">
                {tool.tags.map((tag, index) => (
                  <span
                    key={index}
                    className="inline-flex items-center px-3 py-1 rounded-md text-sm font-medium bg-blue-100 text-blue-800"
                  >
                    <Tag className="w-3 h-3 mr-1" />
                    {tag}
                  </span>
                ))}
              </div>
            </div>

            {/* Screenshots */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">截图预览</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {tool.screenshots.map((screenshot, index) => (
                  <img
                    key={index}
                    src={screenshot}
                    alt={`${tool.name} 截图 ${index + 1}`}
                    className="w-full h-48 object-cover rounded-lg border border-gray-200"
                  />
                ))}
              </div>
            </div>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Submission Info */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">提交信息</h3>
              <div className="space-y-4">
                <div className="flex items-center">
                  <User className="w-5 h-5 text-gray-400 mr-3" />
                  <div>
                    <div className="text-sm font-medium text-gray-900">{tool.submitterName}</div>
                    <div className="text-sm text-gray-500">提交者</div>
                  </div>
                </div>
                
                <div className="flex items-center">
                  <Mail className="w-5 h-5 text-gray-400 mr-3" />
                  <div>
                    <div className="text-sm font-medium text-gray-900">{tool.submitterEmail}</div>
                    <div className="text-sm text-gray-500">联系邮箱</div>
                  </div>
                </div>
                
                <div className="flex items-center">
                  <Calendar className="w-5 h-5 text-gray-400 mr-3" />
                  <div>
                    <div className="text-sm font-medium text-gray-900">{formatDate(tool.submittedAt)}</div>
                    <div className="text-sm text-gray-500">提交时间</div>
                  </div>
                </div>
                
                {tool.publishDate && (
                  <div className="flex items-center">
                    <Clock className="w-5 h-5 text-gray-400 mr-3" />
                    <div>
                      <div className="text-sm font-medium text-gray-900">
                        {new Date(tool.publishDate).toLocaleDateString('zh-CN')}
                      </div>
                      <div className="text-sm text-gray-500">期望发布日期</div>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Review Guidelines */}
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
              <div className="flex items-start">
                <AlertTriangle className="w-5 h-5 text-blue-600 mr-3 mt-0.5 flex-shrink-0" />
                <div>
                  <h3 className="text-sm font-medium text-blue-900 mb-2">审核指南</h3>
                  <ul className="text-sm text-blue-800 space-y-1">
                    <li>• 验证工具网站是否可正常访问</li>
                    <li>• 检查工具描述是否准确客观</li>
                    <li>• 确认分类和标签是否合适</li>
                    <li>• 评估工具质量和实用性</li>
                    <li>• 检查是否存在重复提交</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Reject Modal */}
        {showRejectModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
            <div className="bg-white rounded-lg max-w-md w-full p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">拒绝工具</h3>
              <p className="text-sm text-gray-600 mb-4">
                请详细说明拒绝的原因，这将帮助提交者了解问题并改进他们的提交。
              </p>
              <textarea
                value={rejectReason}
                onChange={(e) => setRejectReason(e.target.value)}
                rows={4}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="请输入详细的拒绝原因..."
              />
              <div className="flex justify-end space-x-3 mt-6">
                <button
                  onClick={() => {
                    setShowRejectModal(false);
                    setRejectReason('');
                  }}
                  disabled={isProcessing}
                  className="px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
                >
                  取消
                </button>
                <button
                  onClick={handleReject}
                  disabled={!rejectReason.trim() || isProcessing}
                  className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
                >
                  {isProcessing ? '处理中...' : '确认拒绝'}
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </Layout>
  );
}

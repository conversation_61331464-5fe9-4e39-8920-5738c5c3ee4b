import { NextAuthOptions } from 'next-auth';
import GoogleProvider from 'next-auth/providers/google';
import GitHubProvider from 'next-auth/providers/github';
import EmailProvider from 'next-auth/providers/email';
import { MongoDBAdapter } from '@auth/mongodb-adapter';
import { MongoClient } from 'mongodb';
import dbConnect from './mongodb';
import User from '../models/User';
import nodemailer from 'nodemailer';

// MongoDB客户端
const client = new MongoClient(process.env.MONGODB_URI!);

// 邮件传输器
const emailTransporter = nodemailer.createTransport({
  host: process.env.EMAIL_HOST,
  port: parseInt(process.env.EMAIL_PORT || '587'),
  secure: false,
  auth: {
    user: process.env.EMAIL_USER,
    pass: process.env.EMAIL_PASS,
  },
});

export const authOptions: NextAuthOptions = {
  adapter: MongoDBAdapter(client),
  providers: [
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
    }),
    GitHubProvider({
      clientId: process.env.GITHUB_CLIENT_ID!,
      clientSecret: process.env.GITHUB_CLIENT_SECRET!,
    }),
    EmailProvider({
      server: {
        host: process.env.EMAIL_HOST,
        port: parseInt(process.env.EMAIL_PORT || '587'),
        auth: {
          user: process.env.EMAIL_USER,
          pass: process.env.EMAIL_PASS,
        },
      },
      from: process.env.EMAIL_FROM,
      sendVerificationRequest: async ({ identifier: email, url, provider }) => {
        const { host } = new URL(url);
        
        await emailTransporter.sendMail({
          to: email,
          from: provider.from,
          subject: `登录 ${host}`,
          text: `登录链接: ${url}`,
          html: `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
              <h2 style="color: #333;">登录 AI Tools Directory</h2>
              <p>点击下面的按钮登录您的账户：</p>
              <a href="${url}" style="display: inline-block; background-color: #3B82F6; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 16px 0;">
                登录账户
              </a>
              <p style="color: #666; font-size: 14px;">
                如果您没有请求此邮件，请忽略它。此链接将在24小时后过期。
              </p>
              <p style="color: #666; font-size: 12px;">
                如果按钮无法点击，请复制以下链接到浏览器：<br>
                ${url}
              </p>
            </div>
          `,
        });
      },
    }),
  ],
  session: {
    strategy: 'jwt',
  },
  callbacks: {
    async signIn({ user, account, profile }) {
      await dbConnect();
      
      try {
        // 查找或创建用户
        let existingUser = await User.findOne({ email: user.email });
        
        if (!existingUser) {
          // 创建新用户
          existingUser = new User({
            email: user.email,
            name: user.name || profile?.name || 'User',
            avatar: user.image || profile?.image,
            emailVerified: true, // OAuth登录默认已验证
            lastLoginAt: new Date(),
          });
          await existingUser.save();
        } else {
          // 更新最后登录时间
          existingUser.lastLoginAt = new Date();
          await existingUser.save();
        }
        
        // 添加或更新账户信息
        if (account && account.provider !== 'email') {
          existingUser.addAccount({
            provider: account.provider as 'google' | 'github',
            providerId: account.provider,
            providerAccountId: account.providerAccountId || account.id || '',
            accessToken: account.access_token,
            refreshToken: account.refresh_token,
            expiresAt: account.expires_at ? new Date(account.expires_at * 1000) : undefined,
          });
          await existingUser.save();
        }
        
        return true;
      } catch (error) {
        console.error('Sign in error:', error);
        return false;
      }
    },
    async jwt({ token, user, account }) {
      if (user) {
        await dbConnect();
        const dbUser = await User.findOne({ email: user.email });
        if (dbUser) {
          token.userId = dbUser._id.toString();
          token.role = dbUser.role;
        }
      }
      return token;
    },
    async session({ session, token }) {
      if (token) {
        session.user.id = token.userId as string;
        session.user.role = token.role as string;
      }
      return session;
    },
  },
  pages: {
    signIn: '/auth/signin',
    error: '/auth/error',
  },
  secret: process.env.NEXTAUTH_SECRET,
};

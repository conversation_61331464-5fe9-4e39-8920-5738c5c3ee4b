import { NextAuthOptions } from 'next-auth';
import GoogleProvider from 'next-auth/providers/google';
import GitHubProvider from 'next-auth/providers/github';
import EmailProvider from 'next-auth/providers/email';
import CredentialsProvider from 'next-auth/providers/credentials';
import { MongoDBAdapter } from '@auth/mongodb-adapter';
import { MongoClient } from 'mongodb';
import dbConnect from './mongodb';
import User from '../models/User';
import nodemailer from 'nodemailer';
import jwt from 'jsonwebtoken';

// MongoDB客户端
const client = new MongoClient(process.env.MONGODB_URI!);

// 邮件传输器
const emailTransporter = nodemailer.createTransport({
  host: process.env.EMAIL_HOST,
  port: parseInt(process.env.EMAIL_PORT || '587'),
  secure: false,
  auth: {
    user: process.env.EMAIL_USER,
    pass: process.env.EMAIL_PASS,
  },
});

console.log('Loading NextAuth configuration...');

export const authOptions: NextAuthOptions = {
  // 注意：Credentials provider与adapter不兼容，所以我们使用JWT策略
  // adapter: MongoDBAdapter(client),
  providers: [
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
    }),
    GitHubProvider({
      clientId: process.env.GITHUB_CLIENT_ID!,
      clientSecret: process.env.GITHUB_CLIENT_SECRET!,
    }),
    CredentialsProvider({
      id: 'credentials',
      name: 'Credentials',
      credentials: {
        username: { label: 'Username', type: 'text' },
        password: { label: 'Password', type: 'password' }
      },
      async authorize(credentials) {
        console.log('=== AUTHORIZE FUNCTION CALLED ===');
        console.log('Credentials received:', JSON.stringify(credentials, null, 2));

        // 简单测试 - 总是返回一个固定用户
        return {
          id: '123',
          email: '<EMAIL>',
          name: 'Test User',
          image: undefined,
          role: 'user'
        };
      }
    }),
  ],
  session: {
    strategy: 'jwt',
  },
  callbacks: {
    async signIn({ user, account, profile }) {
      await dbConnect();
      
      try {
        // 查找或创建用户
        let existingUser = await User.findOne({ email: user.email });
        
        if (!existingUser) {
          // 创建新用户
          existingUser = new User({
            email: user.email,
            name: user.name || profile?.name || 'User',
            avatar: user.image || profile?.image,
            emailVerified: true, // OAuth登录默认已验证
            lastLoginAt: new Date(),
          });
          await existingUser.save();
        } else {
          // 更新最后登录时间
          existingUser.lastLoginAt = new Date();
          await existingUser.save();
        }
        
        // 添加或更新账户信息
        if (account && account.provider !== 'email') {
          existingUser.addAccount({
            provider: account.provider as 'google' | 'github',
            providerId: account.provider,
            providerAccountId: account.providerAccountId || account.id || '',
            accessToken: account.access_token,
            refreshToken: account.refresh_token,
            expiresAt: account.expires_at ? new Date(account.expires_at * 1000) : undefined,
          });
          await existingUser.save();
        }
        
        return true;
      } catch (error) {
        console.error('Sign in error:', error);
        return false;
      }
    },
    async jwt({ token, user }) {
      console.log('JWT callback called with user:', user);
      if (user) {
        // 对于Credentials provider，直接使用返回的用户信息
        if (user.id && user.role) {
          token.userId = user.id;
          token.role = user.role;
        } else {
          // 对于OAuth providers，查找数据库
          await dbConnect();
          const dbUser = await User.findOne({ email: user.email });
          if (dbUser) {
            token.userId = dbUser._id.toString();
            token.role = dbUser.role;
          }
        }
      }
      console.log('JWT token:', token);
      return token;
    },
    async session({ session, token }) {
      if (token && session.user) {
        session.user.id = token.userId as string;
        session.user.role = token.role as string;
      }
      return session;
    },
  },
  pages: {
    signIn: '/auth/signin',
    error: '/auth/error',
  },
  secret: process.env.NEXTAUTH_SECRET,
};

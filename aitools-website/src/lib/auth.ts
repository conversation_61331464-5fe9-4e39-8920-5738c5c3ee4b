import { NextAuthOptions } from 'next-auth';
import GoogleProvider from 'next-auth/providers/google';
import GitHubProvider from 'next-auth/providers/github';
import CredentialsProvider from 'next-auth/providers/credentials';
import dbConnect from './mongodb';
import User from '../models/User';

export const authOptions: NextAuthOptions = {
  // 注意：Credentials provider与adapter不兼容，所以我们使用JWT策略
  // adapter: MongoDBAdapter(client),
  providers: [
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
    }),
    GitHubProvider({
      clientId: process.env.GITHUB_CLIENT_ID!,
      clientSecret: process.env.GITHUB_CLIENT_SECRET!,
    }),
    CredentialsProvider({
      id: 'email-code',
      name: '<PERSON><PERSON> Code',
      credentials: {
        email: { label: 'Email', type: 'email' },
        code: { label: 'Code', type: 'text' },
        token: { label: 'Token', type: 'text' },
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.code || !credentials?.token) {
          return null;
        }

        try {
          await dbConnect();

          // 查找用户
          const user = await User.findOne({
            email: credentials.email.toLowerCase(),
            emailVerificationExpires: { $gt: new Date() }
          });

          if (!user) {
            return null;
          }

          // 验证token和验证码
          const storedData = user.emailVerificationToken;
          if (!storedData || !storedData.includes(':')) {
            return null;
          }

          const [storedToken, storedCode] = storedData.split(':');

          if (storedToken !== credentials.token || storedCode !== credentials.code) {
            return null;
          }

          // 验证成功，更新用户状态
          user.emailVerified = true;
          user.emailVerificationToken = undefined;
          user.emailVerificationExpires = undefined;
          user.lastLoginAt = new Date();

          // 如果用户没有邮箱账户记录，添加一个
          const hasEmailAccount = user.accounts.some((acc: any) => acc.provider === 'email');
          if (!hasEmailAccount) {
            user.accounts.push({
              provider: 'email',
              providerId: 'email',
              providerAccountId: user.email,
            });
          }

          await user.save();

          return {
            id: user._id.toString(),
            email: user.email,
            name: user.name,
            image: user.avatar,
            role: user.role,
          };
        } catch (error) {
          console.error('Email code authorization error:', error);
          return null;
        }
      },
    }),
  ],
  session: {
    strategy: 'jwt',
  },
  callbacks: {
    async signIn({ user, account, profile }) {
      // 对于credentials provider，用户已经在authorize中处理过了
      if (account?.provider === 'email-code') {
        return true;
      }

      await dbConnect();

      try {
        // 查找或创建用户（仅用于OAuth providers）
        let existingUser = await User.findOne({ email: user.email });

        if (!existingUser) {
          // 创建新用户
          existingUser = new User({
            email: user.email,
            name: user.name || profile?.name || 'User',
            avatar: user.image || profile?.image,
            emailVerified: true, // OAuth登录默认已验证
            lastLoginAt: new Date(),
          });
          await existingUser.save();
        } else {
          // 更新最后登录时间
          existingUser.lastLoginAt = new Date();
          await existingUser.save();
        }

        // 添加或更新账户信息
        if (account && account.provider !== 'email-code') {
          existingUser.addAccount({
            provider: account.provider as 'google' | 'github',
            providerId: account.provider,
            providerAccountId: account.providerAccountId || account.id || '',
            accessToken: account.access_token,
            refreshToken: account.refresh_token,
            expiresAt: account.expires_at ? new Date(account.expires_at * 1000) : undefined,
          });
          await existingUser.save();
        }

        return true;
      } catch (error) {
        console.error('Sign in error:', error);
        return false;
      }
    },
    async jwt({ token, user }) {
      if (user) {
        // 对于credentials provider，user对象已经包含了我们需要的信息
        token.userId = user.id;
        token.role = (user as any).role || 'user';
      }
      return token;
    },
    async session({ session, token }) {
      if (token && session.user) {
        (session.user as any).id = token.userId as string;
        (session.user as any).role = token.role as string;
      }
      return session;
    },
  },
  pages: {
    signIn: '/auth/signin',
    error: '/auth/error',
  },
  secret: process.env.NEXTAUTH_SECRET,
};

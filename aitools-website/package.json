{"name": "aitools-website", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack --port 3001", "build": "next build", "start": "next start", "lint": "next lint", "seed": "node -r ts-node/register scripts/seed.js"}, "dependencies": {"@auth/mongodb-adapter": "^3.10.0", "@types/bcryptjs": "^3.0.0", "@types/crypto-js": "^4.2.2", "@types/jsonwebtoken": "^9.0.10", "@types/mongoose": "^5.11.97", "@types/multer": "^1.4.13", "@types/nodemailer": "^6.4.17", "bcryptjs": "^3.0.2", "crypto-js": "^4.2.0", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.523.0", "mongoose": "^8.16.0", "multer": "^2.0.1", "next": "15.3.4", "next-auth": "^4.24.11", "nodemailer": "^6.10.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-icons": "^5.5.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.4", "tailwindcss": "^4", "ts-node": "^10.9.2", "typescript": "^5"}}
{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/Layout.tsx"], "sourcesContent": ["import React from 'react';\nimport Link from 'next/link';\n\ninterface LayoutProps {\n  children: React.ReactNode;\n}\n\nconst Layout: React.FC<LayoutProps> = ({ children }) => {\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n\n      {/* Main Content */}\n      <main className=\"flex-1\">\n        {children}\n      </main>\n\n      {/* Footer */}\n      <footer className=\"bg-white border-t border-gray-200 mt-16\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n          <div className=\"grid grid-cols-1 md:grid-cols-4 gap-8\">\n            <div className=\"col-span-1 md:col-span-2\">\n              <div className=\"flex items-center space-x-2 mb-4\">\n                <div className=\"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center\">\n                  <span className=\"text-white font-bold text-sm\">AI</span>\n                </div>\n                <span className=\"text-xl font-bold text-gray-900\">AI Tools</span>\n              </div>\n              <p className=\"text-gray-600 mb-4\">\n                发现最新最好的 AI 工具，提升您的工作效率和创造力。\n              </p>\n            </div>\n            \n            <div>\n              <h3 className=\"text-sm font-semibold text-gray-900 tracking-wider uppercase mb-4\">\n                快速链接\n              </h3>\n              <ul className=\"space-y-2\">\n                <li>\n                  <Link href=\"/tools\" className=\"text-gray-600 hover:text-blue-600\">\n                    工具目录\n                  </Link>\n                </li>\n                <li>\n                  <Link href=\"/categories\" className=\"text-gray-600 hover:text-blue-600\">\n                    分类浏览\n                  </Link>\n                </li>\n                <li>\n                  <Link href=\"/submit\" className=\"text-gray-600 hover:text-blue-600\">\n                    提交工具\n                  </Link>\n                </li>\n              </ul>\n            </div>\n            \n            <div>\n              <h3 className=\"text-sm font-semibold text-gray-900 tracking-wider uppercase mb-4\">\n                支持\n              </h3>\n              <ul className=\"space-y-2\">\n                <li>\n                  <a href=\"#\" className=\"text-gray-600 hover:text-blue-600\">\n                    帮助中心\n                  </a>\n                </li>\n                <li>\n                  <a href=\"#\" className=\"text-gray-600 hover:text-blue-600\">\n                    联系我们\n                  </a>\n                </li>\n                <li>\n                  <a href=\"#\" className=\"text-gray-600 hover:text-blue-600\">\n                    隐私政策\n                  </a>\n                </li>\n              </ul>\n            </div>\n          </div>\n          \n          <div className=\"border-t border-gray-200 mt-8 pt-8\">\n            <p className=\"text-center text-gray-600\">\n              © 2024 AI Tools Directory. All rights reserved.\n            </p>\n          </div>\n        </div>\n      </footer>\n    </div>\n  );\n};\n\nexport default Layout;\n"], "names": [], "mappings": ";;;;AACA;;;AAMA,MAAM,SAAgC,CAAC,EAAE,QAAQ,EAAE;IACjD,qBACE,8OAAC;QAAI,WAAU;;0BAGb,8OAAC;gBAAK,WAAU;0BACb;;;;;;0BAIH,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAK,WAAU;kEAA+B;;;;;;;;;;;8DAEjD,8OAAC;oDAAK,WAAU;8DAAkC;;;;;;;;;;;;sDAEpD,8OAAC;4CAAE,WAAU;sDAAqB;;;;;;;;;;;;8CAKpC,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAoE;;;;;;sDAGlF,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;8DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAS,WAAU;kEAAoC;;;;;;;;;;;8DAIpE,8OAAC;8DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAc,WAAU;kEAAoC;;;;;;;;;;;8DAIzE,8OAAC;8DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAU,WAAU;kEAAoC;;;;;;;;;;;;;;;;;;;;;;;8CAOzE,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAoE;;;;;;sDAGlF,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;8DACC,cAAA,8OAAC;wDAAE,MAAK;wDAAI,WAAU;kEAAoC;;;;;;;;;;;8DAI5D,8OAAC;8DACC,cAAA,8OAAC;wDAAE,MAAK;wDAAI,WAAU;kEAAoC;;;;;;;;;;;8DAI5D,8OAAC;8DACC,cAAA,8OAAC;wDAAE,MAAK;wDAAI,WAAU;kEAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQlE,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;0CAA4B;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQrD;uCAEe", "debugId": null}}, {"offset": {"line": 269, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/LoadingSpinner.tsx"], "sourcesContent": ["'use client';\n\ninterface LoadingSpinnerProps {\n  size?: 'sm' | 'md' | 'lg';\n  className?: string;\n}\n\nexport default function LoadingSpinner({ size = 'md', className = '' }: LoadingSpinnerProps) {\n  const sizeClasses = {\n    sm: 'w-4 h-4',\n    md: 'w-8 h-8',\n    lg: 'w-12 h-12'\n  };\n\n  return (\n    <div className={`flex justify-center items-center ${className}`}>\n      <div className={`${sizeClasses[size]} border-4 border-gray-200 border-t-blue-600 rounded-full animate-spin`}></div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAOe,SAAS,eAAe,EAAE,OAAO,IAAI,EAAE,YAAY,EAAE,EAAuB;IACzF,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,iCAAiC,EAAE,WAAW;kBAC7D,cAAA,8OAAC;YAAI,WAAW,GAAG,WAAW,CAAC,KAAK,CAAC,qEAAqE,CAAC;;;;;;;;;;;AAGjH", "debugId": null}}, {"offset": {"line": 302, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/ErrorMessage.tsx"], "sourcesContent": ["'use client';\n\nimport { AlertCircle, X } from 'lucide-react';\n\ninterface ErrorMessageProps {\n  message: string;\n  onClose?: () => void;\n  className?: string;\n}\n\nexport default function ErrorMessage({ message, onClose, className = '' }: ErrorMessageProps) {\n  return (\n    <div className={`bg-red-50 border border-red-200 rounded-lg p-4 ${className}`}>\n      <div className=\"flex items-start\">\n        <AlertCircle className=\"w-5 h-5 text-red-500 mt-0.5 mr-3 flex-shrink-0\" />\n        <div className=\"flex-1\">\n          <p className=\"text-red-800 text-sm\">{message}</p>\n        </div>\n        {onClose && (\n          <button\n            onClick={onClose}\n            className=\"ml-3 text-red-400 hover:text-red-600 transition-colors\"\n          >\n            <X className=\"w-4 h-4\" />\n          </button>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAFA;;;AAUe,SAAS,aAAa,EAAE,OAAO,EAAE,OAAO,EAAE,YAAY,EAAE,EAAqB;IAC1F,qBACE,8OAAC;QAAI,WAAW,CAAC,+CAA+C,EAAE,WAAW;kBAC3E,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,oNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;8BACvB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;gBAEtC,yBACC,8OAAC;oBACC,SAAS;oBACT,WAAU;8BAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;wBAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;AAMzB", "debugId": null}}, {"offset": {"line": 372, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/SuccessMessage.tsx"], "sourcesContent": ["'use client';\n\nimport { CheckCircle, X } from 'lucide-react';\n\ninterface SuccessMessageProps {\n  message: string;\n  onClose?: () => void;\n  className?: string;\n}\n\nexport default function SuccessMessage({ message, onClose, className = '' }: SuccessMessageProps) {\n  return (\n    <div className={`bg-green-50 border border-green-200 rounded-lg p-4 ${className}`}>\n      <div className=\"flex items-start\">\n        <CheckCircle className=\"w-5 h-5 text-green-500 mt-0.5 mr-3 flex-shrink-0\" />\n        <div className=\"flex-1\">\n          <p className=\"text-green-800 text-sm\">{message}</p>\n        </div>\n        {onClose && (\n          <button\n            onClick={onClose}\n            className=\"ml-3 text-green-400 hover:text-green-600 transition-colors\"\n          >\n            <X className=\"w-4 h-4\" />\n          </button>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAFA;;;AAUe,SAAS,eAAe,EAAE,OAAO,EAAE,OAAO,EAAE,YAAY,EAAE,EAAuB;IAC9F,qBACE,8OAAC;QAAI,WAAW,CAAC,mDAAmD,EAAE,WAAW;kBAC/E,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,2NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;8BACvB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAE,WAAU;kCAA0B;;;;;;;;;;;gBAExC,yBACC,8OAAC;oBACC,SAAS;oBACT,WAAU;8BAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;wBAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;AAMzB", "debugId": null}}, {"offset": {"line": 442, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/constants/tags.ts"], "sourcesContent": ["// AI工具应用的预定义标签列表 - 基于主流AI工具网站分析和市场调研\nexport const AVAILABLE_TAGS = [\n  // 核心AI功能 - 基础AI能力\n  'AI助手', 'ChatGPT', '对话AI', '智能问答', '语言模型', 'GPT', 'Claude', 'Gemini',\n  'AI聊天机器人', '虚拟助手', '智能客服', 'AI代理', 'AI工作流', '多模态AI',\n\n  // 内容创作 - 文本内容生成\n  '写作助手', '内容生成', '文案创作', '博客写作', '邮件写作', '创意写作',\n  '学术写作', '技术写作', '营销文案', '社交媒体文案', '新闻写作', '小说创作',\n  '诗歌创作', '剧本写作', '简历生成', '求职信', '产品描述', '广告文案',\n\n  // 图像处理 - 图像生成与编辑\n  '图像生成', '图像编辑', '图像增强', '头像生成', '艺术创作', '照片修复',\n  '背景移除', '图像压缩', '图像识别', '图像分析', 'AI绘画', '风格转换',\n  '图像超分辨率', '图像去噪', '人像美化', '产品图片', '插画生成', 'NFT艺术',\n  '漫画生成', '素描转换', '图像标注', '图像分类', '物体检测', '人脸识别',\n\n  // 视频处理 - 视频生成与编辑\n  '视频生成', '视频编辑', '视频剪辑', '视频压缩', '视频转换', '动画制作',\n  '视频字幕', '视频特效', '直播工具', '短视频制作', '视频配音', '视频翻译',\n  '人物换脸', '虚拟主播', '动作捕捉', '视频稳定', '慢动作', '延时摄影',\n  '视频转GIF', '视频水印', '视频合并', '视频分割',\n\n  // 音频处理 - 音频生成与编辑\n  '语音合成', '语音识别', '音频编辑', '音乐生成', '播客工具', '语音转文字',\n  '文字转语音', '音频增强', '降噪', '音频转录', '语音克隆', '音效生成',\n  '背景音乐', '音频混合', '音频压缩', '音频格式转换', '语音翻译', '歌词生成',\n  '音频分离', '人声提取', '伴奏分离', '音频修复',\n\n  // 代码开发 - 编程与开发\n  '代码生成', '代码补全', '代码审查', '调试工具', '代码优化', '自动化测试',\n  'API工具', '开发助手', '代码转换', '代码解释', '代码重构', '文档生成',\n  '低代码平台', '无代码平台', 'GitHub助手', '代码搜索', '漏洞检测', '性能优化',\n  '数据库设计', '架构设计', '部署工具', 'DevOps', 'CI/CD', '容器化',\n\n  // 数据分析 - 数据处理与分析\n  '数据分析', '数据可视化', '统计分析', '预测分析', '商业智能', '报表生成',\n  '数据挖掘', '机器学习', '深度学习', '数据清洗', '数据建模', '数据监控',\n  '实时分析', '用户行为分析', '销售分析', '财务分析', '风险分析', '趋势预测',\n  '异常检测', '推荐系统', 'A/B测试', '数据仪表板',\n\n  // 办公效率 - 办公自动化\n  '办公自动化', '文档处理', '表格处理', '演示文稿', '项目管理', '时间管理',\n  '任务管理', '团队协作', '会议工具', '笔记工具', '日程安排', '邮件管理',\n  '文件同步', '云存储', '在线协作', '工作流程', '审批流程', '考勤管理',\n  '客户关系管理', '人力资源', '财务管理', '库存管理',\n\n  // 设计工具 - 设计与创意\n  'UI设计', 'UX设计', '平面设计', 'Logo设计', '网页设计', '原型设计',\n  '色彩搭配', '字体工具', '图标设计', '设计素材', '品牌设计', '包装设计',\n  '海报设计', '名片设计', '传单设计', '横幅设计', '3D设计', '建筑设计',\n  '室内设计', '服装设计', '珠宝设计', '工业设计',\n\n  // 营销工具 - 数字营销\n  'SEO优化', '社交媒体营销', '邮件营销', '内容营销', '广告优化', '市场分析',\n  '竞品分析', '用户分析', '转化优化', '关键词研究', '链接建设', '网站分析',\n  '社交媒体管理', '影响者营销', '联盟营销', '付费广告', '营销自动化', '客户获取',\n  '品牌监控', '声誉管理', '营销策略', '销售漏斗',\n\n  // 教育学习 - 在线教育\n  '在线教育', '语言学习', '技能培训', '考试辅导', '学习助手', '知识管理',\n  '记忆训练', '阅读理解', '数学辅导', '编程教学', '科学实验', '历史学习',\n  '地理学习', '艺术教育', '音乐教育', '体育训练', '职业培训', '认证考试',\n  '学习计划', '进度跟踪', '个性化学习', '智能题库',\n\n  // 翻译工具 - 语言翻译\n  '机器翻译', '实时翻译', '文档翻译', '网页翻译', '语言检测', '语音翻译',\n  '图片翻译', '视频翻译', '字幕翻译', '多语言支持', '本地化', '术语管理',\n  '翻译记忆', '质量评估', '翻译校对', '语言学习',\n\n  // 搜索工具 - 智能搜索\n  '智能搜索', '学术搜索', '图片搜索', '代码搜索', '文档搜索', '语义搜索',\n  '企业搜索', '电商搜索', '新闻搜索', '专利搜索', '法律搜索', '医学搜索',\n  '搜索引擎优化', '搜索分析', '搜索推荐', '个性化搜索',\n\n  // 健康医疗 - 医疗健康\n  '医疗诊断', '健康监测', '药物发现', '医学影像', '基因分析', '心理健康',\n  '营养分析', '运动健康', '睡眠分析', '疾病预测', '康复训练', '远程医疗',\n  '医疗记录', '症状检查', '用药提醒', '健康咨询',\n\n  // 金融科技 - 金融服务\n  '投资分析', '风险评估', '信用评分', '欺诈检测', '算法交易', '财务规划',\n  '保险科技', '支付处理', '区块链', '加密货币', '智能投顾', '财务报告',\n  '税务计算', '预算管理', '贷款审批', '合规监管',\n\n  // 法律服务 - 法律科技\n  '法律研究', '合同分析', '法律咨询', '案例检索', '法规监控', '合规检查',\n  '知识产权', '专利分析', '法律文档', '诉讼支持', '法律翻译', '风险评估',\n\n  // 电商零售 - 电子商务\n  '产品推荐', '价格优化', '库存管理', '客户服务', '评论分析', '趋势预测',\n  '供应链优化', '个性化营销', '欺诈防护', '物流优化', '用户画像', '转化优化',\n\n  // 游戏娱乐 - 游戏与娱乐\n  '游戏开发', '游戏AI', '角色生成', '关卡设计', '游戏测试', '虚拟现实',\n  '增强现实', '游戏直播', '电竞分析', '娱乐推荐', '内容审核', '社交游戏',\n\n  // 安全防护 - 网络安全\n  '网络安全', '威胁检测', '恶意软件检测', '入侵防护', '数据加密', '身份验证',\n  '访问控制', '安全审计', '漏洞扫描', '安全监控', '事件响应', '隐私保护',\n\n  // 其他实用工具 - 通用工具\n  '文本处理', '格式转换', '二维码生成', '密码管理', '文件管理', '截图工具',\n  '录屏工具', '浏览器插件', '移动应用', '桌面应用', '系统优化', '数据恢复',\n  '文件压缩', '文件同步', '远程控制', '虚拟机', '网络工具', '系统监控'\n];\n\n// 标签的最大选择数量\nexport const MAX_TAGS_COUNT = 3;\n\n// 按分类组织的标签（用于更好的用户体验）\nexport const TAGS_BY_CATEGORY = {\n  '核心AI功能': ['AI助手', 'ChatGPT', '对话AI', '智能问答', '语言模型', 'GPT', 'Claude', 'Gemini', 'AI聊天机器人', '虚拟助手', '智能客服', 'AI代理', 'AI工作流', '多模态AI'],\n  '内容创作': ['写作助手', '内容生成', '文案创作', '博客写作', '邮件写作', '创意写作', '学术写作', '技术写作', '营销文案', '社交媒体文案', '新闻写作', '小说创作', '诗歌创作', '剧本写作', '简历生成', '求职信', '产品描述', '广告文案'],\n  '图像处理': ['图像生成', '图像编辑', '图像增强', '头像生成', '艺术创作', '照片修复', '背景移除', '图像压缩', '图像识别', '图像分析', 'AI绘画', '风格转换', '图像超分辨率', '图像去噪', '人像美化', '产品图片', '插画生成', 'NFT艺术', '漫画生成', '素描转换', '图像标注', '图像分类', '物体检测', '人脸识别'],\n  '视频处理': ['视频生成', '视频编辑', '视频剪辑', '视频压缩', '视频转换', '动画制作', '视频字幕', '视频特效', '直播工具', '短视频制作', '视频配音', '视频翻译', '人物换脸', '虚拟主播', '动作捕捉', '视频稳定', '慢动作', '延时摄影', '视频转GIF', '视频水印', '视频合并', '视频分割'],\n  '音频处理': ['语音合成', '语音识别', '音频编辑', '音乐生成', '播客工具', '语音转文字', '文字转语音', '音频增强', '降噪', '音频转录', '语音克隆', '音效生成', '背景音乐', '音频混合', '音频压缩', '音频格式转换', '语音翻译', '歌词生成', '音频分离', '人声提取', '伴奏分离', '音频修复'],\n  '代码开发': ['代码生成', '代码补全', '代码审查', '调试工具', '代码优化', '自动化测试', 'API工具', '开发助手', '代码转换', '代码解释', '代码重构', '文档生成', '低代码平台', '无代码平台', 'GitHub助手', '代码搜索', '漏洞检测', '性能优化', '数据库设计', '架构设计', '部署工具', 'DevOps', 'CI/CD', '容器化'],\n  '数据分析': ['数据分析', '数据可视化', '统计分析', '预测分析', '商业智能', '报表生成', '数据挖掘', '机器学习', '深度学习', '数据清洗', '数据建模', '数据监控', '实时分析', '用户行为分析', '销售分析', '财务分析', '风险分析', '趋势预测', '异常检测', '推荐系统', 'A/B测试', '数据仪表板'],\n  '办公效率': ['办公自动化', '文档处理', '表格处理', '演示文稿', '项目管理', '时间管理', '任务管理', '团队协作', '会议工具', '笔记工具', '日程安排', '邮件管理', '文件同步', '云存储', '在线协作', '工作流程', '审批流程', '考勤管理', '客户关系管理', '人力资源', '财务管理', '库存管理'],\n  '设计工具': ['UI设计', 'UX设计', '平面设计', 'Logo设计', '网页设计', '原型设计', '色彩搭配', '字体工具', '图标设计', '设计素材', '品牌设计', '包装设计', '海报设计', '名片设计', '传单设计', '横幅设计', '3D设计', '建筑设计', '室内设计', '服装设计', '珠宝设计', '工业设计'],\n  '营销工具': ['SEO优化', '社交媒体营销', '邮件营销', '内容营销', '广告优化', '市场分析', '竞品分析', '用户分析', '转化优化', '关键词研究', '链接建设', '网站分析', '社交媒体管理', '影响者营销', '联盟营销', '付费广告', '营销自动化', '客户获取', '品牌监控', '声誉管理', '营销策略', '销售漏斗'],\n  '教育学习': ['在线教育', '语言学习', '技能培训', '考试辅导', '学习助手', '知识管理', '记忆训练', '阅读理解', '数学辅导', '编程教学', '科学实验', '历史学习', '地理学习', '艺术教育', '音乐教育', '体育训练', '职业培训', '认证考试', '学习计划', '进度跟踪', '个性化学习', '智能题库'],\n  '翻译工具': ['机器翻译', '实时翻译', '文档翻译', '网页翻译', '语言检测', '语音翻译', '图片翻译', '视频翻译', '字幕翻译', '多语言支持', '本地化', '术语管理', '翻译记忆', '质量评估', '翻译校对', '语言学习'],\n  '搜索工具': ['智能搜索', '学术搜索', '图片搜索', '代码搜索', '文档搜索', '语义搜索', '企业搜索', '电商搜索', '新闻搜索', '专利搜索', '法律搜索', '医学搜索', '搜索引擎优化', '搜索分析', '搜索推荐', '个性化搜索'],\n  '健康医疗': ['医疗诊断', '健康监测', '药物发现', '医学影像', '基因分析', '心理健康', '营养分析', '运动健康', '睡眠分析', '疾病预测', '康复训练', '远程医疗', '医疗记录', '症状检查', '用药提醒', '健康咨询'],\n  '金融科技': ['投资分析', '风险评估', '信用评分', '欺诈检测', '算法交易', '财务规划', '保险科技', '支付处理', '区块链', '加密货币', '智能投顾', '财务报告', '税务计算', '预算管理', '贷款审批', '合规监管'],\n  '法律服务': ['法律研究', '合同分析', '法律咨询', '案例检索', '法规监控', '合规检查', '知识产权', '专利分析', '法律文档', '诉讼支持', '法律翻译', '风险评估'],\n  '电商零售': ['产品推荐', '价格优化', '库存管理', '客户服务', '评论分析', '趋势预测', '供应链优化', '个性化营销', '欺诈防护', '物流优化', '用户画像', '转化优化'],\n  '游戏娱乐': ['游戏开发', '游戏AI', '角色生成', '关卡设计', '游戏测试', '虚拟现实', '增强现实', '游戏直播', '电竞分析', '娱乐推荐', '内容审核', '社交游戏'],\n  '安全防护': ['网络安全', '威胁检测', '恶意软件检测', '入侵防护', '数据加密', '身份验证', '访问控制', '安全审计', '漏洞扫描', '安全监控', '事件响应', '隐私保护'],\n  '其他工具': ['文本处理', '格式转换', '二维码生成', '密码管理', '文件管理', '截图工具', '录屏工具', '浏览器插件', '移动应用', '桌面应用', '系统优化', '数据恢复', '文件压缩', '文件同步', '远程控制', '虚拟机', '网络工具', '系统监控']\n};\n"], "names": [], "mappings": "AAAA,qCAAqC;;;;;;AAC9B,MAAM,iBAAiB;IAC5B,kBAAkB;IAClB;IAAQ;IAAW;IAAQ;IAAQ;IAAQ;IAAO;IAAU;IAC5D;IAAW;IAAQ;IAAQ;IAAQ;IAAS;IAE5C,gBAAgB;IAChB;IAAQ;IAAQ;IAAQ;IAAQ;IAAQ;IACxC;IAAQ;IAAQ;IAAQ;IAAU;IAAQ;IAC1C;IAAQ;IAAQ;IAAQ;IAAO;IAAQ;IAEvC,iBAAiB;IACjB;IAAQ;IAAQ;IAAQ;IAAQ;IAAQ;IACxC;IAAQ;IAAQ;IAAQ;IAAQ;IAAQ;IACxC;IAAU;IAAQ;IAAQ;IAAQ;IAAQ;IAC1C;IAAQ;IAAQ;IAAQ;IAAQ;IAAQ;IAExC,iBAAiB;IACjB;IAAQ;IAAQ;IAAQ;IAAQ;IAAQ;IACxC;IAAQ;IAAQ;IAAQ;IAAS;IAAQ;IACzC;IAAQ;IAAQ;IAAQ;IAAQ;IAAO;IACvC;IAAU;IAAQ;IAAQ;IAE1B,iBAAiB;IACjB;IAAQ;IAAQ;IAAQ;IAAQ;IAAQ;IACxC;IAAS;IAAQ;IAAM;IAAQ;IAAQ;IACvC;IAAQ;IAAQ;IAAQ;IAAU;IAAQ;IAC1C;IAAQ;IAAQ;IAAQ;IAExB,eAAe;IACf;IAAQ;IAAQ;IAAQ;IAAQ;IAAQ;IACxC;IAAS;IAAQ;IAAQ;IAAQ;IAAQ;IACzC;IAAS;IAAS;IAAY;IAAQ;IAAQ;IAC9C;IAAS;IAAQ;IAAQ;IAAU;IAAS;IAE5C,iBAAiB;IACjB;IAAQ;IAAS;IAAQ;IAAQ;IAAQ;IACzC;IAAQ;IAAQ;IAAQ;IAAQ;IAAQ;IACxC;IAAQ;IAAU;IAAQ;IAAQ;IAAQ;IAC1C;IAAQ;IAAQ;IAAS;IAEzB,eAAe;IACf;IAAS;IAAQ;IAAQ;IAAQ;IAAQ;IACzC;IAAQ;IAAQ;IAAQ;IAAQ;IAAQ;IACxC;IAAQ;IAAO;IAAQ;IAAQ;IAAQ;IACvC;IAAU;IAAQ;IAAQ;IAE1B,eAAe;IACf;IAAQ;IAAQ;IAAQ;IAAU;IAAQ;IAC1C;IAAQ;IAAQ;IAAQ;IAAQ;IAAQ;IACxC;IAAQ;IAAQ;IAAQ;IAAQ;IAAQ;IACxC;IAAQ;IAAQ;IAAQ;IAExB,cAAc;IACd;IAAS;IAAU;IAAQ;IAAQ;IAAQ;IAC3C;IAAQ;IAAQ;IAAQ;IAAS;IAAQ;IACzC;IAAU;IAAS;IAAQ;IAAQ;IAAS;IAC5C;IAAQ;IAAQ;IAAQ;IAExB,cAAc;IACd;IAAQ;IAAQ;IAAQ;IAAQ;IAAQ;IACxC;IAAQ;IAAQ;IAAQ;IAAQ;IAAQ;IACxC;IAAQ;IAAQ;IAAQ;IAAQ;IAAQ;IACxC;IAAQ;IAAQ;IAAS;IAEzB,cAAc;IACd;IAAQ;IAAQ;IAAQ;IAAQ;IAAQ;IACxC;IAAQ;IAAQ;IAAQ;IAAS;IAAO;IACxC;IAAQ;IAAQ;IAAQ;IAExB,cAAc;IACd;IAAQ;IAAQ;IAAQ;IAAQ;IAAQ;IACxC;IAAQ;IAAQ;IAAQ;IAAQ;IAAQ;IACxC;IAAU;IAAQ;IAAQ;IAE1B,cAAc;IACd;IAAQ;IAAQ;IAAQ;IAAQ;IAAQ;IACxC;IAAQ;IAAQ;IAAQ;IAAQ;IAAQ;IACxC;IAAQ;IAAQ;IAAQ;IAExB,cAAc;IACd;IAAQ;IAAQ;IAAQ;IAAQ;IAAQ;IACxC;IAAQ;IAAQ;IAAO;IAAQ;IAAQ;IACvC;IAAQ;IAAQ;IAAQ;IAExB,cAAc;IACd;IAAQ;IAAQ;IAAQ;IAAQ;IAAQ;IACxC;IAAQ;IAAQ;IAAQ;IAAQ;IAAQ;IAExC,cAAc;IACd;IAAQ;IAAQ;IAAQ;IAAQ;IAAQ;IACxC;IAAS;IAAS;IAAQ;IAAQ;IAAQ;IAE1C,eAAe;IACf;IAAQ;IAAQ;IAAQ;IAAQ;IAAQ;IACxC;IAAQ;IAAQ;IAAQ;IAAQ;IAAQ;IAExC,cAAc;IACd;IAAQ;IAAQ;IAAU;IAAQ;IAAQ;IAC1C;IAAQ;IAAQ;IAAQ;IAAQ;IAAQ;IAExC,gBAAgB;IAChB;IAAQ;IAAQ;IAAS;IAAQ;IAAQ;IACzC;IAAQ;IAAS;IAAQ;IAAQ;IAAQ;IACzC;IAAQ;IAAQ;IAAQ;IAAO;IAAQ;CACxC;AAGM,MAAM,iBAAiB;AAGvB,MAAM,mBAAmB;IAC9B,UAAU;QAAC;QAAQ;QAAW;QAAQ;QAAQ;QAAQ;QAAO;QAAU;QAAU;QAAW;QAAQ;QAAQ;QAAQ;QAAS;KAAQ;IACrI,QAAQ;QAAC;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAU;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAO;QAAQ;KAAO;IACzJ,QAAQ;QAAC;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAU;QAAQ;QAAQ;QAAQ;QAAQ;QAAS;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;KAAO;IAC3M,QAAQ;QAAC;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAS;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAO;QAAQ;QAAU;QAAQ;QAAQ;KAAO;IAC1L,QAAQ;QAAC;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAS;QAAS;QAAQ;QAAM;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAU;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;KAAO;IAC1L,QAAQ;QAAC;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAS;QAAS;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAS;QAAS;QAAY;QAAQ;QAAQ;QAAQ;QAAS;QAAQ;QAAQ;QAAU;QAAS;KAAM;IACnN,QAAQ;QAAC;QAAQ;QAAS;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAU;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAS;KAAQ;IAC7L,QAAQ;QAAC;QAAS;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAO;QAAQ;QAAQ;QAAQ;QAAQ;QAAU;QAAQ;QAAQ;KAAO;IAC1L,QAAQ;QAAC;QAAQ;QAAQ;QAAQ;QAAU;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;KAAO;IAC1L,QAAQ;QAAC;QAAS;QAAU;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAS;QAAQ;QAAQ;QAAU;QAAS;QAAQ;QAAQ;QAAS;QAAQ;QAAQ;QAAQ;QAAQ;KAAO;IAChM,QAAQ;QAAC;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAS;KAAO;IACzL,QAAQ;QAAC;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAS;QAAO;QAAQ;QAAQ;QAAQ;QAAQ;KAAO;IACxI,QAAQ;QAAC;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAU;QAAQ;QAAQ;KAAQ;IAC3I,QAAQ;QAAC;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;KAAO;IACxI,QAAQ;QAAC;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAO;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;KAAO;IACvI,QAAQ;QAAC;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;KAAO;IACxG,QAAQ;QAAC;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAS;QAAS;QAAQ;QAAQ;QAAQ;KAAO;IAC1G,QAAQ;QAAC;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;KAAO;IACxG,QAAQ;QAAC;QAAQ;QAAQ;QAAU;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;KAAO;IAC1G,QAAQ;QAAC;QAAQ;QAAQ;QAAS;QAAQ;QAAQ;QAAQ;QAAQ;QAAS;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAO;QAAQ;KAAO;AAC3J", "debugId": null}}, {"offset": {"line": 1247, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/TagSelector.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { Tag, Search, X } from 'lucide-react';\nimport { AVAILABLE_TAGS, MAX_TAGS_COUNT } from '@/constants/tags';\n\ninterface TagSelectorProps {\n  selectedTags: string[];\n  onTagsChange: (tags: string[]) => void;\n  maxTags?: number;\n}\n\nexport default function TagSelector({\n  selectedTags,\n  onTagsChange,\n  maxTags = MAX_TAGS_COUNT\n}: TagSelectorProps) {\n  const [searchTerm, setSearchTerm] = useState('');\n  const [isOpen, setIsOpen] = useState(false);\n\n  const toggleTag = (tag: string) => {\n    if (selectedTags.includes(tag)) {\n      onTagsChange(selectedTags.filter(t => t !== tag));\n    } else if (selectedTags.length < maxTags) {\n      onTagsChange([...selectedTags, tag]);\n    }\n  };\n\n  const removeTag = (tag: string) => {\n    onTagsChange(selectedTags.filter(t => t !== tag));\n  };\n\n  // 过滤标签：根据搜索词过滤，并排除已选择的标签\n  const filteredTags = AVAILABLE_TAGS.filter(tag =>\n    tag.toLowerCase().includes(searchTerm.toLowerCase()) &&\n    !selectedTags.includes(tag)\n  );\n\n  return (\n    <div className=\"space-y-4\">\n      {/* 标题和计数器 */}\n      <div className=\"flex items-center justify-between\">\n        <h3 className=\"text-lg font-medium text-gray-900\">选择标签</h3>\n        <span className=\"text-sm text-gray-500\">\n          已选择 {selectedTags.length}/{maxTags} 个标签\n        </span>\n      </div>\n\n      {/* 已选择的标签 */}\n      {selectedTags.length > 0 && (\n        <div className=\"space-y-2\">\n          <h4 className=\"text-sm font-medium text-gray-700\">已选择的标签：</h4>\n          <div className=\"flex flex-wrap gap-2\">\n            {selectedTags.map((tag) => (\n              <span\n                key={tag}\n                className=\"inline-flex items-center px-3 py-1 rounded-md text-sm font-medium bg-blue-100 text-blue-800\"\n              >\n                {tag}\n                <button\n                  type=\"button\"\n                  onClick={() => removeTag(tag)}\n                  className=\"ml-2 text-blue-600 hover:text-blue-800\"\n                >\n                  <X className=\"h-3 w-3\" />\n                </button>\n              </span>\n            ))}\n          </div>\n        </div>\n      )}\n\n      {/* 标签选择器 */}\n      <div className=\"space-y-3\">\n        <div className=\"relative\">\n          <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n            选择标签（最多{maxTags}个）\n          </label>\n\n          {/* 搜索框 */}\n          <div className=\"relative mb-3\">\n            <input\n              type=\"text\"\n              placeholder=\"搜索标签...\"\n              value={searchTerm}\n              onChange={(e) => setSearchTerm(e.target.value)}\n              onFocus={() => setIsOpen(true)}\n              className=\"w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            />\n            <Search className=\"absolute left-3 top-2.5 h-4 w-4 text-gray-400\" />\n          </div>\n\n          {/* 标签选择下拉框 */}\n          {(isOpen || searchTerm) && (\n            <div className=\"relative\">\n              <div className=\"absolute z-10 w-full bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-y-auto\">\n                {filteredTags.length > 0 ? (\n                  <div className=\"p-2\">\n                    <div className=\"grid grid-cols-1 gap-1\">\n                      {filteredTags.map((tag) => {\n                        const isDisabled = selectedTags.length >= maxTags;\n\n                        return (\n                          <button\n                            key={tag}\n                            type=\"button\"\n                            onClick={() => {\n                              toggleTag(tag);\n                              setSearchTerm('');\n                              setIsOpen(false);\n                            }}\n                            disabled={isDisabled}\n                            className={`\n                              w-full px-3 py-2 text-sm text-left rounded-md transition-colors\n                              ${isDisabled\n                                ? 'bg-gray-100 text-gray-400 cursor-not-allowed'\n                                : 'hover:bg-blue-50 text-gray-700'\n                              }\n                            `}\n                          >\n                            <div className=\"flex items-center\">\n                              <Tag className=\"h-3 w-3 mr-2 text-gray-400\" />\n                              {tag}\n                            </div>\n                          </button>\n                        );\n                      })}\n                    </div>\n                    {filteredTags.length > 50 && (\n                      <p className=\"text-xs text-gray-500 mt-2 px-3\">\n                        找到 {filteredTags.length} 个匹配标签\n                      </p>\n                    )}\n                  </div>\n                ) : (\n                  <div className=\"p-4 text-center text-gray-500 text-sm\">\n                    {searchTerm ? '未找到匹配的标签' : '开始输入以搜索标签'}\n                  </div>\n                )}\n              </div>\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* 点击外部关闭下拉框 */}\n      {(isOpen || searchTerm) && (\n        <div\n          className=\"fixed inset-0 z-0\"\n          onClick={() => {\n            setIsOpen(false);\n            setSearchTerm('');\n          }}\n        />\n      )}\n\n      {/* 提示信息 */}\n      {selectedTags.length >= maxTags && (\n        <p className=\"text-sm text-amber-600\">\n          最多只能选择{maxTags}个标签\n        </p>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AACA;AAJA;;;;;AAYe,SAAS,YAAY,EAClC,YAAY,EACZ,YAAY,EACZ,UAAU,wHAAA,CAAA,iBAAc,EACP;IACjB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,MAAM,YAAY,CAAC;QACjB,IAAI,aAAa,QAAQ,CAAC,MAAM;YAC9B,aAAa,aAAa,MAAM,CAAC,CAAA,IAAK,MAAM;QAC9C,OAAO,IAAI,aAAa,MAAM,GAAG,SAAS;YACxC,aAAa;mBAAI;gBAAc;aAAI;QACrC;IACF;IAEA,MAAM,YAAY,CAAC;QACjB,aAAa,aAAa,MAAM,CAAC,CAAA,IAAK,MAAM;IAC9C;IAEA,yBAAyB;IACzB,MAAM,eAAe,wHAAA,CAAA,iBAAc,CAAC,MAAM,CAAC,CAAA,MACzC,IAAI,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACjD,CAAC,aAAa,QAAQ,CAAC;IAGzB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAoC;;;;;;kCAClD,8OAAC;wBAAK,WAAU;;4BAAwB;4BACjC,aAAa,MAAM;4BAAC;4BAAE;4BAAQ;;;;;;;;;;;;;YAKtC,aAAa,MAAM,GAAG,mBACrB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAoC;;;;;;kCAClD,8OAAC;wBAAI,WAAU;kCACZ,aAAa,GAAG,CAAC,CAAC,oBACjB,8OAAC;gCAEC,WAAU;;oCAET;kDACD,8OAAC;wCACC,MAAK;wCACL,SAAS,IAAM,UAAU;wCACzB,WAAU;kDAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;;;;;;;;+BATV;;;;;;;;;;;;;;;;0BAkBf,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAM,WAAU;;gCAA+C;gCACtD;gCAAQ;;;;;;;sCAIlB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,MAAK;oCACL,aAAY;oCACZ,OAAO;oCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;oCAC7C,SAAS,IAAM,UAAU;oCACzB,WAAU;;;;;;8CAEZ,8OAAC,sMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;;;;;;;wBAInB,CAAC,UAAU,UAAU,mBACpB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACZ,aAAa,MAAM,GAAG,kBACrB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACZ,aAAa,GAAG,CAAC,CAAC;gDACjB,MAAM,aAAa,aAAa,MAAM,IAAI;gDAE1C,qBACE,8OAAC;oDAEC,MAAK;oDACL,SAAS;wDACP,UAAU;wDACV,cAAc;wDACd,UAAU;oDACZ;oDACA,UAAU;oDACV,WAAW,CAAC;;8BAEV,EAAE,aACE,iDACA,iCACH;4BACH,CAAC;8DAED,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,gMAAA,CAAA,MAAG;gEAAC,WAAU;;;;;;4DACd;;;;;;;mDAlBE;;;;;4CAsBX;;;;;;wCAED,aAAa,MAAM,GAAG,oBACrB,8OAAC;4CAAE,WAAU;;gDAAkC;gDACzC,aAAa,MAAM;gDAAC;;;;;;;;;;;;yDAK9B,8OAAC;oCAAI,WAAU;8CACZ,aAAa,aAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;YAUxC,CAAC,UAAU,UAAU,mBACpB,8OAAC;gBACC,WAAU;gBACV,SAAS;oBACP,UAAU;oBACV,cAAc;gBAChB;;;;;;YAKH,aAAa,MAAM,IAAI,yBACtB,8OAAC;gBAAE,WAAU;;oBAAyB;oBAC7B;oBAAQ;;;;;;;;;;;;;AAKzB", "debugId": null}}, {"offset": {"line": 1541, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/app/submit/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { useSession } from 'next-auth/react';\nimport Layout from '@/components/Layout';\nimport LoadingSpinner from '@/components/LoadingSpinner';\nimport ErrorMessage from '@/components/ErrorMessage';\nimport SuccessMessage from '@/components/SuccessMessage';\nimport LoginModal from '@/components/auth/LoginModal';\nimport {\n  Upload,\n  Link as LinkIcon,\n  Info\n} from 'lucide-react';\nimport { MAX_TAGS_COUNT } from '@/constants/tags';\nimport TagSelector from '@/components/TagSelector';\n\nconst categories = [\n  { value: 'text-generation', label: '文本生成' },\n  { value: 'image-generation', label: '图像生成' },\n  { value: 'code-generation', label: '代码生成' },\n  { value: 'data-analysis', label: '数据分析' },\n  { value: 'audio-processing', label: '音频处理' },\n  { value: 'video-editing', label: '视频编辑' },\n  { value: 'translation', label: '语言翻译' },\n  { value: 'search-engines', label: '搜索引擎' },\n  { value: 'education', label: '教育学习' },\n  { value: 'marketing', label: '营销工具' },\n  { value: 'productivity', label: '生产力工具' },\n  { value: 'customer-service', label: '客户服务' }\n];\n\nconst pricingOptions = [\n  { value: 'free', label: '免费' },\n  { value: 'freemium', label: '免费增值' },\n  { value: 'paid', label: '付费' }\n];\n\ninterface FormData {\n  name: string;\n  tagline: string;\n  description: string;\n  longDescription: string;\n  website: string;\n  logo: string;\n  category: string;\n  tags: string[];\n  pricing: string;\n  pricingDetails: string;\n  screenshots: string[];\n}\n\nexport default function SubmitPage() {\n  const { data: session, status } = useSession();\n  const [formData, setFormData] = useState<FormData>({\n    name: '',\n    tagline: '',\n    description: '',\n    longDescription: '',\n    website: '',\n    logo: '',\n    category: '',\n    tags: [],\n    pricing: '',\n    pricingDetails: '',\n    screenshots: []\n  });\n\n\n\n\n  const [currentScreenshot, setCurrentScreenshot] = useState('');\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle');\n  const [errors, setErrors] = useState<Record<string, string>>({});\n  const [submitMessage, setSubmitMessage] = useState('');\n  const [isLoginModalOpen, setIsLoginModalOpen] = useState(false);\n  const [logoFile, setLogoFile] = useState<File | null>(null);\n  const [logoPreview, setLogoPreview] = useState<string>('');\n\n  const validateForm = (): boolean => {\n    const newErrors: Record<string, string> = {};\n\n    if (!formData.name.trim()) newErrors.name = '工具名称是必填项';\n    if (!formData.description.trim()) newErrors.description = '工具描述是必填项';\n    if (!formData.website.trim()) newErrors.website = '官方网站是必填项';\n    if (!formData.category) newErrors.category = '请选择一个分类';\n    if (!formData.pricing) newErrors.pricing = '请选择价格模式';\n\n    // URL validation\n    if (formData.website && !formData.website.match(/^https?:\\/\\/.+/)) {\n      newErrors.website = '请输入有效的网站地址（以 http:// 或 https:// 开头）';\n    }\n\n\n\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n\n    // 检查用户是否已登录\n    if (!session) {\n      setIsLoginModalOpen(true);\n      return;\n    }\n\n    if (!validateForm()) return;\n\n    setIsSubmitting(true);\n    setSubmitStatus('idle');\n\n    try {\n      let logoUrl = formData.logo;\n\n      // 如果有选择的logo文件，先上传\n      if (logoFile) {\n        const logoFormData = new FormData();\n        logoFormData.append('logo', logoFile);\n\n        const uploadResponse = await fetch('/api/upload/logo', {\n          method: 'POST',\n          body: logoFormData,\n        });\n\n        const uploadData = await uploadResponse.json();\n        if (uploadData.success) {\n          logoUrl = uploadData.data.url;\n        } else {\n          throw new Error(uploadData.message || 'Logo上传失败');\n        }\n      }\n\n      // 调用新的提交API\n      const response = await fetch('/api/tools/submit', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          name: formData.name,\n          tagline: formData.tagline,\n          description: formData.description,\n          longDescription: formData.longDescription,\n          website: formData.website,\n          logo: logoUrl || undefined,\n          category: formData.category,\n          tags: formData.tags,\n          pricing: formData.pricing,\n          pricingDetails: formData.pricingDetails,\n          screenshots: formData.screenshots\n        }),\n      });\n\n      const data = await response.json();\n\n      if (data.success) {\n        setSubmitStatus('success');\n        setSubmitMessage(data.data.message || '工具提交成功！我们会在审核后尽快发布。');\n        // 重置表单\n        setFormData({\n          name: '',\n          tagline: '',\n          description: '',\n          longDescription: '',\n          website: '',\n          logo: '',\n          category: '',\n          tags: [],\n          pricing: '',\n          pricingDetails: '',\n          screenshots: []\n        });\n        setCurrentScreenshot('');\n        setLogoFile(null);\n        setLogoPreview('');\n        setErrors({});\n      } else {\n        setSubmitStatus('error');\n        setSubmitMessage(data.message || '提交失败，请重试');\n      }\n    } catch (error) {\n      console.error('Error submitting tool:', error);\n      setSubmitStatus('error');\n      setSubmitMessage('网络错误，请检查连接后重试');\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n\n\n  // 处理logo文件选择\n  const handleLogoChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const file = e.target.files?.[0];\n    if (file) {\n      setLogoFile(file);\n      // 创建预览URL\n      const reader = new FileReader();\n      reader.onload = (e) => {\n        setLogoPreview(e.target?.result as string);\n      };\n      reader.readAsDataURL(file);\n    }\n  };\n\n  return (\n    <Layout>\n      <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {/* Header */}\n        <div className=\"text-center mb-8\">\n          <h1 className=\"text-3xl font-bold text-gray-900 mb-4\">\n            <Upload className=\"inline-block mr-3 h-8 w-8 text-blue-600\" />\n            提交 AI 工具\n          </h1>\n          <p className=\"text-lg text-gray-600 max-w-2xl mx-auto\">\n            分享您发现或开发的优秀 AI 工具，帮助更多人发现和使用这些工具。我们会在审核通过后发布您的提交。\n          </p>\n        </div>\n\n        {/* Success/Error Messages */}\n        {submitStatus === 'success' && (\n          <SuccessMessage\n            message={submitMessage || '工具提交成功！我们会在 1-3 个工作日内审核您的提交。'}\n            onClose={() => setSubmitStatus('idle')}\n            className=\"mb-6\"\n          />\n        )}\n\n        {submitStatus === 'error' && (\n          <ErrorMessage\n            message={submitMessage || '提交失败，请检查网络连接后重试。'}\n            onClose={() => setSubmitStatus('idle')}\n            className=\"mb-6\"\n          />\n        )}\n\n        {/* Form */}\n        <form onSubmit={handleSubmit} className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-8\">\n          {/* Basic Information */}\n          <div className=\"mb-8\">\n            <h2 className=\"text-xl font-semibold text-gray-900 mb-4\">基本信息</h2>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  工具名称 *\n                </label>\n                <input\n                  type=\"text\"\n                  value={formData.name}\n                  onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}\n                  className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${\n                    errors.name ? 'border-red-300' : 'border-gray-300'\n                  }`}\n                  placeholder=\"例如：ChatGPT\"\n                />\n                {errors.name && <p className=\"text-red-600 text-sm mt-1\">{errors.name}</p>}\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  工具标语（可选）\n                </label>\n                <input\n                  type=\"text\"\n                  value={formData.tagline}\n                  onChange={(e) => setFormData(prev => ({ ...prev, tagline: e.target.value }))}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                  placeholder=\"简短描述工具的核心价值\"\n                />\n              </div>\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6 mt-6\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  官方网站 *\n                </label>\n                <div className=\"relative\">\n                  <input\n                    type=\"url\"\n                    value={formData.website}\n                    onChange={(e) => setFormData(prev => ({ ...prev, website: e.target.value }))}\n                    className={`w-full pl-10 pr-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${\n                      errors.website ? 'border-red-300' : 'border-gray-300'\n                    }`}\n                    placeholder=\"https://example.com\"\n                  />\n                  <LinkIcon className=\"absolute left-3 top-2.5 h-5 w-5 text-gray-400\" />\n                </div>\n                {errors.website && <p className=\"text-red-600 text-sm mt-1\">{errors.website}</p>}\n              </div>\n            </div>\n\n            <div className=\"mt-6\">\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                工具描述 *\n              </label>\n              <textarea\n                value={formData.description}\n                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}\n                rows={4}\n                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${\n                  errors.description ? 'border-red-300' : 'border-gray-300'\n                }`}\n                placeholder=\"详细描述这个 AI 工具的功能和特点...\"\n              />\n              {errors.description && <p className=\"text-red-600 text-sm mt-1\">{errors.description}</p>}\n            </div>\n\n            <div className=\"mt-6\">\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Logo图片（可选）\n              </label>\n              <div className=\"flex items-center gap-4\">\n                <input\n                  type=\"file\"\n                  accept=\"image/*\"\n                  onChange={handleLogoChange}\n                  className=\"block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100\"\n                />\n                {logoPreview && (\n                  <div className=\"flex-shrink-0\">\n                    <img\n                      src={logoPreview}\n                      alt=\"Logo预览\"\n                      className=\"w-16 h-16 object-cover rounded-lg border border-gray-300\"\n                    />\n                  </div>\n                )}\n              </div>\n              <p className=\"text-sm text-gray-500 mt-1\">\n                支持 JPEG、PNG、GIF、WebP 格式，文件大小不超过 5MB\n              </p>\n            </div>\n          </div>\n\n          {/* Category and Pricing */}\n          <div className=\"mb-8\">\n            <h2 className=\"text-xl font-semibold text-gray-900 mb-4\">分类和定价</h2>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  工具分类 *\n                </label>\n                <select\n                  value={formData.category}\n                  onChange={(e) => setFormData(prev => ({ ...prev, category: e.target.value }))}\n                  className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${\n                    errors.category ? 'border-red-300' : 'border-gray-300'\n                  }`}\n                >\n                  <option value=\"\">请选择分类</option>\n                  {categories.map(category => (\n                    <option key={category.value} value={category.value}>\n                      {category.label}\n                    </option>\n                  ))}\n                </select>\n                {errors.category && <p className=\"text-red-600 text-sm mt-1\">{errors.category}</p>}\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  价格模式 *\n                </label>\n                <select\n                  value={formData.pricing}\n                  onChange={(e) => setFormData(prev => ({ ...prev, pricing: e.target.value }))}\n                  className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${\n                    errors.pricing ? 'border-red-300' : 'border-gray-300'\n                  }`}\n                >\n                  <option value=\"\">请选择价格模式</option>\n                  {pricingOptions.map(option => (\n                    <option key={option.value} value={option.value}>\n                      {option.label}\n                    </option>\n                  ))}\n                </select>\n                {errors.pricing && <p className=\"text-red-600 text-sm mt-1\">{errors.pricing}</p>}\n              </div>\n            </div>\n          </div>\n\n          {/* Tags */}\n          <div className=\"mb-8\">\n            <TagSelector\n              selectedTags={formData.tags}\n              onTagsChange={(tags) => setFormData(prev => ({ ...prev, tags }))}\n              maxTags={MAX_TAGS_COUNT}\n            />\n          </div>\n\n\n\n          {/* User Info Display */}\n          {session && (\n            <div className=\"mb-8 bg-green-50 border border-green-200 rounded-lg p-4\">\n              <h3 className=\"text-sm font-medium text-green-800 mb-2\">提交者信息</h3>\n              <p className=\"text-sm text-green-700\">\n                提交者：{session.user?.name || session.user?.email}\n              </p>\n              <p className=\"text-sm text-green-700\">\n                邮箱：{session.user?.email}\n              </p>\n            </div>\n          )}\n\n          {/* Guidelines */}\n          <div className=\"mb-8 bg-blue-50 border border-blue-200 rounded-lg p-6\">\n            <div className=\"flex items-start\">\n              <Info className=\"h-5 w-5 text-blue-600 mr-3 mt-0.5 flex-shrink-0\" />\n              <div>\n                <h3 className=\"text-sm font-medium text-blue-900 mb-2\">提交指南</h3>\n                <ul className=\"text-sm text-blue-800 space-y-1\">\n                  <li>• 请确保提交的是真实存在且可正常访问的 AI 工具</li>\n                  <li>• 工具描述应该准确、客观，避免过度营销</li>\n                  <li>• 我们会在 1-3 个工作日内审核您的提交</li>\n                  <li>• 审核通过后，工具将出现在我们的目录中</li>\n                  <li>• 如有问题，我们会通过邮箱联系您</li>\n                </ul>\n              </div>\n            </div>\n          </div>\n\n          {/* Submit Button */}\n          <div className=\"flex justify-end\">\n            <button\n              type=\"submit\"\n              disabled={isSubmitting}\n              className={`px-8 py-3 rounded-lg font-medium transition-colors ${\n                isSubmitting\n                  ? 'bg-gray-400 text-gray-700 cursor-not-allowed'\n                  : 'bg-blue-600 text-white hover:bg-blue-700'\n              }`}\n            >\n              {isSubmitting ? (\n                <div className=\"flex items-center\">\n                  <LoadingSpinner size=\"sm\" className=\"mr-2\" />\n                  提交中...\n                </div>\n              ) : (\n                '提交工具'\n              )}\n            </button>\n          </div>\n        </form>\n      </div>\n\n      {/* Login Modal */}\n      <LoginModal\n        isOpen={isLoginModalOpen}\n        onClose={() => setIsLoginModalOpen(false)}\n      />\n    </Layout>\n  );\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAKA;AACA;AAfA;;;;;;;;;;;;AAiBA,MAAM,aAAa;IACjB;QAAE,OAAO;QAAmB,OAAO;IAAO;IAC1C;QAAE,OAAO;QAAoB,OAAO;IAAO;IAC3C;QAAE,OAAO;QAAmB,OAAO;IAAO;IAC1C;QAAE,OAAO;QAAiB,OAAO;IAAO;IACxC;QAAE,OAAO;QAAoB,OAAO;IAAO;IAC3C;QAAE,OAAO;QAAiB,OAAO;IAAO;IACxC;QAAE,OAAO;QAAe,OAAO;IAAO;IACtC;QAAE,OAAO;QAAkB,OAAO;IAAO;IACzC;QAAE,OAAO;QAAa,OAAO;IAAO;IACpC;QAAE,OAAO;QAAa,OAAO;IAAO;IACpC;QAAE,OAAO;QAAgB,OAAO;IAAQ;IACxC;QAAE,OAAO;QAAoB,OAAO;IAAO;CAC5C;AAED,MAAM,iBAAiB;IACrB;QAAE,OAAO;QAAQ,OAAO;IAAK;IAC7B;QAAE,OAAO;QAAY,OAAO;IAAO;IACnC;QAAE,OAAO;QAAQ,OAAO;IAAK;CAC9B;AAgBc,SAAS;IACtB,MAAM,EAAE,MAAM,OAAO,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,aAAU,AAAD;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY;QACjD,MAAM;QACN,SAAS;QACT,aAAa;QACb,iBAAiB;QACjB,SAAS;QACT,MAAM;QACN,UAAU;QACV,MAAM,EAAE;QACR,SAAS;QACT,gBAAgB;QAChB,aAAa,EAAE;IACjB;IAKA,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgC;IAC/E,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAC9D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IACtD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAEvD,MAAM,eAAe;QACnB,MAAM,YAAoC,CAAC;QAE3C,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,IAAI,UAAU,IAAI,GAAG;QAC5C,IAAI,CAAC,SAAS,WAAW,CAAC,IAAI,IAAI,UAAU,WAAW,GAAG;QAC1D,IAAI,CAAC,SAAS,OAAO,CAAC,IAAI,IAAI,UAAU,OAAO,GAAG;QAClD,IAAI,CAAC,SAAS,QAAQ,EAAE,UAAU,QAAQ,GAAG;QAC7C,IAAI,CAAC,SAAS,OAAO,EAAE,UAAU,OAAO,GAAG;QAE3C,iBAAiB;QACjB,IAAI,SAAS,OAAO,IAAI,CAAC,SAAS,OAAO,CAAC,KAAK,CAAC,mBAAmB;YACjE,UAAU,OAAO,GAAG;QACtB;QAIA,UAAU;QACV,OAAO,OAAO,IAAI,CAAC,WAAW,MAAM,KAAK;IAC3C;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,YAAY;QACZ,IAAI,CAAC,SAAS;YACZ,oBAAoB;YACpB;QACF;QAEA,IAAI,CAAC,gBAAgB;QAErB,gBAAgB;QAChB,gBAAgB;QAEhB,IAAI;YACF,IAAI,UAAU,SAAS,IAAI;YAE3B,mBAAmB;YACnB,IAAI,UAAU;gBACZ,MAAM,eAAe,IAAI;gBACzB,aAAa,MAAM,CAAC,QAAQ;gBAE5B,MAAM,iBAAiB,MAAM,MAAM,oBAAoB;oBACrD,QAAQ;oBACR,MAAM;gBACR;gBAEA,MAAM,aAAa,MAAM,eAAe,IAAI;gBAC5C,IAAI,WAAW,OAAO,EAAE;oBACtB,UAAU,WAAW,IAAI,CAAC,GAAG;gBAC/B,OAAO;oBACL,MAAM,IAAI,MAAM,WAAW,OAAO,IAAI;gBACxC;YACF;YAEA,YAAY;YACZ,MAAM,WAAW,MAAM,MAAM,qBAAqB;gBAChD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,MAAM,SAAS,IAAI;oBACnB,SAAS,SAAS,OAAO;oBACzB,aAAa,SAAS,WAAW;oBACjC,iBAAiB,SAAS,eAAe;oBACzC,SAAS,SAAS,OAAO;oBACzB,MAAM,WAAW;oBACjB,UAAU,SAAS,QAAQ;oBAC3B,MAAM,SAAS,IAAI;oBACnB,SAAS,SAAS,OAAO;oBACzB,gBAAgB,SAAS,cAAc;oBACvC,aAAa,SAAS,WAAW;gBACnC;YACF;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,gBAAgB;gBAChB,iBAAiB,KAAK,IAAI,CAAC,OAAO,IAAI;gBACtC,OAAO;gBACP,YAAY;oBACV,MAAM;oBACN,SAAS;oBACT,aAAa;oBACb,iBAAiB;oBACjB,SAAS;oBACT,MAAM;oBACN,UAAU;oBACV,MAAM,EAAE;oBACR,SAAS;oBACT,gBAAgB;oBAChB,aAAa,EAAE;gBACjB;gBACA,qBAAqB;gBACrB,YAAY;gBACZ,eAAe;gBACf,UAAU,CAAC;YACb,OAAO;gBACL,gBAAgB;gBAChB,iBAAiB,KAAK,OAAO,IAAI;YACnC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,gBAAgB;YAChB,iBAAiB;QACnB,SAAU;YACR,gBAAgB;QAClB;IACF;IAIA,aAAa;IACb,MAAM,mBAAmB,CAAC;QACxB,MAAM,OAAO,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE;QAChC,IAAI,MAAM;YACR,YAAY;YACZ,UAAU;YACV,MAAM,SAAS,IAAI;YACnB,OAAO,MAAM,GAAG,CAAC;gBACf,eAAe,EAAE,MAAM,EAAE;YAC3B;YACA,OAAO,aAAa,CAAC;QACvB;IACF;IAEA,qBACE,8OAAC,4HAAA,CAAA,UAAM;;0BACL,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;oCAA4C;;;;;;;0CAGhE,8OAAC;gCAAE,WAAU;0CAA0C;;;;;;;;;;;;oBAMxD,iBAAiB,2BAChB,8OAAC,oIAAA,CAAA,UAAc;wBACb,SAAS,iBAAiB;wBAC1B,SAAS,IAAM,gBAAgB;wBAC/B,WAAU;;;;;;oBAIb,iBAAiB,yBAChB,8OAAC,kIAAA,CAAA,UAAY;wBACX,SAAS,iBAAiB;wBAC1B,SAAS,IAAM,gBAAgB;wBAC/B,WAAU;;;;;;kCAKd,8OAAC;wBAAK,UAAU;wBAAc,WAAU;;0CAEtC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA2C;;;;;;kDAEzD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAGhE,8OAAC;wDACC,MAAK;wDACL,OAAO,SAAS,IAAI;wDACpB,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;oEAAE,GAAG,IAAI;oEAAE,MAAM,EAAE,MAAM,CAAC,KAAK;gEAAC,CAAC;wDACvE,WAAW,CAAC,6FAA6F,EACvG,OAAO,IAAI,GAAG,mBAAmB,mBACjC;wDACF,aAAY;;;;;;oDAEb,OAAO,IAAI,kBAAI,8OAAC;wDAAE,WAAU;kEAA6B,OAAO,IAAI;;;;;;;;;;;;0DAGvE,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAGhE,8OAAC;wDACC,MAAK;wDACL,OAAO,SAAS,OAAO;wDACvB,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;oEAAE,GAAG,IAAI;oEAAE,SAAS,EAAE,MAAM,CAAC,KAAK;gEAAC,CAAC;wDAC1E,WAAU;wDACV,aAAY;;;;;;;;;;;;;;;;;;kDAKlB,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DACC,MAAK;4DACL,OAAO,SAAS,OAAO;4DACvB,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;wEAAE,GAAG,IAAI;wEAAE,SAAS,EAAE,MAAM,CAAC,KAAK;oEAAC,CAAC;4DAC1E,WAAW,CAAC,mGAAmG,EAC7G,OAAO,OAAO,GAAG,mBAAmB,mBACpC;4DACF,aAAY;;;;;;sEAEd,8OAAC,kMAAA,CAAA,OAAQ;4DAAC,WAAU;;;;;;;;;;;;gDAErB,OAAO,OAAO,kBAAI,8OAAC;oDAAE,WAAU;8DAA6B,OAAO,OAAO;;;;;;;;;;;;;;;;;kDAI/E,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,8OAAC;gDACC,OAAO,SAAS,WAAW;gDAC3B,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,aAAa,EAAE,MAAM,CAAC,KAAK;wDAAC,CAAC;gDAC9E,MAAM;gDACN,WAAW,CAAC,6FAA6F,EACvG,OAAO,WAAW,GAAG,mBAAmB,mBACxC;gDACF,aAAY;;;;;;4CAEb,OAAO,WAAW,kBAAI,8OAAC;gDAAE,WAAU;0DAA6B,OAAO,WAAW;;;;;;;;;;;;kDAGrF,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDACC,MAAK;wDACL,QAAO;wDACP,UAAU;wDACV,WAAU;;;;;;oDAEX,6BACC,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DACC,KAAK;4DACL,KAAI;4DACJ,WAAU;;;;;;;;;;;;;;;;;0DAKlB,8OAAC;gDAAE,WAAU;0DAA6B;;;;;;;;;;;;;;;;;;0CAO9C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA2C;;;;;;kDAEzD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAGhE,8OAAC;wDACC,OAAO,SAAS,QAAQ;wDACxB,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;oEAAE,GAAG,IAAI;oEAAE,UAAU,EAAE,MAAM,CAAC,KAAK;gEAAC,CAAC;wDAC3E,WAAW,CAAC,6FAA6F,EACvG,OAAO,QAAQ,GAAG,mBAAmB,mBACrC;;0EAEF,8OAAC;gEAAO,OAAM;0EAAG;;;;;;4DAChB,WAAW,GAAG,CAAC,CAAA,yBACd,8OAAC;oEAA4B,OAAO,SAAS,KAAK;8EAC/C,SAAS,KAAK;mEADJ,SAAS,KAAK;;;;;;;;;;;oDAK9B,OAAO,QAAQ,kBAAI,8OAAC;wDAAE,WAAU;kEAA6B,OAAO,QAAQ;;;;;;;;;;;;0DAG/E,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAGhE,8OAAC;wDACC,OAAO,SAAS,OAAO;wDACvB,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;oEAAE,GAAG,IAAI;oEAAE,SAAS,EAAE,MAAM,CAAC,KAAK;gEAAC,CAAC;wDAC1E,WAAW,CAAC,6FAA6F,EACvG,OAAO,OAAO,GAAG,mBAAmB,mBACpC;;0EAEF,8OAAC;gEAAO,OAAM;0EAAG;;;;;;4DAChB,eAAe,GAAG,CAAC,CAAA,uBAClB,8OAAC;oEAA0B,OAAO,OAAO,KAAK;8EAC3C,OAAO,KAAK;mEADF,OAAO,KAAK;;;;;;;;;;;oDAK5B,OAAO,OAAO,kBAAI,8OAAC;wDAAE,WAAU;kEAA6B,OAAO,OAAO;;;;;;;;;;;;;;;;;;;;;;;;0CAMjF,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,iIAAA,CAAA,UAAW;oCACV,cAAc,SAAS,IAAI;oCAC3B,cAAc,CAAC,OAAS,YAAY,CAAA,OAAQ,CAAC;gDAAE,GAAG,IAAI;gDAAE;4CAAK,CAAC;oCAC9D,SAAS,wHAAA,CAAA,iBAAc;;;;;;;;;;;4BAO1B,yBACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA0C;;;;;;kDACxD,8OAAC;wCAAE,WAAU;;4CAAyB;4CAC/B,QAAQ,IAAI,EAAE,QAAQ,QAAQ,IAAI,EAAE;;;;;;;kDAE3C,8OAAC;wCAAE,WAAU;;4CAAyB;4CAChC,QAAQ,IAAI,EAAE;;;;;;;;;;;;;0CAMxB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;sDAChB,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAyC;;;;;;8DACvD,8OAAC;oDAAG,WAAU;;sEACZ,8OAAC;sEAAG;;;;;;sEACJ,8OAAC;sEAAG;;;;;;sEACJ,8OAAC;sEAAG;;;;;;sEACJ,8OAAC;sEAAG;;;;;;sEACJ,8OAAC;sEAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAOZ,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCACC,MAAK;oCACL,UAAU;oCACV,WAAW,CAAC,mDAAmD,EAC7D,eACI,iDACA,4CACJ;8CAED,6BACC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,oIAAA,CAAA,UAAc;gDAAC,MAAK;gDAAK,WAAU;;;;;;4CAAS;;;;;;+CAI/C;;;;;;;;;;;;;;;;;;;;;;;0BAQV,8OAAC,wIAAA,CAAA,UAAU;gBACT,QAAQ;gBACR,SAAS,IAAM,oBAAoB;;;;;;;;;;;;AAI3C", "debugId": null}}]}
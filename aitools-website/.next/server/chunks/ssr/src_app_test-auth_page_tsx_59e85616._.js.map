{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/app/test-auth/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useSession, signIn, signOut } from 'next-auth/react';\nimport { useState } from 'react';\n\nexport default function TestAuth() {\n  const { data: session, status } = useSession();\n  const [email, setEmail] = useState('');\n  const [code, setCode] = useState('');\n  const [token, setToken] = useState('');\n  const [message, setMessage] = useState('');\n\n  const sendCode = async () => {\n    try {\n      const response = await fetch('/api/auth/send-code', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({ email }),\n      });\n      const data = await response.json();\n      if (data.success) {\n        setToken(data.token);\n        setMessage('验证码已发送！Token: ' + data.token);\n      } else {\n        setMessage('发送失败: ' + data.error);\n      }\n    } catch (error) {\n      setMessage('网络错误');\n    }\n  };\n\n  const verifyCode = async () => {\n    try {\n      const result = await signIn('email-code', {\n        email,\n        code,\n        token,\n        redirect: false,\n      });\n      \n      if (result?.ok) {\n        setMessage('登录成功！');\n      } else {\n        setMessage('登录失败: ' + result?.error);\n      }\n    } catch (error) {\n      setMessage('验证失败');\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 py-12 px-4\">\n      <div className=\"max-w-md mx-auto bg-white rounded-lg shadow-md p-6\">\n        <h1 className=\"text-2xl font-bold mb-6\">认证测试页面</h1>\n        \n        <div className=\"mb-6\">\n          <h2 className=\"text-lg font-semibold mb-2\">当前状态</h2>\n          <p>Status: {status}</p>\n          {session ? (\n            <div>\n              <p>已登录用户: {session.user?.email}</p>\n              <p>用户名: {session.user?.name}</p>\n              <p>用户ID: {(session.user as any)?.id}</p>\n              <p>用户角色: {(session.user as any)?.role}</p>\n              <button \n                onClick={() => signOut()}\n                className=\"mt-2 bg-red-500 text-white px-4 py-2 rounded\"\n              >\n                退出登录\n              </button>\n            </div>\n          ) : (\n            <p>未登录</p>\n          )}\n        </div>\n\n        <div className=\"mb-6\">\n          <h2 className=\"text-lg font-semibold mb-2\">邮件验证码登录测试</h2>\n          \n          <div className=\"mb-4\">\n            <label className=\"block text-sm font-medium mb-1\">邮箱</label>\n            <input\n              type=\"email\"\n              value={email}\n              onChange={(e) => setEmail(e.target.value)}\n              className=\"w-full border rounded px-3 py-2\"\n              placeholder=\"输入邮箱\"\n            />\n          </div>\n\n          <button\n            onClick={sendCode}\n            className=\"w-full bg-blue-500 text-white py-2 rounded mb-4\"\n          >\n            发送验证码\n          </button>\n\n          <div className=\"mb-4\">\n            <label className=\"block text-sm font-medium mb-1\">验证码</label>\n            <input\n              type=\"text\"\n              value={code}\n              onChange={(e) => setCode(e.target.value)}\n              className=\"w-full border rounded px-3 py-2\"\n              placeholder=\"输入6位验证码\"\n            />\n          </div>\n\n          <div className=\"mb-4\">\n            <label className=\"block text-sm font-medium mb-1\">Token</label>\n            <input\n              type=\"text\"\n              value={token}\n              onChange={(e) => setToken(e.target.value)}\n              className=\"w-full border rounded px-3 py-2\"\n              placeholder=\"自动填充\"\n            />\n          </div>\n\n          <button\n            onClick={verifyCode}\n            className=\"w-full bg-green-500 text-white py-2 rounded mb-4\"\n          >\n            验证登录\n          </button>\n\n          {message && (\n            <div className=\"p-3 bg-gray-100 rounded text-sm\">\n              {message}\n            </div>\n          )}\n        </div>\n\n        <div className=\"mb-6\">\n          <h2 className=\"text-lg font-semibold mb-2\">OAuth登录测试</h2>\n          <button\n            onClick={() => signIn('google')}\n            className=\"w-full bg-red-500 text-white py-2 rounded mb-2\"\n          >\n            Google登录\n          </button>\n          <button\n            onClick={() => signIn('github')}\n            className=\"w-full bg-gray-800 text-white py-2 rounded\"\n          >\n            GitHub登录\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKe,SAAS;IACtB,MAAM,EAAE,MAAM,OAAO,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,aAAU,AAAD;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,WAAW;QACf,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,uBAAuB;gBAClD,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBAAE;gBAAM;YAC/B;YACA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,IAAI,KAAK,OAAO,EAAE;gBAChB,SAAS,KAAK,KAAK;gBACnB,WAAW,mBAAmB,KAAK,KAAK;YAC1C,OAAO;gBACL,WAAW,WAAW,KAAK,KAAK;YAClC;QACF,EAAE,OAAO,OAAO;YACd,WAAW;QACb;IACF;IAEA,MAAM,aAAa;QACjB,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,8IAAA,CAAA,SAAM,AAAD,EAAE,cAAc;gBACxC;gBACA;gBACA;gBACA,UAAU;YACZ;YAEA,IAAI,QAAQ,IAAI;gBACd,WAAW;YACb,OAAO;gBACL,WAAW,WAAW,QAAQ;YAChC;QACF,EAAE,OAAO,OAAO;YACd,WAAW;QACb;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,WAAU;8BAA0B;;;;;;8BAExC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA6B;;;;;;sCAC3C,8OAAC;;gCAAE;gCAAS;;;;;;;wBACX,wBACC,8OAAC;;8CACC,8OAAC;;wCAAE;wCAAQ,QAAQ,IAAI,EAAE;;;;;;;8CACzB,8OAAC;;wCAAE;wCAAM,QAAQ,IAAI,EAAE;;;;;;;8CACvB,8OAAC;;wCAAE;wCAAQ,QAAQ,IAAI,EAAU;;;;;;;8CACjC,8OAAC;;wCAAE;wCAAQ,QAAQ,IAAI,EAAU;;;;;;;8CACjC,8OAAC;oCACC,SAAS,IAAM,CAAA,GAAA,8IAAA,CAAA,UAAO,AAAD;oCACrB,WAAU;8CACX;;;;;;;;;;;iDAKH,8OAAC;sCAAE;;;;;;;;;;;;8BAIP,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA6B;;;;;;sCAE3C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAM,WAAU;8CAAiC;;;;;;8CAClD,8OAAC;oCACC,MAAK;oCACL,OAAO;oCACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;oCACxC,WAAU;oCACV,aAAY;;;;;;;;;;;;sCAIhB,8OAAC;4BACC,SAAS;4BACT,WAAU;sCACX;;;;;;sCAID,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAM,WAAU;8CAAiC;;;;;;8CAClD,8OAAC;oCACC,MAAK;oCACL,OAAO;oCACP,UAAU,CAAC,IAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;oCACvC,WAAU;oCACV,aAAY;;;;;;;;;;;;sCAIhB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAM,WAAU;8CAAiC;;;;;;8CAClD,8OAAC;oCACC,MAAK;oCACL,OAAO;oCACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;oCACxC,WAAU;oCACV,aAAY;;;;;;;;;;;;sCAIhB,8OAAC;4BACC,SAAS;4BACT,WAAU;sCACX;;;;;;wBAIA,yBACC,8OAAC;4BAAI,WAAU;sCACZ;;;;;;;;;;;;8BAKP,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA6B;;;;;;sCAC3C,8OAAC;4BACC,SAAS,IAAM,CAAA,GAAA,8IAAA,CAAA,SAAM,AAAD,EAAE;4BACtB,WAAU;sCACX;;;;;;sCAGD,8OAAC;4BACC,SAAS,IAAM,CAAA,GAAA,8IAAA,CAAA,SAAM,AAAD,EAAE;4BACtB,WAAU;sCACX;;;;;;;;;;;;;;;;;;;;;;;AAOX", "debugId": null}}]}
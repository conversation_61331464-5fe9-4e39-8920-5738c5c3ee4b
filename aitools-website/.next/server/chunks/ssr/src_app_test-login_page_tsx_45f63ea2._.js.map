{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/app/test-login/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { signIn, useSession } from 'next-auth/react';\n\nexport default function TestLoginPage() {\n  const { data: session, status } = useSession();\n  const [email, setEmail] = useState('<EMAIL>');\n  const [code, setCode] = useState('');\n  const [token, setToken] = useState('');\n  const [loading, setLoading] = useState(false);\n\n  const sendCode = async () => {\n    setLoading(true);\n    try {\n      const response = await fetch('/api/auth/send-code', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({ email }),\n      });\n      const data = await response.json();\n      if (data.success) {\n        setToken(data.token);\n        alert('验证码已发送！Token: ' + data.token);\n      } else {\n        alert('发送失败: ' + data.error);\n      }\n    } catch (error) {\n      alert('网络错误');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const verifyAndLogin = async () => {\n    if (!code || !token) {\n      alert('请先发送验证码并输入验证码');\n      return;\n    }\n\n    setLoading(true);\n    try {\n      // 使用自定义登录API\n      const response = await fetch('/api/auth/custom-login', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({ email, code, token }),\n      });\n      const data = await response.json();\n\n      if (data.success) {\n        alert('登录成功！');\n        // 刷新页面以更新session\n        window.location.reload();\n      } else {\n        alert('验证失败: ' + data.error);\n      }\n    } catch (error) {\n      alert('网络错误: ' + error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const testCredentials = async () => {\n    console.log('开始测试Credentials provider...');\n    try {\n      const result = await signIn('credentials', {\n        username: 'test',\n        password: 'test',\n        redirect: false,\n      });\n      console.log('SignIn result:', result);\n      alert('测试结果: ' + JSON.stringify(result));\n    } catch (error) {\n      console.error('测试错误:', error);\n      alert('测试错误: ' + error);\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-100 py-12 px-4\">\n      <div className=\"max-w-md mx-auto bg-white rounded-lg shadow-md p-6\">\n        <h1 className=\"text-2xl font-bold mb-6\">测试邮件验证码登录</h1>\n        \n        <div className=\"space-y-4\">\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              邮箱地址\n            </label>\n            <input\n              type=\"email\"\n              value={email}\n              onChange={(e) => setEmail(e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              placeholder=\"输入邮箱地址\"\n            />\n          </div>\n\n          <button\n            onClick={sendCode}\n            disabled={loading || !email}\n            className=\"w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 disabled:opacity-50\"\n          >\n            {loading ? '发送中...' : '发送验证码'}\n          </button>\n\n          {token && (\n            <div className=\"p-3 bg-gray-100 rounded-md\">\n              <p className=\"text-sm text-gray-600\">Token: {token}</p>\n            </div>\n          )}\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              验证码\n            </label>\n            <input\n              type=\"text\"\n              value={code}\n              onChange={(e) => setCode(e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              placeholder=\"输入6位验证码\"\n              maxLength={6}\n            />\n          </div>\n\n          <button\n            onClick={verifyAndLogin}\n            disabled={loading || !code || !token}\n            className=\"w-full bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700 disabled:opacity-50\"\n          >\n            {loading ? '验证中...' : '验证并登录'}\n          </button>\n\n          <button\n            onClick={testCredentials}\n            className=\"w-full bg-purple-600 text-white py-2 px-4 rounded-md hover:bg-purple-700\"\n          >\n            测试Credentials Provider\n          </button>\n        </div>\n\n        <div className=\"mt-8 p-4 bg-gray-50 rounded-md\">\n          <h2 className=\"text-lg font-semibold mb-2\">当前登录状态</h2>\n          <p className=\"text-sm text-gray-600\">状态: {status}</p>\n          {session ? (\n            <div className=\"mt-2\">\n              <p className=\"text-sm\"><strong>用户ID:</strong> {session.user?.id}</p>\n              <p className=\"text-sm\"><strong>邮箱:</strong> {session.user?.email}</p>\n              <p className=\"text-sm\"><strong>姓名:</strong> {session.user?.name}</p>\n              <p className=\"text-sm\"><strong>角色:</strong> {session.user?.role}</p>\n            </div>\n          ) : (\n            <p className=\"text-sm text-gray-500\">未登录</p>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKe,SAAS;IACtB,MAAM,EAAE,MAAM,OAAO,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,aAAU,AAAD;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,WAAW;QACf,WAAW;QACX,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,uBAAuB;gBAClD,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBAAE;gBAAM;YAC/B;YACA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,IAAI,KAAK,OAAO,EAAE;gBAChB,SAAS,KAAK,KAAK;gBACnB,MAAM,mBAAmB,KAAK,KAAK;YACrC,OAAO;gBACL,MAAM,WAAW,KAAK,KAAK;YAC7B;QACF,EAAE,OAAO,OAAO;YACd,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,iBAAiB;QACrB,IAAI,CAAC,QAAQ,CAAC,OAAO;YACnB,MAAM;YACN;QACF;QAEA,WAAW;QACX,IAAI;YACF,aAAa;YACb,MAAM,WAAW,MAAM,MAAM,0BAA0B;gBACrD,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBAAE;oBAAO;oBAAM;gBAAM;YAC5C;YACA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,MAAM;gBACN,iBAAiB;gBACjB,OAAO,QAAQ,CAAC,MAAM;YACxB,OAAO;gBACL,MAAM,WAAW,KAAK,KAAK;YAC7B;QACF,EAAE,OAAO,OAAO;YACd,MAAM,WAAW;QACnB,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,kBAAkB;QACtB,QAAQ,GAAG,CAAC;QACZ,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,8IAAA,CAAA,SAAM,AAAD,EAAE,eAAe;gBACzC,UAAU;gBACV,UAAU;gBACV,UAAU;YACZ;YACA,QAAQ,GAAG,CAAC,kBAAkB;YAC9B,MAAM,WAAW,KAAK,SAAS,CAAC;QAClC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,SAAS;YACvB,MAAM,WAAW;QACnB;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,WAAU;8BAA0B;;;;;;8BAExC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,8OAAC;oCACC,MAAK;oCACL,OAAO;oCACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;oCACxC,WAAU;oCACV,aAAY;;;;;;;;;;;;sCAIhB,8OAAC;4BACC,SAAS;4BACT,UAAU,WAAW,CAAC;4BACtB,WAAU;sCAET,UAAU,WAAW;;;;;;wBAGvB,uBACC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;;oCAAwB;oCAAQ;;;;;;;;;;;;sCAIjD,8OAAC;;8CACC,8OAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,8OAAC;oCACC,MAAK;oCACL,OAAO;oCACP,UAAU,CAAC,IAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;oCACvC,WAAU;oCACV,aAAY;oCACZ,WAAW;;;;;;;;;;;;sCAIf,8OAAC;4BACC,SAAS;4BACT,UAAU,WAAW,CAAC,QAAQ,CAAC;4BAC/B,WAAU;sCAET,UAAU,WAAW;;;;;;sCAGxB,8OAAC;4BACC,SAAS;4BACT,WAAU;sCACX;;;;;;;;;;;;8BAKH,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA6B;;;;;;sCAC3C,8OAAC;4BAAE,WAAU;;gCAAwB;gCAAK;;;;;;;wBACzC,wBACC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;;sDAAU,8OAAC;sDAAO;;;;;;wCAAc;wCAAE,QAAQ,IAAI,EAAE;;;;;;;8CAC7D,8OAAC;oCAAE,WAAU;;sDAAU,8OAAC;sDAAO;;;;;;wCAAY;wCAAE,QAAQ,IAAI,EAAE;;;;;;;8CAC3D,8OAAC;oCAAE,WAAU;;sDAAU,8OAAC;sDAAO;;;;;;wCAAY;wCAAE,QAAQ,IAAI,EAAE;;;;;;;8CAC3D,8OAAC;oCAAE,WAAU;;sDAAU,8OAAC;sDAAO;;;;;;wCAAY;wCAAE,QAAQ,IAAI,EAAE;;;;;;;;;;;;iDAG7D,8OAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;;;;;;;;;;;;AAMjD", "debugId": null}}]}
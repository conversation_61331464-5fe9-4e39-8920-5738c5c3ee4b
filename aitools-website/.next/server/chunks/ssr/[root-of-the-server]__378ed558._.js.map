{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/Layout.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Link from 'next/link';\nimport { Search, Menu, X } from 'lucide-react';\n\ninterface LayoutProps {\n  children: React.ReactNode;\n}\n\nconst Layout: React.FC<LayoutProps> = ({ children }) => {\n  const [isMenuOpen, setIsMenuOpen] = React.useState(false);\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header */}\n      <header className=\"bg-white shadow-sm border-b border-gray-200\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center h-16\">\n            {/* Logo */}\n            <div className=\"flex items-center\">\n              <Link href=\"/\" className=\"flex items-center space-x-2\">\n                <div className=\"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center\">\n                  <span className=\"text-white font-bold text-sm\">AI</span>\n                </div>\n                <span className=\"text-xl font-bold text-gray-900\">AI Tools</span>\n              </Link>\n            </div>\n\n            {/* Desktop Navigation */}\n            <nav className=\"hidden md:flex items-center space-x-8\">\n              <Link href=\"/\" className=\"text-gray-700 hover:text-blue-600 transition-colors\">\n                首页\n              </Link>\n              <Link href=\"/tools\" className=\"text-gray-700 hover:text-blue-600 transition-colors\">\n                工具目录\n              </Link>\n              <Link href=\"/categories\" className=\"text-gray-700 hover:text-blue-600 transition-colors\">\n                分类\n              </Link>\n              <Link href=\"/submit\" className=\"text-gray-700 hover:text-blue-600 transition-colors\">\n                提交工具\n              </Link>\n              <Link href=\"/dashboard\" className=\"text-gray-700 hover:text-blue-600 transition-colors\">\n                仪表板\n              </Link>\n            </nav>\n\n            {/* Search Bar */}\n            <div className=\"hidden md:flex items-center flex-1 max-w-md mx-8\">\n              <div className=\"relative w-full\">\n                <input\n                  type=\"text\"\n                  placeholder=\"搜索 AI 工具...\"\n                  className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                />\n                <Search className=\"absolute left-3 top-2.5 h-5 w-5 text-gray-400\" />\n              </div>\n            </div>\n\n            {/* Mobile menu button */}\n            <div className=\"md:hidden\">\n              <button\n                onClick={() => setIsMenuOpen(!isMenuOpen)}\n                className=\"text-gray-700 hover:text-blue-600\"\n              >\n                {isMenuOpen ? <X className=\"h-6 w-6\" /> : <Menu className=\"h-6 w-6\" />}\n              </button>\n            </div>\n          </div>\n        </div>\n\n        {/* Mobile Navigation */}\n        {isMenuOpen && (\n          <div className=\"md:hidden bg-white border-t border-gray-200\">\n            <div className=\"px-4 py-2 space-y-1\">\n              <Link\n                href=\"/\"\n                className=\"block px-3 py-2 text-gray-700 hover:text-blue-600 hover:bg-gray-50 rounded-md\"\n              >\n                首页\n              </Link>\n              <Link\n                href=\"/tools\"\n                className=\"block px-3 py-2 text-gray-700 hover:text-blue-600 hover:bg-gray-50 rounded-md\"\n              >\n                工具目录\n              </Link>\n              <Link\n                href=\"/categories\"\n                className=\"block px-3 py-2 text-gray-700 hover:text-blue-600 hover:bg-gray-50 rounded-md\"\n              >\n                分类\n              </Link>\n              <Link\n                href=\"/submit\"\n                className=\"block px-3 py-2 text-gray-700 hover:text-blue-600 hover:bg-gray-50 rounded-md\"\n              >\n                提交工具\n              </Link>\n              <Link\n                href=\"/dashboard\"\n                className=\"block px-3 py-2 text-gray-700 hover:text-blue-600 hover:bg-gray-50 rounded-md\"\n              >\n                仪表板\n              </Link>\n              {/* Mobile Search */}\n              <div className=\"px-3 py-2\">\n                <div className=\"relative\">\n                  <input\n                    type=\"text\"\n                    placeholder=\"搜索 AI 工具...\"\n                    className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                  />\n                  <Search className=\"absolute left-3 top-2.5 h-5 w-5 text-gray-400\" />\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n      </header>\n\n      {/* Main Content */}\n      <main className=\"flex-1\">\n        {children}\n      </main>\n\n      {/* Footer */}\n      <footer className=\"bg-white border-t border-gray-200 mt-16\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n          <div className=\"grid grid-cols-1 md:grid-cols-4 gap-8\">\n            <div className=\"col-span-1 md:col-span-2\">\n              <div className=\"flex items-center space-x-2 mb-4\">\n                <div className=\"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center\">\n                  <span className=\"text-white font-bold text-sm\">AI</span>\n                </div>\n                <span className=\"text-xl font-bold text-gray-900\">AI Tools</span>\n              </div>\n              <p className=\"text-gray-600 mb-4\">\n                发现最新最好的 AI 工具，提升您的工作效率和创造力。\n              </p>\n            </div>\n            \n            <div>\n              <h3 className=\"text-sm font-semibold text-gray-900 tracking-wider uppercase mb-4\">\n                快速链接\n              </h3>\n              <ul className=\"space-y-2\">\n                <li>\n                  <Link href=\"/tools\" className=\"text-gray-600 hover:text-blue-600\">\n                    工具目录\n                  </Link>\n                </li>\n                <li>\n                  <Link href=\"/categories\" className=\"text-gray-600 hover:text-blue-600\">\n                    分类浏览\n                  </Link>\n                </li>\n                <li>\n                  <Link href=\"/submit\" className=\"text-gray-600 hover:text-blue-600\">\n                    提交工具\n                  </Link>\n                </li>\n              </ul>\n            </div>\n            \n            <div>\n              <h3 className=\"text-sm font-semibold text-gray-900 tracking-wider uppercase mb-4\">\n                支持\n              </h3>\n              <ul className=\"space-y-2\">\n                <li>\n                  <a href=\"#\" className=\"text-gray-600 hover:text-blue-600\">\n                    帮助中心\n                  </a>\n                </li>\n                <li>\n                  <a href=\"#\" className=\"text-gray-600 hover:text-blue-600\">\n                    联系我们\n                  </a>\n                </li>\n                <li>\n                  <a href=\"#\" className=\"text-gray-600 hover:text-blue-600\">\n                    隐私政策\n                  </a>\n                </li>\n              </ul>\n            </div>\n          </div>\n          \n          <div className=\"border-t border-gray-200 mt-8 pt-8\">\n            <p className=\"text-center text-gray-600\">\n              © 2024 AI Tools Directory. All rights reserved.\n            </p>\n          </div>\n        </div>\n      </footer>\n    </div>\n  );\n};\n\nexport default Layout;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAJA;;;;;AAUA,MAAM,SAAgC,CAAC,EAAE,QAAQ,EAAE;IACjD,MAAM,CAAC,YAAY,cAAc,GAAG,qMAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IAEnD,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;;kCAChB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAI,WAAU;;0DACvB,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAK,WAAU;8DAA+B;;;;;;;;;;;0DAEjD,8OAAC;gDAAK,WAAU;0DAAkC;;;;;;;;;;;;;;;;;8CAKtD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAI,WAAU;sDAAsD;;;;;;sDAG/E,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAS,WAAU;sDAAsD;;;;;;sDAGpF,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAc,WAAU;sDAAsD;;;;;;sDAGzF,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAU,WAAU;sDAAsD;;;;;;sDAGrF,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAa,WAAU;sDAAsD;;;;;;;;;;;;8CAM1F,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,MAAK;gDACL,aAAY;gDACZ,WAAU;;;;;;0DAEZ,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;;;;;;;;;;;;8CAKtB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCACC,SAAS,IAAM,cAAc,CAAC;wCAC9B,WAAU;kDAET,2BAAa,8OAAC,4LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;iEAAe,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAOjE,4BACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAID,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,MAAK;gDACL,aAAY;gDACZ,WAAU;;;;;;0DAEZ,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS9B,8OAAC;gBAAK,WAAU;0BACb;;;;;;0BAIH,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAK,WAAU;kEAA+B;;;;;;;;;;;8DAEjD,8OAAC;oDAAK,WAAU;8DAAkC;;;;;;;;;;;;sDAEpD,8OAAC;4CAAE,WAAU;sDAAqB;;;;;;;;;;;;8CAKpC,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAoE;;;;;;sDAGlF,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;8DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAS,WAAU;kEAAoC;;;;;;;;;;;8DAIpE,8OAAC;8DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAc,WAAU;kEAAoC;;;;;;;;;;;8DAIzE,8OAAC;8DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAU,WAAU;kEAAoC;;;;;;;;;;;;;;;;;;;;;;;8CAOzE,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAoE;;;;;;sDAGlF,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;8DACC,cAAA,8OAAC;wDAAE,MAAK;wDAAI,WAAU;kEAAoC;;;;;;;;;;;8DAI5D,8OAAC;8DACC,cAAA,8OAAC;wDAAE,MAAK;wDAAI,WAAU;kEAAoC;;;;;;;;;;;8DAI5D,8OAAC;8DACC,cAAA,8OAAC;wDAAE,MAAK;wDAAI,WAAU;kEAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQlE,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;0CAA4B;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQrD;uCAEe", "debugId": null}}, {"offset": {"line": 581, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/ToolCard.tsx"], "sourcesContent": ["import React from 'react';\nimport Link from 'next/link';\nimport { ExternalLink, Heart, Eye, Star } from 'lucide-react';\n\ninterface ToolCardProps {\n  tool: {\n    _id: string;\n    name: string;\n    description: string;\n    website: string;\n    logo?: string;\n    category: string;\n    tags: string[];\n    pricing: 'free' | 'freemium' | 'paid';\n    views: number;\n    likes: number;\n  };\n}\n\nconst ToolCard: React.FC<ToolCardProps> = ({ tool }) => {\n  const getPricingColor = (pricing: string) => {\n    switch (pricing) {\n      case 'free':\n        return 'bg-green-100 text-green-800';\n      case 'freemium':\n        return 'bg-blue-100 text-blue-800';\n      case 'paid':\n        return 'bg-orange-100 text-orange-800';\n      default:\n        return 'bg-gray-100 text-gray-800';\n    }\n  };\n\n  const getPricingText = (pricing: string) => {\n    switch (pricing) {\n      case 'free':\n        return '免费';\n      case 'freemium':\n        return '免费增值';\n      case 'paid':\n        return '付费';\n      default:\n        return pricing;\n    }\n  };\n\n  return (\n    <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow duration-200\">\n      <div className=\"p-6\">\n        {/* Header */}\n        <div className=\"flex items-start justify-between mb-4\">\n          <div className=\"flex items-center space-x-3\">\n            {tool.logo ? (\n              <img\n                src={tool.logo}\n                alt={tool.name}\n                className=\"w-12 h-12 rounded-lg object-cover\"\n              />\n            ) : (\n              <div className=\"w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center\">\n                <span className=\"text-white font-bold text-lg\">\n                  {tool.name.charAt(0).toUpperCase()}\n                </span>\n              </div>\n            )}\n            <div>\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-1\">\n                {tool.name}\n              </h3>\n              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getPricingColor(tool.pricing)}`}>\n                {getPricingText(tool.pricing)}\n              </span>\n            </div>\n          </div>\n          \n          <a\n            href={tool.website}\n            target=\"_blank\"\n            rel=\"noopener noreferrer\"\n            className=\"text-gray-400 hover:text-blue-600 transition-colors\"\n          >\n            <ExternalLink className=\"h-5 w-5\" />\n          </a>\n        </div>\n\n        {/* Description */}\n        <p className=\"text-gray-600 text-sm mb-4 line-clamp-2\">\n          {tool.description}\n        </p>\n\n        {/* Tags */}\n        <div className=\"flex flex-wrap gap-2 mb-4\">\n          {tool.tags.slice(0, 3).map((tag, index) => (\n            <span\n              key={index}\n              className=\"inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-700\"\n            >\n              {tag}\n            </span>\n          ))}\n          {tool.tags.length > 3 && (\n            <span className=\"inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-700\">\n              +{tool.tags.length - 3}\n            </span>\n          )}\n        </div>\n\n        {/* Stats and Actions */}\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center space-x-4 text-sm text-gray-500\">\n            <div className=\"flex items-center space-x-1\">\n              <Eye className=\"h-4 w-4\" />\n              <span>{tool.views}</span>\n            </div>\n            <div className=\"flex items-center space-x-1\">\n              <Heart className=\"h-4 w-4\" />\n              <span>{tool.likes}</span>\n            </div>\n          </div>\n          \n          <div className=\"flex items-center space-x-2\">\n            <button className=\"text-gray-400 hover:text-red-500 transition-colors\">\n              <Heart className=\"h-5 w-5\" />\n            </button>\n            <Link\n              href={`/tools/${tool._id}`}\n              className=\"inline-flex items-center px-3 py-1.5 border border-transparent text-sm font-medium rounded-md text-blue-600 bg-blue-50 hover:bg-blue-100 transition-colors\"\n            >\n              查看详情\n            </Link>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ToolCard;\n"], "names": [], "mappings": ";;;;AACA;AACA;AAAA;AAAA;;;;AAiBA,MAAM,WAAoC,CAAC,EAAE,IAAI,EAAE;IACjD,MAAM,kBAAkB,CAAC;QACvB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;gCACZ,KAAK,IAAI,iBACR,8OAAC;oCACC,KAAK,KAAK,IAAI;oCACd,KAAK,KAAK,IAAI;oCACd,WAAU;;;;;yDAGZ,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAK,WAAU;kDACb,KAAK,IAAI,CAAC,MAAM,CAAC,GAAG,WAAW;;;;;;;;;;;8CAItC,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDACX,KAAK,IAAI;;;;;;sDAEZ,8OAAC;4CAAK,WAAW,CAAC,wEAAwE,EAAE,gBAAgB,KAAK,OAAO,GAAG;sDACxH,eAAe,KAAK,OAAO;;;;;;;;;;;;;;;;;;sCAKlC,8OAAC;4BACC,MAAM,KAAK,OAAO;4BAClB,QAAO;4BACP,KAAI;4BACJ,WAAU;sCAEV,cAAA,8OAAC,sNAAA,CAAA,eAAY;gCAAC,WAAU;;;;;;;;;;;;;;;;;8BAK5B,8OAAC;oBAAE,WAAU;8BACV,KAAK,WAAW;;;;;;8BAInB,8OAAC;oBAAI,WAAU;;wBACZ,KAAK,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,KAAK,sBAC/B,8OAAC;gCAEC,WAAU;0CAET;+BAHI;;;;;wBAMR,KAAK,IAAI,CAAC,MAAM,GAAG,mBAClB,8OAAC;4BAAK,WAAU;;gCAA8F;gCAC1G,KAAK,IAAI,CAAC,MAAM,GAAG;;;;;;;;;;;;;8BAM3B,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,gMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;sDACf,8OAAC;sDAAM,KAAK,KAAK;;;;;;;;;;;;8CAEnB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;sDACjB,8OAAC;sDAAM,KAAK,KAAK;;;;;;;;;;;;;;;;;;sCAIrB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAO,WAAU;8CAChB,cAAA,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;;;;;;8CAEnB,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAM,CAAC,OAAO,EAAE,KAAK,GAAG,EAAE;oCAC1B,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb;uCAEe", "debugId": null}}, {"offset": {"line": 855, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/app/categories/%5Bslug%5D/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport Link from 'next/link';\nimport Layout from '@/components/Layout';\nimport ToolCard from '@/components/ToolCard';\nimport { ArrowLeft, Filter, Grid, List, ChevronDown } from 'lucide-react';\n\n// Mock data - 在实际应用中这些数据会从 API 获取\nconst categoryData = {\n  _id: '1',\n  name: '文本生成',\n  slug: 'text-generation',\n  description: 'AI tools for generating and editing text content, including chatbots, writing assistants, and content creation tools.',\n  icon: '📝',\n  color: '#3B82F6',\n  toolCount: 25\n};\n\nconst categoryTools = [\n  {\n    _id: '1',\n    name: 'ChatGPT',\n    description: 'Advanced AI chatbot for conversations and text generation',\n    website: 'https://chat.openai.com',\n    category: 'text-generation',\n    tags: ['chatbot', 'conversation', 'writing'],\n    pricing: 'freemium' as const,\n    views: 1250,\n    likes: 89\n  },\n  {\n    _id: '5',\n    name: '<PERSON>',\n    description: 'AI writing assistant for marketing copy and content creation',\n    website: 'https://jasper.ai',\n    category: 'text-generation',\n    tags: ['writing', 'marketing', 'content'],\n    pricing: 'paid' as const,\n    views: 634,\n    likes: 52\n  },\n  {\n    _id: '7',\n    name: 'Copy.ai',\n    description: 'AI-powered copywriting tool for marketing and sales content',\n    website: 'https://copy.ai',\n    category: 'text-generation',\n    tags: ['copywriting', 'marketing', 'sales'],\n    pricing: 'freemium' as const,\n    views: 445,\n    likes: 38\n  },\n  {\n    _id: '8',\n    name: 'Writesonic',\n    description: 'AI writing platform for articles, ads, and marketing copy',\n    website: 'https://writesonic.com',\n    category: 'text-generation',\n    tags: ['writing', 'articles', 'marketing'],\n    pricing: 'freemium' as const,\n    views: 567,\n    likes: 43\n  },\n  {\n    _id: '9',\n    name: 'Grammarly',\n    description: 'AI-powered writing assistant for grammar and style checking',\n    website: 'https://grammarly.com',\n    category: 'text-generation',\n    tags: ['grammar', 'writing', 'editing'],\n    pricing: 'freemium' as const,\n    views: 892,\n    likes: 76\n  }\n];\n\nconst pricingOptions = [\n  { value: '', label: '所有价格' },\n  { value: 'free', label: '免费' },\n  { value: 'freemium', label: '免费增值' },\n  { value: 'paid', label: '付费' }\n];\n\nconst sortOptions = [\n  { value: 'popular', label: '最受欢迎' },\n  { value: 'newest', label: '最新添加' },\n  { value: 'name', label: '名称排序' },\n  { value: 'views', label: '浏览量' }\n];\n\ninterface CategoryPageProps {\n  params: {\n    slug: string;\n  };\n}\n\nexport default function CategoryPage({ params }: CategoryPageProps) {\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedPricing, setSelectedPricing] = useState('');\n  const [sortBy, setSortBy] = useState('popular');\n  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');\n  const [showFilters, setShowFilters] = useState(false);\n\n  // Filter and sort tools\n  const filteredTools = categoryTools.filter(tool => {\n    const matchesSearch = tool.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         tool.description.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         tool.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));\n    const matchesPricing = !selectedPricing || tool.pricing === selectedPricing;\n    \n    return matchesSearch && matchesPricing;\n  });\n\n  const sortedTools = [...filteredTools].sort((a, b) => {\n    switch (sortBy) {\n      case 'popular':\n        return b.likes - a.likes;\n      case 'views':\n        return b.views - a.views;\n      case 'name':\n        return a.name.localeCompare(b.name);\n      case 'newest':\n        return 0; // 在实际应用中会根据创建日期排序\n      default:\n        return 0;\n    }\n  });\n\n  return (\n    <Layout>\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {/* Breadcrumb */}\n        <div className=\"flex items-center space-x-2 text-sm text-gray-500 mb-6\">\n          <Link href=\"/\" className=\"hover:text-blue-600\">首页</Link>\n          <span>/</span>\n          <Link href=\"/categories\" className=\"hover:text-blue-600\">分类</Link>\n          <span>/</span>\n          <span className=\"text-gray-900\">{categoryData.name}</span>\n        </div>\n\n        {/* Back Button */}\n        <div className=\"mb-6\">\n          <Link\n            href=\"/categories\"\n            className=\"inline-flex items-center text-blue-600 hover:text-blue-700\"\n          >\n            <ArrowLeft className=\"mr-2 h-4 w-4\" />\n            返回分类列表\n          </Link>\n        </div>\n\n        {/* Category Header */}\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-8 mb-8\">\n          <div className=\"flex items-center space-x-4 mb-4\">\n            <div \n              className=\"w-16 h-16 rounded-lg flex items-center justify-center text-3xl\"\n              style={{ backgroundColor: categoryData.color }}\n            >\n              <span className=\"text-white\">\n                {categoryData.icon}\n              </span>\n            </div>\n            <div>\n              <h1 className=\"text-3xl font-bold text-gray-900 mb-2\">\n                {categoryData.name}\n              </h1>\n              <p className=\"text-lg text-gray-600\">\n                {categoryData.toolCount} 个工具\n              </p>\n            </div>\n          </div>\n          <p className=\"text-gray-600 leading-relaxed\">\n            {categoryData.description}\n          </p>\n        </div>\n\n        {/* Search and Filters */}\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8\">\n          {/* Search Bar */}\n          <div className=\"relative mb-4\">\n            <input\n              type=\"text\"\n              placeholder=\"在此分类中搜索工具...\"\n              value={searchTerm}\n              onChange={(e) => setSearchTerm(e.target.value)}\n              className=\"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            />\n            <Filter className=\"absolute left-3 top-3 h-5 w-5 text-gray-400\" />\n          </div>\n\n          {/* Filter Toggle Button (Mobile) */}\n          <div className=\"md:hidden mb-4\">\n            <button\n              onClick={() => setShowFilters(!showFilters)}\n              className=\"flex items-center justify-center w-full px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50\"\n            >\n              <Filter className=\"mr-2 h-4 w-4\" />\n              筛选选项\n              <ChevronDown className={`ml-2 h-4 w-4 transform ${showFilters ? 'rotate-180' : ''}`} />\n            </button>\n          </div>\n\n          {/* Filters */}\n          <div className={`grid grid-cols-1 md:grid-cols-3 gap-4 ${showFilters ? 'block' : 'hidden md:grid'}`}>\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">价格</label>\n              <select\n                value={selectedPricing}\n                onChange={(e) => setSelectedPricing(e.target.value)}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              >\n                {pricingOptions.map(option => (\n                  <option key={option.value} value={option.value}>\n                    {option.label}\n                  </option>\n                ))}\n              </select>\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">排序</label>\n              <select\n                value={sortBy}\n                onChange={(e) => setSortBy(e.target.value)}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              >\n                {sortOptions.map(option => (\n                  <option key={option.value} value={option.value}>\n                    {option.label}\n                  </option>\n                ))}\n              </select>\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">视图</label>\n              <div className=\"flex rounded-lg border border-gray-300\">\n                <button\n                  onClick={() => setViewMode('grid')}\n                  className={`flex-1 px-3 py-2 text-sm font-medium rounded-l-lg ${\n                    viewMode === 'grid'\n                      ? 'bg-blue-600 text-white'\n                      : 'bg-white text-gray-700 hover:bg-gray-50'\n                  }`}\n                >\n                  <Grid className=\"h-4 w-4 mx-auto\" />\n                </button>\n                <button\n                  onClick={() => setViewMode('list')}\n                  className={`flex-1 px-3 py-2 text-sm font-medium rounded-r-lg ${\n                    viewMode === 'list'\n                      ? 'bg-blue-600 text-white'\n                      : 'bg-white text-gray-700 hover:bg-gray-50'\n                  }`}\n                >\n                  <List className=\"h-4 w-4 mx-auto\" />\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Results */}\n        <div className=\"mb-6\">\n          <p className=\"text-gray-600\">\n            显示 {sortedTools.length} 个结果\n            {searchTerm && ` 搜索 \"${searchTerm}\"`}\n          </p>\n        </div>\n\n        {/* Tools Grid/List */}\n        {sortedTools.length > 0 ? (\n          <div className={viewMode === 'grid' \n            ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'\n            : 'space-y-4'\n          }>\n            {sortedTools.map((tool) => (\n              <ToolCard key={tool._id} tool={tool} />\n            ))}\n          </div>\n        ) : (\n          <div className=\"text-center py-12\">\n            <div className=\"text-gray-400 mb-4\">\n              <Filter className=\"h-12 w-12 mx-auto\" />\n            </div>\n            <h3 className=\"text-lg font-medium text-gray-900 mb-2\">未找到匹配的工具</h3>\n            <p className=\"text-gray-600\">\n              尝试调整搜索条件或筛选选项\n            </p>\n          </div>\n        )}\n\n        {/* Related Categories */}\n        <section className=\"mt-16 bg-gray-50 rounded-lg p-8\">\n          <h2 className=\"text-2xl font-bold text-gray-900 mb-6\">相关分类</h2>\n          <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4\">\n            <Link\n              href=\"/categories/image-generation\"\n              className=\"flex items-center space-x-2 p-3 bg-white rounded-lg hover:shadow-sm transition-shadow\"\n            >\n              <span className=\"text-2xl\">🎨</span>\n              <span className=\"text-sm font-medium text-gray-900\">图像生成</span>\n            </Link>\n            <Link\n              href=\"/categories/code-generation\"\n              className=\"flex items-center space-x-2 p-3 bg-white rounded-lg hover:shadow-sm transition-shadow\"\n            >\n              <span className=\"text-2xl\">💻</span>\n              <span className=\"text-sm font-medium text-gray-900\">代码生成</span>\n            </Link>\n            <Link\n              href=\"/categories/data-analysis\"\n              className=\"flex items-center space-x-2 p-3 bg-white rounded-lg hover:shadow-sm transition-shadow\"\n            >\n              <span className=\"text-2xl\">📊</span>\n              <span className=\"text-sm font-medium text-gray-900\">数据分析</span>\n            </Link>\n            <Link\n              href=\"/categories/audio-processing\"\n              className=\"flex items-center space-x-2 p-3 bg-white rounded-lg hover:shadow-sm transition-shadow\"\n            >\n              <span className=\"text-2xl\">🎵</span>\n              <span className=\"text-sm font-medium text-gray-900\">音频处理</span>\n            </Link>\n          </div>\n        </section>\n      </div>\n    </Layout>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AANA;;;;;;;AAQA,kCAAkC;AAClC,MAAM,eAAe;IACnB,KAAK;IACL,MAAM;IACN,MAAM;IACN,aAAa;IACb,MAAM;IACN,OAAO;IACP,WAAW;AACb;AAEA,MAAM,gBAAgB;IACpB;QACE,KAAK;QACL,MAAM;QACN,aAAa;QACb,SAAS;QACT,UAAU;QACV,MAAM;YAAC;YAAW;YAAgB;SAAU;QAC5C,SAAS;QACT,OAAO;QACP,OAAO;IACT;IACA;QACE,KAAK;QACL,MAAM;QACN,aAAa;QACb,SAAS;QACT,UAAU;QACV,MAAM;YAAC;YAAW;YAAa;SAAU;QACzC,SAAS;QACT,OAAO;QACP,OAAO;IACT;IACA;QACE,KAAK;QACL,MAAM;QACN,aAAa;QACb,SAAS;QACT,UAAU;QACV,MAAM;YAAC;YAAe;YAAa;SAAQ;QAC3C,SAAS;QACT,OAAO;QACP,OAAO;IACT;IACA;QACE,KAAK;QACL,MAAM;QACN,aAAa;QACb,SAAS;QACT,UAAU;QACV,MAAM;YAAC;YAAW;YAAY;SAAY;QAC1C,SAAS;QACT,OAAO;QACP,OAAO;IACT;IACA;QACE,KAAK;QACL,MAAM;QACN,aAAa;QACb,SAAS;QACT,UAAU;QACV,MAAM;YAAC;YAAW;YAAW;SAAU;QACvC,SAAS;QACT,OAAO;QACP,OAAO;IACT;CACD;AAED,MAAM,iBAAiB;IACrB;QAAE,OAAO;QAAI,OAAO;IAAO;IAC3B;QAAE,OAAO;QAAQ,OAAO;IAAK;IAC7B;QAAE,OAAO;QAAY,OAAO;IAAO;IACnC;QAAE,OAAO;QAAQ,OAAO;IAAK;CAC9B;AAED,MAAM,cAAc;IAClB;QAAE,OAAO;QAAW,OAAO;IAAO;IAClC;QAAE,OAAO;QAAU,OAAO;IAAO;IACjC;QAAE,OAAO;QAAQ,OAAO;IAAO;IAC/B;QAAE,OAAO;QAAS,OAAO;IAAM;CAChC;AAQc,SAAS,aAAa,EAAE,MAAM,EAAqB;IAChE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB;IAC1D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,wBAAwB;IACxB,MAAM,gBAAgB,cAAc,MAAM,CAAC,CAAA;QACzC,MAAM,gBAAgB,KAAK,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACxD,KAAK,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC9D,KAAK,IAAI,CAAC,IAAI,CAAC,CAAA,MAAO,IAAI,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;QAC5F,MAAM,iBAAiB,CAAC,mBAAmB,KAAK,OAAO,KAAK;QAE5D,OAAO,iBAAiB;IAC1B;IAEA,MAAM,cAAc;WAAI;KAAc,CAAC,IAAI,CAAC,CAAC,GAAG;QAC9C,OAAQ;YACN,KAAK;gBACH,OAAO,EAAE,KAAK,GAAG,EAAE,KAAK;YAC1B,KAAK;gBACH,OAAO,EAAE,KAAK,GAAG,EAAE,KAAK;YAC1B,KAAK;gBACH,OAAO,EAAE,IAAI,CAAC,aAAa,CAAC,EAAE,IAAI;YACpC,KAAK;gBACH,OAAO,GAAG,kBAAkB;YAC9B;gBACE,OAAO;QACX;IACF;IAEA,qBACE,8OAAC,4HAAA,CAAA,UAAM;kBACL,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;sCAAsB;;;;;;sCAC/C,8OAAC;sCAAK;;;;;;sCACN,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAc,WAAU;sCAAsB;;;;;;sCACzD,8OAAC;sCAAK;;;;;;sCACN,8OAAC;4BAAK,WAAU;sCAAiB,aAAa,IAAI;;;;;;;;;;;;8BAIpD,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;wBACH,MAAK;wBACL,WAAU;;0CAEV,8OAAC,gNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;8BAM1C,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,WAAU;oCACV,OAAO;wCAAE,iBAAiB,aAAa,KAAK;oCAAC;8CAE7C,cAAA,8OAAC;wCAAK,WAAU;kDACb,aAAa,IAAI;;;;;;;;;;;8CAGtB,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDACX,aAAa,IAAI;;;;;;sDAEpB,8OAAC;4CAAE,WAAU;;gDACV,aAAa,SAAS;gDAAC;;;;;;;;;;;;;;;;;;;sCAI9B,8OAAC;4BAAE,WAAU;sCACV,aAAa,WAAW;;;;;;;;;;;;8BAK7B,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,MAAK;oCACL,aAAY;oCACZ,OAAO;oCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;oCAC7C,WAAU;;;;;;8CAEZ,8OAAC,sMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;;;;;;;sCAIpB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,SAAS,IAAM,eAAe,CAAC;gCAC/B,WAAU;;kDAEV,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;oCAAiB;kDAEnC,8OAAC,oNAAA,CAAA,cAAW;wCAAC,WAAW,CAAC,uBAAuB,EAAE,cAAc,eAAe,IAAI;;;;;;;;;;;;;;;;;sCAKvF,8OAAC;4BAAI,WAAW,CAAC,sCAAsC,EAAE,cAAc,UAAU,kBAAkB;;8CACjG,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAChE,8OAAC;4CACC,OAAO;4CACP,UAAU,CAAC,IAAM,mBAAmB,EAAE,MAAM,CAAC,KAAK;4CAClD,WAAU;sDAET,eAAe,GAAG,CAAC,CAAA,uBAClB,8OAAC;oDAA0B,OAAO,OAAO,KAAK;8DAC3C,OAAO,KAAK;mDADF,OAAO,KAAK;;;;;;;;;;;;;;;;8CAO/B,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAChE,8OAAC;4CACC,OAAO;4CACP,UAAU,CAAC,IAAM,UAAU,EAAE,MAAM,CAAC,KAAK;4CACzC,WAAU;sDAET,YAAY,GAAG,CAAC,CAAA,uBACf,8OAAC;oDAA0B,OAAO,OAAO,KAAK;8DAC3C,OAAO,KAAK;mDADF,OAAO,KAAK;;;;;;;;;;;;;;;;8CAO/B,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAChE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,SAAS,IAAM,YAAY;oDAC3B,WAAW,CAAC,kDAAkD,EAC5D,aAAa,SACT,2BACA,2CACJ;8DAEF,cAAA,8OAAC,yMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;;;;;;8DAElB,8OAAC;oDACC,SAAS,IAAM,YAAY;oDAC3B,WAAW,CAAC,kDAAkD,EAC5D,aAAa,SACT,2BACA,2CACJ;8DAEF,cAAA,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQ1B,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAE,WAAU;;4BAAgB;4BACvB,YAAY,MAAM;4BAAC;4BACtB,cAAc,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC;;;;;;;;;;;;gBAKvC,YAAY,MAAM,GAAG,kBACpB,8OAAC;oBAAI,WAAW,aAAa,SACzB,yDACA;8BAED,YAAY,GAAG,CAAC,CAAC,qBAChB,8OAAC,8HAAA,CAAA,UAAQ;4BAAgB,MAAM;2BAAhB,KAAK,GAAG;;;;;;;;;yCAI3B,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;;;;;;sCAEpB,8OAAC;4BAAG,WAAU;sCAAyC;;;;;;sCACvD,8OAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;8BAOjC,8OAAC;oBAAQ,WAAU;;sCACjB,8OAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;;sDAEV,8OAAC;4CAAK,WAAU;sDAAW;;;;;;sDAC3B,8OAAC;4CAAK,WAAU;sDAAoC;;;;;;;;;;;;8CAEtD,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;;sDAEV,8OAAC;4CAAK,WAAU;sDAAW;;;;;;sDAC3B,8OAAC;4CAAK,WAAU;sDAAoC;;;;;;;;;;;;8CAEtD,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;;sDAEV,8OAAC;4CAAK,WAAU;sDAAW;;;;;;sDAC3B,8OAAC;4CAAK,WAAU;sDAAoC;;;;;;;;;;;;8CAEtD,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;;sDAEV,8OAAC;4CAAK,WAAU;sDAAW;;;;;;sDAC3B,8OAAC;4CAAK,WAAU;sDAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOlE", "debugId": null}}]}
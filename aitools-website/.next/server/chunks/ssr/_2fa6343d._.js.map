{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/Layout.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Layout.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Layout.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA6R,GAC1T,2DACA", "debugId": null}}, {"offset": {"line": 38, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/Layout.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Layout.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Layout.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAyQ,GACtS,uCACA", "debugId": null}}, {"offset": {"line": 52, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 62, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/LoadingSpinner.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/LoadingSpinner.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/LoadingSpinner.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAqS,GAClU,mEACA", "debugId": null}}, {"offset": {"line": 76, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/LoadingSpinner.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/LoadingSpinner.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/LoadingSpinner.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAiR,GAC9S,+CACA", "debugId": null}}, {"offset": {"line": 90, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 100, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/ErrorMessage.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/ErrorMessage.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ErrorMessage.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAmS,GAChU,iEACA", "debugId": null}}, {"offset": {"line": 114, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/ErrorMessage.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/ErrorMessage.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ErrorMessage.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA+Q,GAC5S,6CACA", "debugId": null}}, {"offset": {"line": 128, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 138, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/lib/api.ts"], "sourcesContent": ["// API客户端工具类\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3001/api';\n\nexport interface ApiResponse<T = any> {\n  success: boolean;\n  data?: T;\n  error?: string;\n  message?: string;\n  details?: string[];\n}\n\nexport interface PaginationInfo {\n  currentPage: number;\n  totalPages: number;\n  totalItems: number;\n  itemsPerPage: number;\n  hasNextPage: boolean;\n  hasPrevPage: boolean;\n}\n\nexport interface Tool {\n  _id: string;\n  name: string;\n  description: string;\n  longDescription?: string;\n  website: string;\n  logo?: string;\n  category: string;\n  tags: string[];\n  pricing: 'free' | 'freemium' | 'paid';\n  pricingDetails?: string;\n  features: string[];\n  screenshots?: string[];\n  submittedBy: string;\n  submittedAt: string;\n  publishedAt?: string;\n  status: 'pending' | 'approved' | 'rejected';\n  reviewNotes?: string;\n  reviewedBy?: string;\n  reviewedAt?: string;\n  views: number;\n  likes: number;\n  isActive: boolean;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface ToolsResponse {\n  tools: Tool[];\n  pagination: PaginationInfo;\n  stats?: {\n    total: number;\n    pending: number;\n    approved: number;\n    rejected: number;\n  };\n}\n\nexport interface AdminStats {\n  overview: {\n    totalTools: number;\n    pendingTools: number;\n    approvedTools: number;\n    rejectedTools: number;\n    totalViews: number;\n    totalLikes: number;\n    recentSubmissions: number;\n    recentApprovals: number;\n    recentRejections: number;\n    avgReviewTime: number;\n  };\n  categoryStats: Array<{\n    _id: string;\n    count: number;\n    totalViews: number;\n    totalLikes: number;\n  }>;\n  topTools: Array<{\n    _id: string;\n    name: string;\n    category: string;\n    views: number;\n    likes: number;\n  }>;\n  recentActivity: Array<{\n    _id: string;\n    name: string;\n    submittedBy: string;\n    submittedAt: string;\n    status: string;\n    reviewedAt?: string;\n    reviewedBy?: string;\n  }>;\n  dailyStats: Array<{\n    date: string;\n    day: string;\n    submissions: number;\n    approvals: number;\n    rejections: number;\n  }>;\n  timeRange: string;\n}\n\nexport interface Category {\n  id: string;\n  name: string;\n  count: number;\n  totalViews: number;\n  totalLikes: number;\n}\n\nexport interface CategoriesResponse {\n  categories: Category[];\n  overview: {\n    totalTools: number;\n    totalViews: number;\n    totalLikes: number;\n  };\n}\n\nclass ApiClient {\n  private baseURL: string;\n\n  constructor(baseURL: string = API_BASE_URL) {\n    this.baseURL = baseURL;\n  }\n\n  private async request<T>(\n    endpoint: string,\n    options: RequestInit = {}\n  ): Promise<ApiResponse<T>> {\n    try {\n      const url = `${this.baseURL}${endpoint}`;\n      const config: RequestInit = {\n        headers: {\n          'Content-Type': 'application/json',\n          ...options.headers,\n        },\n        ...options,\n      };\n\n      const response = await fetch(url, config);\n      const data = await response.json();\n\n      if (!response.ok) {\n        throw new Error(data.error || `HTTP error! status: ${response.status}`);\n      }\n\n      return data;\n    } catch (error) {\n      console.error('API request failed:', error);\n      return {\n        success: false,\n        error: error instanceof Error ? error.message : '请求失败',\n      };\n    }\n  }\n\n  // 工具相关API\n  async getTools(params?: {\n    page?: number;\n    limit?: number;\n    category?: string;\n    status?: string;\n    search?: string;\n    sort?: string;\n    order?: string;\n  }): Promise<ApiResponse<ToolsResponse>> {\n    const searchParams = new URLSearchParams();\n    if (params) {\n      Object.entries(params).forEach(([key, value]) => {\n        if (value !== undefined) {\n          searchParams.append(key, value.toString());\n        }\n      });\n    }\n    const query = searchParams.toString();\n    return this.request<ToolsResponse>(`/tools${query ? `?${query}` : ''}`);\n  }\n\n  async getTool(id: string): Promise<ApiResponse<Tool>> {\n    return this.request<Tool>(`/tools/${id}`);\n  }\n\n  async createTool(toolData: Partial<Tool>): Promise<ApiResponse<Tool>> {\n    return this.request<Tool>('/tools', {\n      method: 'POST',\n      body: JSON.stringify(toolData),\n    });\n  }\n\n  async updateTool(id: string, toolData: Partial<Tool>): Promise<ApiResponse<Tool>> {\n    return this.request<Tool>(`/tools/${id}`, {\n      method: 'PUT',\n      body: JSON.stringify(toolData),\n    });\n  }\n\n  async deleteTool(id: string): Promise<ApiResponse<void>> {\n    return this.request<void>(`/tools/${id}`, {\n      method: 'DELETE',\n    });\n  }\n\n  // 管理员API\n  async getAdminTools(params?: {\n    page?: number;\n    limit?: number;\n    status?: string;\n    search?: string;\n    sort?: string;\n    order?: string;\n  }): Promise<ApiResponse<ToolsResponse>> {\n    const searchParams = new URLSearchParams();\n    if (params) {\n      Object.entries(params).forEach(([key, value]) => {\n        if (value !== undefined) {\n          searchParams.append(key, value.toString());\n        }\n      });\n    }\n    const query = searchParams.toString();\n    return this.request<ToolsResponse>(`/admin/tools${query ? `?${query}` : ''}`);\n  }\n\n  async approveTool(id: string, data: {\n    reviewedBy?: string;\n    reviewNotes?: string;\n    publishedAt?: string;\n  }): Promise<ApiResponse<Tool>> {\n    return this.request<Tool>(`/admin/tools/${id}/approve`, {\n      method: 'POST',\n      body: JSON.stringify(data),\n    });\n  }\n\n  async rejectTool(id: string, data: {\n    reviewedBy?: string;\n    rejectReason: string;\n  }): Promise<ApiResponse<Tool>> {\n    return this.request<Tool>(`/admin/tools/${id}/reject`, {\n      method: 'POST',\n      body: JSON.stringify(data),\n    });\n  }\n\n  async getAdminStats(timeRange?: string): Promise<ApiResponse<AdminStats>> {\n    const query = timeRange ? `?timeRange=${timeRange}` : '';\n    return this.request<AdminStats>(`/admin/stats${query}`);\n  }\n\n  // 分类API\n  async getCategories(): Promise<ApiResponse<CategoriesResponse>> {\n    return this.request<CategoriesResponse>('/categories');\n  }\n}\n\n// 创建默认的API客户端实例\nexport const apiClient = new ApiClient();\n\n// 导出API客户端类以便在需要时创建新实例\nexport { ApiClient };\n"], "names": [], "mappings": "AAAA,YAAY;;;;;AACZ,MAAM,eAAe,QAAQ,GAAG,CAAC,wBAAwB,IAAI;AAuH7D,MAAM;IACI,QAAgB;IAExB,YAAY,UAAkB,YAAY,CAAE;QAC1C,IAAI,CAAC,OAAO,GAAG;IACjB;IAEA,MAAc,QACZ,QAAgB,EAChB,UAAuB,CAAC,CAAC,EACA;QACzB,IAAI;YACF,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,GAAG,UAAU;YACxC,MAAM,SAAsB;gBAC1B,SAAS;oBACP,gBAAgB;oBAChB,GAAG,QAAQ,OAAO;gBACpB;gBACA,GAAG,OAAO;YACZ;YAEA,MAAM,WAAW,MAAM,MAAM,KAAK;YAClC,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,KAAK,KAAK,IAAI,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YACxE;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uBAAuB;YACrC,OAAO;gBACL,SAAS;gBACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAClD;QACF;IACF;IAEA,UAAU;IACV,MAAM,SAAS,MAQd,EAAuC;QACtC,MAAM,eAAe,IAAI;QACzB,IAAI,QAAQ;YACV,OAAO,OAAO,CAAC,QAAQ,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;gBAC1C,IAAI,UAAU,WAAW;oBACvB,aAAa,MAAM,CAAC,KAAK,MAAM,QAAQ;gBACzC;YACF;QACF;QACA,MAAM,QAAQ,aAAa,QAAQ;QACnC,OAAO,IAAI,CAAC,OAAO,CAAgB,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC,EAAE,OAAO,GAAG,IAAI;IACxE;IAEA,MAAM,QAAQ,EAAU,EAA8B;QACpD,OAAO,IAAI,CAAC,OAAO,CAAO,CAAC,OAAO,EAAE,IAAI;IAC1C;IAEA,MAAM,WAAW,QAAuB,EAA8B;QACpE,OAAO,IAAI,CAAC,OAAO,CAAO,UAAU;YAClC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,WAAW,EAAU,EAAE,QAAuB,EAA8B;QAChF,OAAO,IAAI,CAAC,OAAO,CAAO,CAAC,OAAO,EAAE,IAAI,EAAE;YACxC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,WAAW,EAAU,EAA8B;QACvD,OAAO,IAAI,CAAC,OAAO,CAAO,CAAC,OAAO,EAAE,IAAI,EAAE;YACxC,QAAQ;QACV;IACF;IAEA,SAAS;IACT,MAAM,cAAc,MAOnB,EAAuC;QACtC,MAAM,eAAe,IAAI;QACzB,IAAI,QAAQ;YACV,OAAO,OAAO,CAAC,QAAQ,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;gBAC1C,IAAI,UAAU,WAAW;oBACvB,aAAa,MAAM,CAAC,KAAK,MAAM,QAAQ;gBACzC;YACF;QACF;QACA,MAAM,QAAQ,aAAa,QAAQ;QACnC,OAAO,IAAI,CAAC,OAAO,CAAgB,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC,EAAE,OAAO,GAAG,IAAI;IAC9E;IAEA,MAAM,YAAY,EAAU,EAAE,IAI7B,EAA8B;QAC7B,OAAO,IAAI,CAAC,OAAO,CAAO,CAAC,aAAa,EAAE,GAAG,QAAQ,CAAC,EAAE;YACtD,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,WAAW,EAAU,EAAE,IAG5B,EAA8B;QAC7B,OAAO,IAAI,CAAC,OAAO,CAAO,CAAC,aAAa,EAAE,GAAG,OAAO,CAAC,EAAE;YACrD,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,cAAc,SAAkB,EAAoC;QACxE,MAAM,QAAQ,YAAY,CAAC,WAAW,EAAE,WAAW,GAAG;QACtD,OAAO,IAAI,CAAC,OAAO,CAAa,CAAC,YAAY,EAAE,OAAO;IACxD;IAEA,QAAQ;IACR,MAAM,gBAA0D;QAC9D,OAAO,IAAI,CAAC,OAAO,CAAqB;IAC1C;AACF;AAGO,MAAM,YAAY,IAAI", "debugId": null}}, {"offset": {"line": 248, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/app/tools/%5Bid%5D/page.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport Link from 'next/link';\nimport { useParams } from 'next/navigation';\nimport Layout from '@/components/Layout';\nimport LoadingSpinner from '@/components/LoadingSpinner';\nimport ErrorMessage from '@/components/ErrorMessage';\nimport { apiClient, Tool } from '@/lib/api';\nimport {\n  ExternalLink,\n  Heart,\n  Eye,\n  Star,\n  Tag,\n  DollarSign,\n  ArrowLeft,\n  Share2\n} from 'lucide-react';\n\n// 工具详情页面组件\n\ninterface ToolDetailPageProps {\n  params: {\n    id: string;\n  };\n}\n\nexport default function ToolDetailPage({ params }: ToolDetailPageProps) {\n  const [tool, setTool] = useState<Tool | null>(null);\n  const [relatedTools, setRelatedTools] = useState<Tool[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n\n  useEffect(() => {\n    if (params.id) {\n      fetchToolDetails(params.id);\n    }\n  }, [params.id]);\n\n  const fetchToolDetails = async (toolId: string) => {\n    try {\n      setLoading(true);\n      setError('');\n\n      const response = await apiClient.getTool(toolId);\n\n      if (response.success && response.data) {\n        setTool(response.data);\n        // 获取相关工具\n        fetchRelatedTools(response.data.category);\n      } else {\n        setError(response.error || '工具不存在');\n      }\n    } catch (err) {\n      setError('网络错误，请重试');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const fetchRelatedTools = async (category: string) => {\n    try {\n      const response = await apiClient.getTools({\n        category,\n        status: 'approved',\n        limit: 3\n      });\n\n      if (response.success && response.data) {\n        // 排除当前工具\n        const filtered = response.data.tools.filter(t => t._id !== params.id);\n        setRelatedTools(filtered.slice(0, 3));\n      }\n    } catch (err) {\n      // 静默失败，相关工具不是必需的\n    }\n  };\n\n  const getPricingColor = (pricing: string) => {\n    switch (pricing) {\n      case 'free':\n        return 'bg-green-100 text-green-800';\n      case 'freemium':\n        return 'bg-blue-100 text-blue-800';\n      case 'paid':\n        return 'bg-orange-100 text-orange-800';\n      default:\n        return 'bg-gray-100 text-gray-800';\n    }\n  };\n\n  const getPricingText = (pricing: string) => {\n    switch (pricing) {\n      case 'free':\n        return '免费';\n      case 'freemium':\n        return '免费增值';\n      case 'paid':\n        return '付费';\n      default:\n        return pricing;\n    }\n  };\n\n  if (loading) {\n    return (\n      <Layout>\n        <div className=\"min-h-screen flex items-center justify-center\">\n          <LoadingSpinner size=\"lg\" />\n        </div>\n      </Layout>\n    );\n  }\n\n  if (error || !tool) {\n    return (\n      <Layout>\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n          <ErrorMessage\n            message={error || '工具不存在'}\n            onClose={() => setError('')}\n          />\n          <div className=\"text-center mt-8\">\n            <Link\n              href=\"/tools\"\n              className=\"inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n            >\n              <ArrowLeft className=\"w-4 h-4 mr-2\" />\n              返回工具目录\n            </Link>\n          </div>\n        </div>\n      </Layout>\n    );\n  }\n\n  return (\n    <Layout>\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {/* Breadcrumb */}\n        <div className=\"flex items-center space-x-2 text-sm text-gray-500 mb-6\">\n          <Link href=\"/\" className=\"hover:text-blue-600\">首页</Link>\n          <span>/</span>\n          <Link href=\"/tools\" className=\"hover:text-blue-600\">工具目录</Link>\n          <span>/</span>\n          <span className=\"text-gray-900\">{tool.name}</span>\n        </div>\n\n        {/* Back Button */}\n        <div className=\"mb-6\">\n          <Link\n            href=\"/tools\"\n            className=\"inline-flex items-center text-blue-600 hover:text-blue-700\"\n          >\n            <ArrowLeft className=\"mr-2 h-4 w-4\" />\n            返回工具目录\n          </Link>\n        </div>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8\">\n          {/* Main Content */}\n          <div className=\"lg:col-span-2\">\n            {/* Tool Header */}\n            <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6\">\n              <div className=\"flex items-start justify-between mb-6\">\n                <div className=\"flex items-center space-x-4\">\n                  {tool.logo ? (\n                    <img\n                      src={tool.logo}\n                      alt={tool.name}\n                      className=\"w-16 h-16 rounded-lg object-cover\"\n                    />\n                  ) : (\n                    <div className=\"w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center\">\n                      <span className=\"text-white font-bold text-2xl\">\n                        {tool.name.charAt(0).toUpperCase()}\n                      </span>\n                    </div>\n                  )}\n                  <div>\n                    <h1 className=\"text-3xl font-bold text-gray-900 mb-2\">\n                      {tool.name}\n                    </h1>\n                    <div className=\"flex items-center space-x-4\">\n                      <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getPricingColor(tool.pricing)}`}>\n                        <DollarSign className=\"mr-1 h-4 w-4\" />\n                        {getPricingText(tool.pricing)}\n                      </span>\n                      <div className=\"flex items-center space-x-4 text-sm text-gray-500\">\n                        <div className=\"flex items-center space-x-1\">\n                          <Eye className=\"h-4 w-4\" />\n                          <span>{tool.views || 0} 浏览</span>\n                        </div>\n                        <div className=\"flex items-center space-x-1\">\n                          <Heart className=\"h-4 w-4\" />\n                          <span>{tool.likes || 0} 喜欢</span>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n                \n                <div className=\"flex items-center space-x-2\">\n                  <button className=\"p-2 text-gray-400 hover:text-red-500 transition-colors\">\n                    <Heart className=\"h-5 w-5\" />\n                  </button>\n                  <button className=\"p-2 text-gray-400 hover:text-blue-500 transition-colors\">\n                    <Share2 className=\"h-5 w-5\" />\n                  </button>\n                </div>\n              </div>\n\n              {/* Description */}\n              <p className=\"text-gray-600 text-lg leading-relaxed mb-6\">\n                {tool.description}\n              </p>\n\n              {/* Tags */}\n              <div className=\"flex flex-wrap gap-2 mb-6\">\n                {tool.tags?.map((tag, index) => (\n                  <span\n                    key={index}\n                    className=\"inline-flex items-center px-3 py-1 rounded-md text-sm font-medium bg-gray-100 text-gray-700 hover:bg-gray-200 cursor-pointer\"\n                  >\n                    <Tag className=\"mr-1 h-3 w-3\" />\n                    {tag}\n                  </span>\n                ))}\n              </div>\n\n              {/* CTA Button */}\n              <div className=\"flex flex-col sm:flex-row gap-4\">\n                <a\n                  href={tool.website}\n                  target=\"_blank\"\n                  rel=\"noopener noreferrer\"\n                  className=\"inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 transition-colors\"\n                >\n                  <ExternalLink className=\"mr-2 h-5 w-5\" />\n                  访问 {tool.name}\n                </a>\n                <button className=\"inline-flex items-center justify-center px-6 py-3 border border-gray-300 text-base font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 transition-colors\">\n                  <Heart className=\"mr-2 h-5 w-5\" />\n                  添加到收藏\n                </button>\n              </div>\n            </div>\n\n            {/* Features */}\n            {tool.features && tool.features.length > 0 && (\n              <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6\">\n                <h2 className=\"text-xl font-semibold text-gray-900 mb-4\">主要功能</h2>\n                <ul className=\"grid grid-cols-1 md:grid-cols-2 gap-3\">\n                  {tool.features.map((feature, index) => (\n                    <li key={index} className=\"flex items-center space-x-2\">\n                      <Star className=\"h-4 w-4 text-blue-600 flex-shrink-0\" />\n                      <span className=\"text-gray-700\">{feature}</span>\n                    </li>\n                  ))}\n                </ul>\n              </div>\n            )}\n\n            {/* Tool Info - 暂时隐藏pros和cons，因为API中没有这些字段 */}\n          </div>\n\n          {/* Sidebar */}\n          <div className=\"lg:col-span-1\">\n            {/* Tool Info */}\n            <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6\">\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">工具信息</h3>\n              <div className=\"space-y-3\">\n                <div className=\"flex items-center justify-between\">\n                  <span className=\"text-gray-600\">分类</span>\n                  <span className=\"text-gray-900 font-medium\">文本生成</span>\n                </div>\n                <div className=\"flex items-center justify-between\">\n                  <span className=\"text-gray-600\">价格模式</span>\n                  <span className={`px-2 py-1 rounded text-sm font-medium ${getPricingColor(tool.pricing)}`}>\n                    {getPricingText(tool.pricing)}\n                  </span>\n                </div>\n                <div className=\"flex items-center justify-between\">\n                  <span className=\"text-gray-600\">提交日期</span>\n                  <span className=\"text-gray-900\">{tool.submittedAt || tool.createdAt}</span>\n                </div>\n                <div className=\"flex items-center justify-between\">\n                  <span className=\"text-gray-600\">提交者</span>\n                  <span className=\"text-gray-900\">{tool.submittedBy || '未知'}</span>\n                </div>\n              </div>\n            </div>\n\n            {/* Related Tools */}\n            {relatedTools.length > 0 && (\n              <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n                <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">相关工具</h3>\n                <div className=\"space-y-4\">\n                    {relatedTools.map((relatedTool) => (\n                      <div key={relatedTool._id} className=\"border border-gray-200 rounded-lg p-4 hover:shadow-sm transition-shadow\">\n                        <Link href={`/tools/${relatedTool._id}`}>\n                          <h4 className=\"font-medium text-gray-900 hover:text-blue-600 mb-1\">\n                            {relatedTool.name}\n                          </h4>\n                          <p className=\"text-sm text-gray-600 mb-2 line-clamp-2\">\n                            {relatedTool.description}\n                          </p>\n                          <div className=\"flex items-center justify-between text-xs text-gray-500\">\n                            <span className={`px-2 py-1 rounded ${getPricingColor(relatedTool.pricing)}`}>\n                              {getPricingText(relatedTool.pricing)}\n                            </span>\n                            <div className=\"flex items-center space-x-2\">\n                              <span>{relatedTool.views || 0} 浏览</span>\n                              <span>{relatedTool.likes || 0} 喜欢</span>\n                            </div>\n                          </div>\n                        </Link>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n              )}\n          </div>\n        </div>\n      </div>\n    </Layout>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;AAmBe,SAAS,eAAe,EAAE,MAAM,EAAuB;IACpE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC3D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,OAAO,EAAE,EAAE;YACb,iBAAiB,OAAO,EAAE;QAC5B;IACF,GAAG;QAAC,OAAO,EAAE;KAAC;IAEd,MAAM,mBAAmB,OAAO;QAC9B,IAAI;YACF,WAAW;YACX,SAAS;YAET,MAAM,WAAW,MAAM,iHAAA,CAAA,YAAS,CAAC,OAAO,CAAC;YAEzC,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;gBACrC,QAAQ,SAAS,IAAI;gBACrB,SAAS;gBACT,kBAAkB,SAAS,IAAI,CAAC,QAAQ;YAC1C,OAAO;gBACL,SAAS,SAAS,KAAK,IAAI;YAC7B;QACF,EAAE,OAAO,KAAK;YACZ,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,oBAAoB,OAAO;QAC/B,IAAI;YACF,MAAM,WAAW,MAAM,iHAAA,CAAA,YAAS,CAAC,QAAQ,CAAC;gBACxC;gBACA,QAAQ;gBACR,OAAO;YACT;YAEA,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;gBACrC,SAAS;gBACT,MAAM,WAAW,SAAS,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,GAAG,KAAK,OAAO,EAAE;gBACpE,gBAAgB,SAAS,KAAK,CAAC,GAAG;YACpC;QACF,EAAE,OAAO,KAAK;QACZ,iBAAiB;QACnB;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC,4HAAA,CAAA,UAAM;sBACL,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,oIAAA,CAAA,UAAc;oBAAC,MAAK;;;;;;;;;;;;;;;;IAI7B;IAEA,IAAI,SAAS,CAAC,MAAM;QAClB,qBACE,8OAAC,4HAAA,CAAA,UAAM;sBACL,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,kIAAA,CAAA,UAAY;wBACX,SAAS,SAAS;wBAClB,SAAS,IAAM,SAAS;;;;;;kCAE1B,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;;8CAEV,8OAAC,gNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;;;;;;;;;;;IAOlD;IAEA,qBACE,8OAAC,4HAAA,CAAA,UAAM;kBACL,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;sCAAsB;;;;;;sCAC/C,8OAAC;sCAAK;;;;;;sCACN,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAS,WAAU;sCAAsB;;;;;;sCACpD,8OAAC;sCAAK;;;;;;sCACN,8OAAC;4BAAK,WAAU;sCAAiB,KAAK,IAAI;;;;;;;;;;;;8BAI5C,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;wBACH,MAAK;wBACL,WAAU;;0CAEV,8OAAC,gNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;8BAK1C,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;wDACZ,KAAK,IAAI,iBACR,8OAAC;4DACC,KAAK,KAAK,IAAI;4DACd,KAAK,KAAK,IAAI;4DACd,WAAU;;;;;iFAGZ,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAK,WAAU;0EACb,KAAK,IAAI,CAAC,MAAM,CAAC,GAAG,WAAW;;;;;;;;;;;sEAItC,8OAAC;;8EACC,8OAAC;oEAAG,WAAU;8EACX,KAAK,IAAI;;;;;;8EAEZ,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAK,WAAW,CAAC,oEAAoE,EAAE,gBAAgB,KAAK,OAAO,GAAG;;8FACrH,8OAAC,kNAAA,CAAA,aAAU;oFAAC,WAAU;;;;;;gFACrB,eAAe,KAAK,OAAO;;;;;;;sFAE9B,8OAAC;4EAAI,WAAU;;8FACb,8OAAC;oFAAI,WAAU;;sGACb,8OAAC,gMAAA,CAAA,MAAG;4FAAC,WAAU;;;;;;sGACf,8OAAC;;gGAAM,KAAK,KAAK,IAAI;gGAAE;;;;;;;;;;;;;8FAEzB,8OAAC;oFAAI,WAAU;;sGACb,8OAAC,oMAAA,CAAA,QAAK;4FAAC,WAAU;;;;;;sGACjB,8OAAC;;gGAAM,KAAK,KAAK,IAAI;gGAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8DAOjC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAO,WAAU;sEAChB,cAAA,8OAAC,oMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;;;;;;sEAEnB,8OAAC;4DAAO,WAAU;sEAChB,cAAA,8OAAC,0MAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;sDAMxB,8OAAC;4CAAE,WAAU;sDACV,KAAK,WAAW;;;;;;sDAInB,8OAAC;4CAAI,WAAU;sDACZ,KAAK,IAAI,EAAE,IAAI,CAAC,KAAK,sBACpB,8OAAC;oDAEC,WAAU;;sEAEV,8OAAC,gMAAA,CAAA,MAAG;4DAAC,WAAU;;;;;;wDACd;;mDAJI;;;;;;;;;;sDAUX,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,MAAM,KAAK,OAAO;oDAClB,QAAO;oDACP,KAAI;oDACJ,WAAU;;sEAEV,8OAAC,sNAAA,CAAA,eAAY;4DAAC,WAAU;;;;;;wDAAiB;wDACrC,KAAK,IAAI;;;;;;;8DAEf,8OAAC;oDAAO,WAAU;;sEAChB,8OAAC,oMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;;;;;;;;gCAOvC,KAAK,QAAQ,IAAI,KAAK,QAAQ,CAAC,MAAM,GAAG,mBACvC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,8OAAC;4CAAG,WAAU;sDACX,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,sBAC3B,8OAAC;oDAAe,WAAU;;sEACxB,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;sEAChB,8OAAC;4DAAK,WAAU;sEAAiB;;;;;;;mDAF1B;;;;;;;;;;;;;;;;;;;;;;sCAanB,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,8OAAC;4DAAK,WAAU;sEAA4B;;;;;;;;;;;;8DAE9C,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,8OAAC;4DAAK,WAAW,CAAC,sCAAsC,EAAE,gBAAgB,KAAK,OAAO,GAAG;sEACtF,eAAe,KAAK,OAAO;;;;;;;;;;;;8DAGhC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,8OAAC;4DAAK,WAAU;sEAAiB,KAAK,WAAW,IAAI,KAAK,SAAS;;;;;;;;;;;;8DAErE,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,8OAAC;4DAAK,WAAU;sEAAiB,KAAK,WAAW,IAAI;;;;;;;;;;;;;;;;;;;;;;;;gCAM1D,aAAa,MAAM,GAAG,mBACrB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,8OAAC;4CAAI,WAAU;sDACV,aAAa,GAAG,CAAC,CAAC,4BACjB,8OAAC;oDAA0B,WAAU;8DACnC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAM,CAAC,OAAO,EAAE,YAAY,GAAG,EAAE;;0EACrC,8OAAC;gEAAG,WAAU;0EACX,YAAY,IAAI;;;;;;0EAEnB,8OAAC;gEAAE,WAAU;0EACV,YAAY,WAAW;;;;;;0EAE1B,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAW,CAAC,kBAAkB,EAAE,gBAAgB,YAAY,OAAO,GAAG;kFACzE,eAAe,YAAY,OAAO;;;;;;kFAErC,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;;oFAAM,YAAY,KAAK,IAAI;oFAAE;;;;;;;0FAC9B,8OAAC;;oFAAM,YAAY,KAAK,IAAI;oFAAE;;;;;;;;;;;;;;;;;;;;;;;;;mDAd5B,YAAY,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4B/C", "debugId": null}}]}
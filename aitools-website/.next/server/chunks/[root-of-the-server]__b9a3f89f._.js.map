{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/lib/mongodb.ts"], "sourcesContent": ["import mongoose from 'mongoose';\n\nconst MONGODB_URI = process.env.MONGODB_URI;\n\nif (!MONGODB_URI) {\n  throw new Error(\n    'Please define the MONGODB_URI environment variable inside .env.local'\n  );\n}\n\n/**\n * Global is used here to maintain a cached connection across hot reloads\n * in development. This prevents connections growing exponentially\n * during API Route usage.\n */\nlet cached = global.mongoose;\n\nif (!cached) {\n  cached = global.mongoose = { conn: null, promise: null };\n}\n\nasync function dbConnect() {\n  if (cached.conn) {\n    return cached.conn;\n  }\n\n  if (!cached.promise) {\n    const opts = {\n      bufferCommands: false,\n    };\n\n    cached.promise = mongoose.connect(MONGODB_URI, opts).then((mongoose) => {\n      return mongoose;\n    });\n  }\n\n  try {\n    cached.conn = await cached.promise;\n  } catch (e) {\n    cached.promise = null;\n    throw e;\n  }\n\n  return cached.conn;\n}\n\nexport default dbConnect;\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,cAAc,QAAQ,GAAG,CAAC,WAAW;AAE3C,IAAI,CAAC,aAAa;IAChB,MAAM,IAAI,MACR;AAEJ;AAEA;;;;CAIC,GACD,IAAI,SAAS,OAAO,QAAQ;AAE5B,IAAI,CAAC,QAAQ;IACX,SAAS,OAAO,QAAQ,GAAG;QAAE,MAAM;QAAM,SAAS;IAAK;AACzD;AAEA,eAAe;IACb,IAAI,OAAO,IAAI,EAAE;QACf,OAAO,OAAO,IAAI;IACpB;IAEA,IAAI,CAAC,OAAO,OAAO,EAAE;QACnB,MAAM,OAAO;YACX,gBAAgB;QAClB;QAEA,OAAO,OAAO,GAAG,yGAAA,CAAA,UAAQ,CAAC,OAAO,CAAC,aAAa,MAAM,IAAI,CAAC,CAAC;YACzD,OAAO;QACT;IACF;IAEA,IAAI;QACF,OAAO,IAAI,GAAG,MAAM,OAAO,OAAO;IACpC,EAAE,OAAO,GAAG;QACV,OAAO,OAAO,GAAG;QACjB,MAAM;IACR;IAEA,OAAO,OAAO,IAAI;AACpB;uCAEe", "debugId": null}}, {"offset": {"line": 115, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/models/User.ts"], "sourcesContent": ["import mongoose, { Document, Schema } from 'mongoose';\n\nexport interface IAccount {\n  provider: 'google' | 'github' | 'email';\n  providerId: string;\n  providerAccountId: string;\n  accessToken?: string;\n  refreshToken?: string;\n  expiresAt?: Date;\n}\n\nexport interface IUser extends Document {\n  email: string;\n  name: string;\n  avatar?: string;\n  role: 'user' | 'admin';\n  isActive: boolean;\n\n  // 认证相关\n  emailVerified: boolean;\n  emailVerificationToken?: string;\n  emailVerificationExpires?: Date;\n\n  // OAuth账户关联\n  accounts: IAccount[];\n\n  // 用户行为\n  submittedTools: string[]; // Tool IDs\n  likedTools: string[]; // Tool IDs\n  comments: string[]; // Comment IDs\n\n  // 时间戳\n  createdAt: Date;\n  updatedAt: Date;\n  lastLoginAt?: Date;\n}\n\nconst AccountSchema = new Schema({\n  provider: {\n    type: String,\n    required: true,\n    enum: ['google', 'github', 'email']\n  },\n  providerId: {\n    type: String,\n    required: true\n  },\n  providerAccountId: {\n    type: String,\n    required: true\n  },\n  accessToken: String,\n  refreshToken: String,\n  expiresAt: Date\n}, { _id: false });\n\nconst UserSchema: Schema = new Schema({\n  email: {\n    type: String,\n    required: [true, 'Email is required'],\n    unique: true,\n    trim: true,\n    lowercase: true,\n    validate: {\n      validator: function(v: string) {\n        return /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(v);\n      },\n      message: 'Please enter a valid email address'\n    }\n  },\n  name: {\n    type: String,\n    required: [true, 'Name is required'],\n    trim: true,\n    maxlength: [100, 'Name cannot exceed 100 characters']\n  },\n  avatar: {\n    type: String,\n    trim: true\n  },\n  role: {\n    type: String,\n    required: true,\n    enum: ['user', 'admin'],\n    default: 'user'\n  },\n  isActive: {\n    type: Boolean,\n    default: true\n  },\n\n  // 认证相关\n  emailVerified: {\n    type: Boolean,\n    default: false\n  },\n  emailVerificationToken: {\n    type: String,\n    trim: true\n  },\n  emailVerificationExpires: {\n    type: Date\n  },\n\n  // OAuth账户关联\n  accounts: [AccountSchema],\n\n  // 用户行为\n  submittedTools: [{\n    type: Schema.Types.ObjectId,\n    ref: 'Tool'\n  }],\n  likedTools: [{\n    type: Schema.Types.ObjectId,\n    ref: 'Tool'\n  }],\n  comments: [{\n    type: Schema.Types.ObjectId,\n    ref: 'Comment'\n  }],\n\n  // 时间戳\n  lastLoginAt: {\n    type: Date\n  }\n}, {\n  timestamps: true,\n  toJSON: { virtuals: true },\n  toObject: { virtuals: true }\n});\n\n// Indexes\nUserSchema.index({ email: 1 });\nUserSchema.index({ role: 1 });\nUserSchema.index({ emailVerificationToken: 1 });\nUserSchema.index({ 'accounts.provider': 1, 'accounts.providerAccountId': 1 });\n\n// 实例方法\nUserSchema.methods.addAccount = function(account: IAccount) {\n  // 检查是否已存在相同的账户\n  const existingAccount = this.accounts.find(\n    (acc: IAccount) => acc.provider === account.provider && acc.providerAccountId === account.providerAccountId\n  );\n\n  if (!existingAccount) {\n    this.accounts.push(account);\n  } else {\n    // 更新现有账户信息\n    Object.assign(existingAccount, account);\n  }\n};\n\nUserSchema.methods.removeAccount = function(provider: string, providerAccountId: string) {\n  this.accounts = this.accounts.filter(\n    (acc: IAccount) => !(acc.provider === provider && acc.providerAccountId === providerAccountId)\n  );\n};\n\nexport default mongoose.models.User || mongoose.model<IUser>('User', UserSchema);\n"], "names": [], "mappings": ";;;AAAA;;AAqCA,MAAM,gBAAgB,IAAI,yGAAA,CAAA,SAAM,CAAC;IAC/B,UAAU;QACR,MAAM;QACN,UAAU;QACV,MAAM;YAAC;YAAU;YAAU;SAAQ;IACrC;IACA,YAAY;QACV,MAAM;QACN,UAAU;IACZ;IACA,mBAAmB;QACjB,MAAM;QACN,UAAU;IACZ;IACA,aAAa;IACb,cAAc;IACd,WAAW;AACb,GAAG;IAAE,KAAK;AAAM;AAEhB,MAAM,aAAqB,IAAI,yGAAA,CAAA,SAAM,CAAC;IACpC,OAAO;QACL,MAAM;QACN,UAAU;YAAC;YAAM;SAAoB;QACrC,QAAQ;QACR,MAAM;QACN,WAAW;QACX,UAAU;YACR,WAAW,SAAS,CAAS;gBAC3B,OAAO,6BAA6B,IAAI,CAAC;YAC3C;YACA,SAAS;QACX;IACF;IACA,MAAM;QACJ,MAAM;QACN,UAAU;YAAC;YAAM;SAAmB;QACpC,MAAM;QACN,WAAW;YAAC;YAAK;SAAoC;IACvD;IACA,QAAQ;QACN,MAAM;QACN,MAAM;IACR;IACA,MAAM;QACJ,MAAM;QACN,UAAU;QACV,MAAM;YAAC;YAAQ;SAAQ;QACvB,SAAS;IACX;IACA,UAAU;QACR,MAAM;QACN,SAAS;IACX;IAEA,OAAO;IACP,eAAe;QACb,MAAM;QACN,SAAS;IACX;IACA,wBAAwB;QACtB,MAAM;QACN,MAAM;IACR;IACA,0BAA0B;QACxB,MAAM;IACR;IAEA,YAAY;IACZ,UAAU;QAAC;KAAc;IAEzB,OAAO;IACP,gBAAgB;QAAC;YACf,MAAM,yGAAA,CAAA,SAAM,CAAC,KAAK,CAAC,QAAQ;YAC3B,KAAK;QACP;KAAE;IACF,YAAY;QAAC;YACX,MAAM,yGAAA,CAAA,SAAM,CAAC,KAAK,CAAC,QAAQ;YAC3B,KAAK;QACP;KAAE;IACF,UAAU;QAAC;YACT,MAAM,yGAAA,CAAA,SAAM,CAAC,KAAK,CAAC,QAAQ;YAC3B,KAAK;QACP;KAAE;IAEF,MAAM;IACN,aAAa;QACX,MAAM;IACR;AACF,GAAG;IACD,YAAY;IACZ,QAAQ;QAAE,UAAU;IAAK;IACzB,UAAU;QAAE,UAAU;IAAK;AAC7B;AAEA,UAAU;AACV,WAAW,KAAK,CAAC;IAAE,OAAO;AAAE;AAC5B,WAAW,KAAK,CAAC;IAAE,MAAM;AAAE;AAC3B,WAAW,KAAK,CAAC;IAAE,wBAAwB;AAAE;AAC7C,WAAW,KAAK,CAAC;IAAE,qBAAqB;IAAG,8BAA8B;AAAE;AAE3E,OAAO;AACP,WAAW,OAAO,CAAC,UAAU,GAAG,SAAS,OAAiB;IACxD,eAAe;IACf,MAAM,kBAAkB,IAAI,CAAC,QAAQ,CAAC,IAAI,CACxC,CAAC,MAAkB,IAAI,QAAQ,KAAK,QAAQ,QAAQ,IAAI,IAAI,iBAAiB,KAAK,QAAQ,iBAAiB;IAG7G,IAAI,CAAC,iBAAiB;QACpB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;IACrB,OAAO;QACL,WAAW;QACX,OAAO,MAAM,CAAC,iBAAiB;IACjC;AACF;AAEA,WAAW,OAAO,CAAC,aAAa,GAAG,SAAS,QAAgB,EAAE,iBAAyB;IACrF,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAClC,CAAC,MAAkB,CAAC,CAAC,IAAI,QAAQ,KAAK,YAAY,IAAI,iBAAiB,KAAK,iBAAiB;AAEjG;uCAEe,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,IAAI,IAAI,yGAAA,CAAA,UAAQ,CAAC,KAAK,CAAQ,QAAQ", "debugId": null}}, {"offset": {"line": 393, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/app/api/auth/send-code/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport dbConnect from '../../../../lib/mongodb';\nimport User from '../../../../models/User';\nimport nodemailer from 'nodemailer';\nimport crypto from 'crypto';\n\n// 邮件传输器\nconst emailTransporter = nodemailer.createTransport({\n  host: process.env.EMAIL_HOST,\n  port: parseInt(process.env.EMAIL_PORT || '587'),\n  secure: false,\n  auth: {\n    user: process.env.EMAIL_USER,\n    pass: process.env.EMAIL_PASS,\n  },\n});\n\n// POST /api/auth/send-code - 发送邮件验证码\nexport async function POST(request: NextRequest) {\n  try {\n    await dbConnect();\n    \n    const { email } = await request.json();\n    \n    // 验证邮箱格式\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    if (!email || !emailRegex.test(email)) {\n      return NextResponse.json(\n        { success: false, error: '请输入有效的邮箱地址' },\n        { status: 400 }\n      );\n    }\n    \n    // 生成6位数验证码\n    const verificationCode = Math.floor(100000 + Math.random() * 900000).toString();\n    const verificationToken = crypto.randomBytes(32).toString('hex');\n    const expiresAt = new Date(Date.now() + 10 * 60 * 1000); // 10分钟后过期\n    \n    // 查找或创建用户\n    let user = await User.findOne({ email: email.toLowerCase() });\n    \n    if (!user) {\n      // 创建新用户（未验证状态）\n      user = new User({\n        email: email.toLowerCase(),\n        name: email.split('@')[0], // 使用邮箱前缀作为默认用户名\n        emailVerified: false,\n        emailVerificationToken: verificationToken,\n        emailVerificationExpires: expiresAt,\n      });\n    } else {\n      // 更新验证码\n      user.emailVerificationToken = verificationToken;\n      user.emailVerificationExpires = expiresAt;\n    }\n    \n    await user.save();\n    \n    // 发送验证码邮件\n    try {\n      await emailTransporter.sendMail({\n        to: email,\n        from: process.env.EMAIL_FROM,\n        subject: 'AI Tools Directory - 登录验证码',\n        html: `\n          <div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;\">\n            <div style=\"text-align: center; margin-bottom: 30px;\">\n              <h1 style=\"color: #3B82F6; margin: 0;\">AI Tools Directory</h1>\n            </div>\n            \n            <div style=\"background-color: #f8fafc; padding: 30px; border-radius: 8px; text-align: center;\">\n              <h2 style=\"color: #1f2937; margin-bottom: 20px;\">您的登录验证码</h2>\n              \n              <div style=\"background-color: white; padding: 20px; border-radius: 6px; margin: 20px 0;\">\n                <span style=\"font-size: 32px; font-weight: bold; color: #3B82F6; letter-spacing: 8px;\">\n                  ${verificationCode}\n                </span>\n              </div>\n              \n              <p style=\"color: #6b7280; margin: 20px 0;\">\n                请在10分钟内输入此验证码完成登录\n              </p>\n              \n              <p style=\"color: #ef4444; font-size: 14px; margin-top: 30px;\">\n                如果您没有请求此验证码，请忽略此邮件\n              </p>\n            </div>\n            \n            <div style=\"text-align: center; margin-top: 30px; color: #9ca3af; font-size: 12px;\">\n              <p>此邮件由 AI Tools Directory 自动发送，请勿回复</p>\n            </div>\n          </div>\n        `,\n        text: `您的 AI Tools Directory 登录验证码是：${verificationCode}。请在10分钟内使用此验证码完成登录。`,\n      });\n      \n      // 为了安全，我们将验证码存储在数据库中而不是直接返回\n      // 这里我们临时存储验证码用于验证（实际应用中应该加密存储）\n      user.emailVerificationToken = `${verificationToken}:${verificationCode}`;\n      await user.save();\n      \n      return NextResponse.json({\n        success: true,\n        message: '验证码已发送到您的邮箱',\n        token: verificationToken // 返回token用于后续验证\n      });\n      \n    } catch (emailError) {\n      console.error('Email sending error:', emailError);\n      return NextResponse.json(\n        { success: false, error: '发送邮件失败，请稍后重试' },\n        { status: 500 }\n      );\n    }\n    \n  } catch (error) {\n    console.error('Send code error:', error);\n    return NextResponse.json(\n      { success: false, error: '服务器错误，请稍后重试' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;;;;;;AAEA,QAAQ;AACR,MAAM,mBAAmB,iJAAA,CAAA,UAAU,CAAC,eAAe,CAAC;IAClD,MAAM,QAAQ,GAAG,CAAC,UAAU;IAC5B,MAAM,SAAS,QAAQ,GAAG,CAAC,UAAU,IAAI;IACzC,QAAQ;IACR,MAAM;QACJ,MAAM,QAAQ,GAAG,CAAC,UAAU;QAC5B,MAAM,QAAQ,GAAG,CAAC,UAAU;IAC9B;AACF;AAGO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,CAAA,GAAA,uHAAA,CAAA,UAAS,AAAD;QAEd,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,QAAQ,IAAI;QAEpC,SAAS;QACT,MAAM,aAAa;QACnB,IAAI,CAAC,SAAS,CAAC,WAAW,IAAI,CAAC,QAAQ;YACrC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAa,GACtC;gBAAE,QAAQ;YAAI;QAElB;QAEA,WAAW;QACX,MAAM,mBAAmB,KAAK,KAAK,CAAC,SAAS,KAAK,MAAM,KAAK,QAAQ,QAAQ;QAC7E,MAAM,oBAAoB,qGAAA,CAAA,UAAM,CAAC,WAAW,CAAC,IAAI,QAAQ,CAAC;QAC1D,MAAM,YAAY,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,OAAO,UAAU;QAEnE,UAAU;QACV,IAAI,OAAO,MAAM,uHAAA,CAAA,UAAI,CAAC,OAAO,CAAC;YAAE,OAAO,MAAM,WAAW;QAAG;QAE3D,IAAI,CAAC,MAAM;YACT,eAAe;YACf,OAAO,IAAI,uHAAA,CAAA,UAAI,CAAC;gBACd,OAAO,MAAM,WAAW;gBACxB,MAAM,MAAM,KAAK,CAAC,IAAI,CAAC,EAAE;gBACzB,eAAe;gBACf,wBAAwB;gBACxB,0BAA0B;YAC5B;QACF,OAAO;YACL,QAAQ;YACR,KAAK,sBAAsB,GAAG;YAC9B,KAAK,wBAAwB,GAAG;QAClC;QAEA,MAAM,KAAK,IAAI;QAEf,UAAU;QACV,IAAI;YACF,MAAM,iBAAiB,QAAQ,CAAC;gBAC9B,IAAI;gBACJ,MAAM,QAAQ,GAAG,CAAC,UAAU;gBAC5B,SAAS;gBACT,MAAM,CAAC;;;;;;;;;;;kBAWG,EAAE,iBAAiB;;;;;;;;;;;;;;;;;QAiB7B,CAAC;gBACD,MAAM,CAAC,6BAA6B,EAAE,iBAAiB,mBAAmB,CAAC;YAC7E;YAEA,4BAA4B;YAC5B,+BAA+B;YAC/B,KAAK,sBAAsB,GAAG,GAAG,kBAAkB,CAAC,EAAE,kBAAkB;YACxE,MAAM,KAAK,IAAI;YAEf,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,SAAS;gBACT,OAAO,kBAAkB,gBAAgB;YAC3C;QAEF,EAAE,OAAO,YAAY;YACnB,QAAQ,KAAK,CAAC,wBAAwB;YACtC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAe,GACxC;gBAAE,QAAQ;YAAI;QAElB;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oBAAoB;QAClC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;YAAO,OAAO;QAAc,GACvC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}
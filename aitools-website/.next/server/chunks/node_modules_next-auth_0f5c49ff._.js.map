{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/node_modules/next-auth/core/types.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});"], "names": [], "mappings": "AAAA;AAEA,OAAO,cAAc,CAAC,SAAS,cAAc;IAC3C,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/node_modules/next-auth/core/errors.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.UnsupportedStrategy = exports.UnknownError = exports.OAuthCallbackError = exports.MissingSecret = exports.MissingAuthorize = exports.MissingAdapterMethods = exports.MissingAdapter = exports.MissingAPIRoute = exports.InvalidCallbackUrl = exports.AccountNotLinkedError = void 0;\nexports.adapterErrorHandler = adapterErrorHandler;\nexports.capitalize = capitalize;\nexports.eventsErrorHandler = eventsErrorHandler;\nexports.upperSnake = upperSnake;\nvar _regenerator = _interopRequireDefault(require(\"@babel/runtime/regenerator\"));\nvar _asyncToGenerator2 = _interopRequireDefault(require(\"@babel/runtime/helpers/asyncToGenerator\"));\nvar _defineProperty2 = _interopRequireDefault(require(\"@babel/runtime/helpers/defineProperty\"));\nvar _classCallCheck2 = _interopRequireDefault(require(\"@babel/runtime/helpers/classCallCheck\"));\nvar _createClass2 = _interopRequireDefault(require(\"@babel/runtime/helpers/createClass\"));\nvar _possibleConstructorReturn2 = _interopRequireDefault(require(\"@babel/runtime/helpers/possibleConstructorReturn\"));\nvar _getPrototypeOf2 = _interopRequireDefault(require(\"@babel/runtime/helpers/getPrototypeOf\"));\nvar _inherits2 = _interopRequireDefault(require(\"@babel/runtime/helpers/inherits\"));\nvar _wrapNativeSuper2 = _interopRequireDefault(require(\"@babel/runtime/helpers/wrapNativeSuper\"));\nfunction _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }\nfunction _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }\nvar UnknownError = exports.UnknownError = function (_Error) {\n  function UnknownError(error) {\n    var _message;\n    var _this;\n    (0, _classCallCheck2.default)(this, UnknownError);\n    _this = _callSuper(this, UnknownError, [(_message = error === null || error === void 0 ? void 0 : error.message) !== null && _message !== void 0 ? _message : error]);\n    _this.name = \"UnknownError\";\n    _this.code = error.code;\n    if (error instanceof Error) {\n      _this.stack = error.stack;\n    }\n    return _this;\n  }\n  (0, _inherits2.default)(UnknownError, _Error);\n  return (0, _createClass2.default)(UnknownError, [{\n    key: \"toJSON\",\n    value: function toJSON() {\n      return {\n        name: this.name,\n        message: this.message,\n        stack: this.stack\n      };\n    }\n  }]);\n}((0, _wrapNativeSuper2.default)(Error));\nvar OAuthCallbackError = exports.OAuthCallbackError = function (_UnknownError) {\n  function OAuthCallbackError() {\n    var _this2;\n    (0, _classCallCheck2.default)(this, OAuthCallbackError);\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this2 = _callSuper(this, OAuthCallbackError, [].concat(args));\n    (0, _defineProperty2.default)(_this2, \"name\", \"OAuthCallbackError\");\n    return _this2;\n  }\n  (0, _inherits2.default)(OAuthCallbackError, _UnknownError);\n  return (0, _createClass2.default)(OAuthCallbackError);\n}(UnknownError);\nvar AccountNotLinkedError = exports.AccountNotLinkedError = function (_UnknownError2) {\n  function AccountNotLinkedError() {\n    var _this3;\n    (0, _classCallCheck2.default)(this, AccountNotLinkedError);\n    for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n      args[_key2] = arguments[_key2];\n    }\n    _this3 = _callSuper(this, AccountNotLinkedError, [].concat(args));\n    (0, _defineProperty2.default)(_this3, \"name\", \"AccountNotLinkedError\");\n    return _this3;\n  }\n  (0, _inherits2.default)(AccountNotLinkedError, _UnknownError2);\n  return (0, _createClass2.default)(AccountNotLinkedError);\n}(UnknownError);\nvar MissingAPIRoute = exports.MissingAPIRoute = function (_UnknownError3) {\n  function MissingAPIRoute() {\n    var _this4;\n    (0, _classCallCheck2.default)(this, MissingAPIRoute);\n    for (var _len3 = arguments.length, args = new Array(_len3), _key3 = 0; _key3 < _len3; _key3++) {\n      args[_key3] = arguments[_key3];\n    }\n    _this4 = _callSuper(this, MissingAPIRoute, [].concat(args));\n    (0, _defineProperty2.default)(_this4, \"name\", \"MissingAPIRouteError\");\n    (0, _defineProperty2.default)(_this4, \"code\", \"MISSING_NEXTAUTH_API_ROUTE_ERROR\");\n    return _this4;\n  }\n  (0, _inherits2.default)(MissingAPIRoute, _UnknownError3);\n  return (0, _createClass2.default)(MissingAPIRoute);\n}(UnknownError);\nvar MissingSecret = exports.MissingSecret = function (_UnknownError4) {\n  function MissingSecret() {\n    var _this5;\n    (0, _classCallCheck2.default)(this, MissingSecret);\n    for (var _len4 = arguments.length, args = new Array(_len4), _key4 = 0; _key4 < _len4; _key4++) {\n      args[_key4] = arguments[_key4];\n    }\n    _this5 = _callSuper(this, MissingSecret, [].concat(args));\n    (0, _defineProperty2.default)(_this5, \"name\", \"MissingSecretError\");\n    (0, _defineProperty2.default)(_this5, \"code\", \"NO_SECRET\");\n    return _this5;\n  }\n  (0, _inherits2.default)(MissingSecret, _UnknownError4);\n  return (0, _createClass2.default)(MissingSecret);\n}(UnknownError);\nvar MissingAuthorize = exports.MissingAuthorize = function (_UnknownError5) {\n  function MissingAuthorize() {\n    var _this6;\n    (0, _classCallCheck2.default)(this, MissingAuthorize);\n    for (var _len5 = arguments.length, args = new Array(_len5), _key5 = 0; _key5 < _len5; _key5++) {\n      args[_key5] = arguments[_key5];\n    }\n    _this6 = _callSuper(this, MissingAuthorize, [].concat(args));\n    (0, _defineProperty2.default)(_this6, \"name\", \"MissingAuthorizeError\");\n    (0, _defineProperty2.default)(_this6, \"code\", \"CALLBACK_CREDENTIALS_HANDLER_ERROR\");\n    return _this6;\n  }\n  (0, _inherits2.default)(MissingAuthorize, _UnknownError5);\n  return (0, _createClass2.default)(MissingAuthorize);\n}(UnknownError);\nvar MissingAdapter = exports.MissingAdapter = function (_UnknownError6) {\n  function MissingAdapter() {\n    var _this7;\n    (0, _classCallCheck2.default)(this, MissingAdapter);\n    for (var _len6 = arguments.length, args = new Array(_len6), _key6 = 0; _key6 < _len6; _key6++) {\n      args[_key6] = arguments[_key6];\n    }\n    _this7 = _callSuper(this, MissingAdapter, [].concat(args));\n    (0, _defineProperty2.default)(_this7, \"name\", \"MissingAdapterError\");\n    (0, _defineProperty2.default)(_this7, \"code\", \"EMAIL_REQUIRES_ADAPTER_ERROR\");\n    return _this7;\n  }\n  (0, _inherits2.default)(MissingAdapter, _UnknownError6);\n  return (0, _createClass2.default)(MissingAdapter);\n}(UnknownError);\nvar MissingAdapterMethods = exports.MissingAdapterMethods = function (_UnknownError7) {\n  function MissingAdapterMethods() {\n    var _this8;\n    (0, _classCallCheck2.default)(this, MissingAdapterMethods);\n    for (var _len7 = arguments.length, args = new Array(_len7), _key7 = 0; _key7 < _len7; _key7++) {\n      args[_key7] = arguments[_key7];\n    }\n    _this8 = _callSuper(this, MissingAdapterMethods, [].concat(args));\n    (0, _defineProperty2.default)(_this8, \"name\", \"MissingAdapterMethodsError\");\n    (0, _defineProperty2.default)(_this8, \"code\", \"MISSING_ADAPTER_METHODS_ERROR\");\n    return _this8;\n  }\n  (0, _inherits2.default)(MissingAdapterMethods, _UnknownError7);\n  return (0, _createClass2.default)(MissingAdapterMethods);\n}(UnknownError);\nvar UnsupportedStrategy = exports.UnsupportedStrategy = function (_UnknownError8) {\n  function UnsupportedStrategy() {\n    var _this9;\n    (0, _classCallCheck2.default)(this, UnsupportedStrategy);\n    for (var _len8 = arguments.length, args = new Array(_len8), _key8 = 0; _key8 < _len8; _key8++) {\n      args[_key8] = arguments[_key8];\n    }\n    _this9 = _callSuper(this, UnsupportedStrategy, [].concat(args));\n    (0, _defineProperty2.default)(_this9, \"name\", \"UnsupportedStrategyError\");\n    (0, _defineProperty2.default)(_this9, \"code\", \"CALLBACK_CREDENTIALS_JWT_ERROR\");\n    return _this9;\n  }\n  (0, _inherits2.default)(UnsupportedStrategy, _UnknownError8);\n  return (0, _createClass2.default)(UnsupportedStrategy);\n}(UnknownError);\nvar InvalidCallbackUrl = exports.InvalidCallbackUrl = function (_UnknownError9) {\n  function InvalidCallbackUrl() {\n    var _this10;\n    (0, _classCallCheck2.default)(this, InvalidCallbackUrl);\n    for (var _len9 = arguments.length, args = new Array(_len9), _key9 = 0; _key9 < _len9; _key9++) {\n      args[_key9] = arguments[_key9];\n    }\n    _this10 = _callSuper(this, InvalidCallbackUrl, [].concat(args));\n    (0, _defineProperty2.default)(_this10, \"name\", \"InvalidCallbackUrl\");\n    (0, _defineProperty2.default)(_this10, \"code\", \"INVALID_CALLBACK_URL_ERROR\");\n    return _this10;\n  }\n  (0, _inherits2.default)(InvalidCallbackUrl, _UnknownError9);\n  return (0, _createClass2.default)(InvalidCallbackUrl);\n}(UnknownError);\nfunction upperSnake(s) {\n  return s.replace(/([A-Z])/g, \"_$1\").toUpperCase();\n}\nfunction capitalize(s) {\n  return \"\".concat(s[0].toUpperCase()).concat(s.slice(1));\n}\nfunction eventsErrorHandler(methods, logger) {\n  return Object.keys(methods).reduce(function (acc, name) {\n    acc[name] = (0, _asyncToGenerator2.default)(_regenerator.default.mark(function _callee() {\n      var method,\n        _args = arguments;\n      return _regenerator.default.wrap(function _callee$(_context) {\n        while (1) switch (_context.prev = _context.next) {\n          case 0:\n            _context.prev = 0;\n            method = methods[name];\n            _context.next = 4;\n            return method.apply(void 0, _args);\n          case 4:\n            return _context.abrupt(\"return\", _context.sent);\n          case 7:\n            _context.prev = 7;\n            _context.t0 = _context[\"catch\"](0);\n            logger.error(\"\".concat(upperSnake(name), \"_EVENT_ERROR\"), _context.t0);\n          case 10:\n          case \"end\":\n            return _context.stop();\n        }\n      }, _callee, null, [[0, 7]]);\n    }));\n    return acc;\n  }, {});\n}\nfunction adapterErrorHandler(adapter, logger) {\n  if (!adapter) return;\n  return Object.keys(adapter).reduce(function (acc, name) {\n    acc[name] = (0, _asyncToGenerator2.default)(_regenerator.default.mark(function _callee2() {\n      var _len10,\n        args,\n        _key10,\n        method,\n        e,\n        _args2 = arguments;\n      return _regenerator.default.wrap(function _callee2$(_context2) {\n        while (1) switch (_context2.prev = _context2.next) {\n          case 0:\n            _context2.prev = 0;\n            for (_len10 = _args2.length, args = new Array(_len10), _key10 = 0; _key10 < _len10; _key10++) {\n              args[_key10] = _args2[_key10];\n            }\n            logger.debug(\"adapter_\".concat(name), {\n              args: args\n            });\n            method = adapter[name];\n            _context2.next = 6;\n            return method.apply(void 0, args);\n          case 6:\n            return _context2.abrupt(\"return\", _context2.sent);\n          case 9:\n            _context2.prev = 9;\n            _context2.t0 = _context2[\"catch\"](0);\n            logger.error(\"adapter_error_\".concat(name), _context2.t0);\n            e = new UnknownError(_context2.t0);\n            e.name = \"\".concat(capitalize(name), \"Error\");\n            throw e;\n          case 15:\n          case \"end\":\n            return _context2.stop();\n        }\n      }, _callee2, null, [[0, 9]]);\n    }));\n    return acc;\n  }, {});\n}"], "names": [], "mappings": "AAAA;AAEA,IAAI;AACJ,OAAO,cAAc,CAAC,SAAS,cAAc;IAC3C,OAAO;AACT;AACA,QAAQ,mBAAmB,GAAG,QAAQ,YAAY,GAAG,QAAQ,kBAAkB,GAAG,QAAQ,aAAa,GAAG,QAAQ,gBAAgB,GAAG,QAAQ,qBAAqB,GAAG,QAAQ,cAAc,GAAG,QAAQ,eAAe,GAAG,QAAQ,kBAAkB,GAAG,QAAQ,qBAAqB,GAAG,KAAK;AAC1R,QAAQ,mBAAmB,GAAG;AAC9B,QAAQ,UAAU,GAAG;AACrB,QAAQ,kBAAkB,GAAG;AAC7B,QAAQ,UAAU,GAAG;AACrB,IAAI,eAAe;AACnB,IAAI,qBAAqB;AACzB,IAAI,mBAAmB;AACvB,IAAI,mBAAmB;AACvB,IAAI,gBAAgB;AACpB,IAAI,8BAA8B;AAClC,IAAI,mBAAmB;AACvB,IAAI,aAAa;AACjB,IAAI,oBAAoB;AACxB,SAAS,WAAW,CAAC,EAAE,CAAC,EAAE,CAAC;IAAI,OAAO,IAAI,CAAC,GAAG,iBAAiB,OAAO,EAAE,IAAI,CAAC,GAAG,4BAA4B,OAAO,EAAE,GAAG,8BAA8B,QAAQ,SAAS,CAAC,GAAG,KAAK,EAAE,EAAE,CAAC,GAAG,iBAAiB,OAAO,EAAE,GAAG,WAAW,IAAI,EAAE,KAAK,CAAC,GAAG;AAAK;AACpP,SAAS;IAA8B,IAAI;QAAE,IAAI,IAAI,CAAC,QAAQ,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,SAAS,CAAC,SAAS,EAAE,EAAE,YAAa;IAAK,EAAE,OAAO,GAAG,CAAC;IAAE,OAAO,CAAC,4BAA4B,SAAS;QAA8B,OAAO,CAAC,CAAC;IAAG,CAAC;AAAK;AAClP,IAAI,eAAe,QAAQ,YAAY,GAAG,SAAU,MAAM;IACxD,SAAS,aAAa,KAAK;QACzB,IAAI;QACJ,IAAI;QACJ,CAAC,GAAG,iBAAiB,OAAO,EAAE,IAAI,EAAE;QACpC,QAAQ,WAAW,IAAI,EAAE,cAAc;YAAC,CAAC,WAAW,UAAU,QAAQ,UAAU,KAAK,IAAI,KAAK,IAAI,MAAM,OAAO,MAAM,QAAQ,aAAa,KAAK,IAAI,WAAW;SAAM;QACpK,MAAM,IAAI,GAAG;QACb,MAAM,IAAI,GAAG,MAAM,IAAI;QACvB,IAAI,iBAAiB,OAAO;YAC1B,MAAM,KAAK,GAAG,MAAM,KAAK;QAC3B;QACA,OAAO;IACT;IACA,CAAC,GAAG,WAAW,OAAO,EAAE,cAAc;IACtC,OAAO,CAAC,GAAG,cAAc,OAAO,EAAE,cAAc;QAAC;YAC/C,KAAK;YACL,OAAO,SAAS;gBACd,OAAO;oBACL,MAAM,IAAI,CAAC,IAAI;oBACf,SAAS,IAAI,CAAC,OAAO;oBACrB,OAAO,IAAI,CAAC,KAAK;gBACnB;YACF;QACF;KAAE;AACJ,EAAE,CAAC,GAAG,kBAAkB,OAAO,EAAE;AACjC,IAAI,qBAAqB,QAAQ,kBAAkB,GAAG,SAAU,aAAa;IAC3E,SAAS;QACP,IAAI;QACJ,CAAC,GAAG,iBAAiB,OAAO,EAAE,IAAI,EAAE;QACpC,IAAK,IAAI,OAAO,UAAU,MAAM,EAAE,OAAO,IAAI,MAAM,OAAO,OAAO,GAAG,OAAO,MAAM,OAAQ;YACvF,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC,KAAK;QAC9B;QACA,SAAS,WAAW,IAAI,EAAE,oBAAoB,EAAE,CAAC,MAAM,CAAC;QACxD,CAAC,GAAG,iBAAiB,OAAO,EAAE,QAAQ,QAAQ;QAC9C,OAAO;IACT;IACA,CAAC,GAAG,WAAW,OAAO,EAAE,oBAAoB;IAC5C,OAAO,CAAC,GAAG,cAAc,OAAO,EAAE;AACpC,EAAE;AACF,IAAI,wBAAwB,QAAQ,qBAAqB,GAAG,SAAU,cAAc;IAClF,SAAS;QACP,IAAI;QACJ,CAAC,GAAG,iBAAiB,OAAO,EAAE,IAAI,EAAE;QACpC,IAAK,IAAI,QAAQ,UAAU,MAAM,EAAE,OAAO,IAAI,MAAM,QAAQ,QAAQ,GAAG,QAAQ,OAAO,QAAS;YAC7F,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC,MAAM;QAChC;QACA,SAAS,WAAW,IAAI,EAAE,uBAAuB,EAAE,CAAC,MAAM,CAAC;QAC3D,CAAC,GAAG,iBAAiB,OAAO,EAAE,QAAQ,QAAQ;QAC9C,OAAO;IACT;IACA,CAAC,GAAG,WAAW,OAAO,EAAE,uBAAuB;IAC/C,OAAO,CAAC,GAAG,cAAc,OAAO,EAAE;AACpC,EAAE;AACF,IAAI,kBAAkB,QAAQ,eAAe,GAAG,SAAU,cAAc;IACtE,SAAS;QACP,IAAI;QACJ,CAAC,GAAG,iBAAiB,OAAO,EAAE,IAAI,EAAE;QACpC,IAAK,IAAI,QAAQ,UAAU,MAAM,EAAE,OAAO,IAAI,MAAM,QAAQ,QAAQ,GAAG,QAAQ,OAAO,QAAS;YAC7F,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC,MAAM;QAChC;QACA,SAAS,WAAW,IAAI,EAAE,iBAAiB,EAAE,CAAC,MAAM,CAAC;QACrD,CAAC,GAAG,iBAAiB,OAAO,EAAE,QAAQ,QAAQ;QAC9C,CAAC,GAAG,iBAAiB,OAAO,EAAE,QAAQ,QAAQ;QAC9C,OAAO;IACT;IACA,CAAC,GAAG,WAAW,OAAO,EAAE,iBAAiB;IACzC,OAAO,CAAC,GAAG,cAAc,OAAO,EAAE;AACpC,EAAE;AACF,IAAI,gBAAgB,QAAQ,aAAa,GAAG,SAAU,cAAc;IAClE,SAAS;QACP,IAAI;QACJ,CAAC,GAAG,iBAAiB,OAAO,EAAE,IAAI,EAAE;QACpC,IAAK,IAAI,QAAQ,UAAU,MAAM,EAAE,OAAO,IAAI,MAAM,QAAQ,QAAQ,GAAG,QAAQ,OAAO,QAAS;YAC7F,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC,MAAM;QAChC;QACA,SAAS,WAAW,IAAI,EAAE,eAAe,EAAE,CAAC,MAAM,CAAC;QACnD,CAAC,GAAG,iBAAiB,OAAO,EAAE,QAAQ,QAAQ;QAC9C,CAAC,GAAG,iBAAiB,OAAO,EAAE,QAAQ,QAAQ;QAC9C,OAAO;IACT;IACA,CAAC,GAAG,WAAW,OAAO,EAAE,eAAe;IACvC,OAAO,CAAC,GAAG,cAAc,OAAO,EAAE;AACpC,EAAE;AACF,IAAI,mBAAmB,QAAQ,gBAAgB,GAAG,SAAU,cAAc;IACxE,SAAS;QACP,IAAI;QACJ,CAAC,GAAG,iBAAiB,OAAO,EAAE,IAAI,EAAE;QACpC,IAAK,IAAI,QAAQ,UAAU,MAAM,EAAE,OAAO,IAAI,MAAM,QAAQ,QAAQ,GAAG,QAAQ,OAAO,QAAS;YAC7F,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC,MAAM;QAChC;QACA,SAAS,WAAW,IAAI,EAAE,kBAAkB,EAAE,CAAC,MAAM,CAAC;QACtD,CAAC,GAAG,iBAAiB,OAAO,EAAE,QAAQ,QAAQ;QAC9C,CAAC,GAAG,iBAAiB,OAAO,EAAE,QAAQ,QAAQ;QAC9C,OAAO;IACT;IACA,CAAC,GAAG,WAAW,OAAO,EAAE,kBAAkB;IAC1C,OAAO,CAAC,GAAG,cAAc,OAAO,EAAE;AACpC,EAAE;AACF,IAAI,iBAAiB,QAAQ,cAAc,GAAG,SAAU,cAAc;IACpE,SAAS;QACP,IAAI;QACJ,CAAC,GAAG,iBAAiB,OAAO,EAAE,IAAI,EAAE;QACpC,IAAK,IAAI,QAAQ,UAAU,MAAM,EAAE,OAAO,IAAI,MAAM,QAAQ,QAAQ,GAAG,QAAQ,OAAO,QAAS;YAC7F,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC,MAAM;QAChC;QACA,SAAS,WAAW,IAAI,EAAE,gBAAgB,EAAE,CAAC,MAAM,CAAC;QACpD,CAAC,GAAG,iBAAiB,OAAO,EAAE,QAAQ,QAAQ;QAC9C,CAAC,GAAG,iBAAiB,OAAO,EAAE,QAAQ,QAAQ;QAC9C,OAAO;IACT;IACA,CAAC,GAAG,WAAW,OAAO,EAAE,gBAAgB;IACxC,OAAO,CAAC,GAAG,cAAc,OAAO,EAAE;AACpC,EAAE;AACF,IAAI,wBAAwB,QAAQ,qBAAqB,GAAG,SAAU,cAAc;IAClF,SAAS;QACP,IAAI;QACJ,CAAC,GAAG,iBAAiB,OAAO,EAAE,IAAI,EAAE;QACpC,IAAK,IAAI,QAAQ,UAAU,MAAM,EAAE,OAAO,IAAI,MAAM,QAAQ,QAAQ,GAAG,QAAQ,OAAO,QAAS;YAC7F,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC,MAAM;QAChC;QACA,SAAS,WAAW,IAAI,EAAE,uBAAuB,EAAE,CAAC,MAAM,CAAC;QAC3D,CAAC,GAAG,iBAAiB,OAAO,EAAE,QAAQ,QAAQ;QAC9C,CAAC,GAAG,iBAAiB,OAAO,EAAE,QAAQ,QAAQ;QAC9C,OAAO;IACT;IACA,CAAC,GAAG,WAAW,OAAO,EAAE,uBAAuB;IAC/C,OAAO,CAAC,GAAG,cAAc,OAAO,EAAE;AACpC,EAAE;AACF,IAAI,sBAAsB,QAAQ,mBAAmB,GAAG,SAAU,cAAc;IAC9E,SAAS;QACP,IAAI;QACJ,CAAC,GAAG,iBAAiB,OAAO,EAAE,IAAI,EAAE;QACpC,IAAK,IAAI,QAAQ,UAAU,MAAM,EAAE,OAAO,IAAI,MAAM,QAAQ,QAAQ,GAAG,QAAQ,OAAO,QAAS;YAC7F,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC,MAAM;QAChC;QACA,SAAS,WAAW,IAAI,EAAE,qBAAqB,EAAE,CAAC,MAAM,CAAC;QACzD,CAAC,GAAG,iBAAiB,OAAO,EAAE,QAAQ,QAAQ;QAC9C,CAAC,GAAG,iBAAiB,OAAO,EAAE,QAAQ,QAAQ;QAC9C,OAAO;IACT;IACA,CAAC,GAAG,WAAW,OAAO,EAAE,qBAAqB;IAC7C,OAAO,CAAC,GAAG,cAAc,OAAO,EAAE;AACpC,EAAE;AACF,IAAI,qBAAqB,QAAQ,kBAAkB,GAAG,SAAU,cAAc;IAC5E,SAAS;QACP,IAAI;QACJ,CAAC,GAAG,iBAAiB,OAAO,EAAE,IAAI,EAAE;QACpC,IAAK,IAAI,QAAQ,UAAU,MAAM,EAAE,OAAO,IAAI,MAAM,QAAQ,QAAQ,GAAG,QAAQ,OAAO,QAAS;YAC7F,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC,MAAM;QAChC;QACA,UAAU,WAAW,IAAI,EAAE,oBAAoB,EAAE,CAAC,MAAM,CAAC;QACzD,CAAC,GAAG,iBAAiB,OAAO,EAAE,SAAS,QAAQ;QAC/C,CAAC,GAAG,iBAAiB,OAAO,EAAE,SAAS,QAAQ;QAC/C,OAAO;IACT;IACA,CAAC,GAAG,WAAW,OAAO,EAAE,oBAAoB;IAC5C,OAAO,CAAC,GAAG,cAAc,OAAO,EAAE;AACpC,EAAE;AACF,SAAS,WAAW,CAAC;IACnB,OAAO,EAAE,OAAO,CAAC,YAAY,OAAO,WAAW;AACjD;AACA,SAAS,WAAW,CAAC;IACnB,OAAO,GAAG,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,WAAW,IAAI,MAAM,CAAC,EAAE,KAAK,CAAC;AACtD;AACA,SAAS,mBAAmB,OAAO,EAAE,MAAM;IACzC,OAAO,OAAO,IAAI,CAAC,SAAS,MAAM,CAAC,SAAU,GAAG,EAAE,IAAI;QACpD,GAAG,CAAC,KAAK,GAAG,CAAC,GAAG,mBAAmB,OAAO,EAAE,aAAa,OAAO,CAAC,IAAI,CAAC,SAAS;YAC7E,IAAI,QACF,QAAQ;YACV,OAAO,aAAa,OAAO,CAAC,IAAI,CAAC,SAAS,SAAS,QAAQ;gBACzD,MAAO,EAAG,OAAQ,SAAS,IAAI,GAAG,SAAS,IAAI;oBAC7C,KAAK;wBACH,SAAS,IAAI,GAAG;wBAChB,SAAS,OAAO,CAAC,KAAK;wBACtB,SAAS,IAAI,GAAG;wBAChB,OAAO,OAAO,KAAK,CAAC,KAAK,GAAG;oBAC9B,KAAK;wBACH,OAAO,SAAS,MAAM,CAAC,UAAU,SAAS,IAAI;oBAChD,KAAK;wBACH,SAAS,IAAI,GAAG;wBAChB,SAAS,EAAE,GAAG,QAAQ,CAAC,QAAQ,CAAC;wBAChC,OAAO,KAAK,CAAC,GAAG,MAAM,CAAC,WAAW,OAAO,iBAAiB,SAAS,EAAE;oBACvE,KAAK;oBACL,KAAK;wBACH,OAAO,SAAS,IAAI;gBACxB;YACF,GAAG,SAAS,MAAM;gBAAC;oBAAC;oBAAG;iBAAE;aAAC;QAC5B;QACA,OAAO;IACT,GAAG,CAAC;AACN;AACA,SAAS,oBAAoB,OAAO,EAAE,MAAM;IAC1C,IAAI,CAAC,SAAS;IACd,OAAO,OAAO,IAAI,CAAC,SAAS,MAAM,CAAC,SAAU,GAAG,EAAE,IAAI;QACpD,GAAG,CAAC,KAAK,GAAG,CAAC,GAAG,mBAAmB,OAAO,EAAE,aAAa,OAAO,CAAC,IAAI,CAAC,SAAS;YAC7E,IAAI,QACF,MACA,QACA,QACA,GACA,SAAS;YACX,OAAO,aAAa,OAAO,CAAC,IAAI,CAAC,SAAS,UAAU,SAAS;gBAC3D,MAAO,EAAG,OAAQ,UAAU,IAAI,GAAG,UAAU,IAAI;oBAC/C,KAAK;wBACH,UAAU,IAAI,GAAG;wBACjB,IAAK,SAAS,OAAO,MAAM,EAAE,OAAO,IAAI,MAAM,SAAS,SAAS,GAAG,SAAS,QAAQ,SAAU;4BAC5F,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC,OAAO;wBAC/B;wBACA,OAAO,KAAK,CAAC,WAAW,MAAM,CAAC,OAAO;4BACpC,MAAM;wBACR;wBACA,SAAS,OAAO,CAAC,KAAK;wBACtB,UAAU,IAAI,GAAG;wBACjB,OAAO,OAAO,KAAK,CAAC,KAAK,GAAG;oBAC9B,KAAK;wBACH,OAAO,UAAU,MAAM,CAAC,UAAU,UAAU,IAAI;oBAClD,KAAK;wBACH,UAAU,IAAI,GAAG;wBACjB,UAAU,EAAE,GAAG,SAAS,CAAC,QAAQ,CAAC;wBAClC,OAAO,KAAK,CAAC,iBAAiB,MAAM,CAAC,OAAO,UAAU,EAAE;wBACxD,IAAI,IAAI,aAAa,UAAU,EAAE;wBACjC,EAAE,IAAI,GAAG,GAAG,MAAM,CAAC,WAAW,OAAO;wBACrC,MAAM;oBACR,KAAK;oBACL,KAAK;wBACH,OAAO,UAAU,IAAI;gBACzB;YACF,GAAG,UAAU,MAAM;gBAAC;oBAAC;oBAAG;iBAAE;aAAC;QAC7B;QACA,OAAO;IACT,GAAG,CAAC;AACN", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 290, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/node_modules/next-auth/utils/logger.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nexports.proxyLogger = proxyLogger;\nexports.setLogger = setLogger;\nvar _regenerator = _interopRequireDefault(require(\"@babel/runtime/regenerator\"));\nvar _defineProperty2 = _interopRequireDefault(require(\"@babel/runtime/helpers/defineProperty\"));\nvar _asyncToGenerator2 = _interopRequireDefault(require(\"@babel/runtime/helpers/asyncToGenerator\"));\nvar _errors = require(\"../core/errors\");\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0, _defineProperty2.default)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction formatError(o) {\n  if (o instanceof Error && !(o instanceof _errors.UnknownError)) {\n    return {\n      message: o.message,\n      stack: o.stack,\n      name: o.name\n    };\n  }\n  if (hasErrorProperty(o)) {\n    var _o$message;\n    o.error = formatError(o.error);\n    o.message = (_o$message = o.message) !== null && _o$message !== void 0 ? _o$message : o.error.message;\n  }\n  return o;\n}\nfunction hasErrorProperty(x) {\n  return !!(x !== null && x !== void 0 && x.error);\n}\nvar _logger = {\n  error: function error(code, metadata) {\n    metadata = formatError(metadata);\n    console.error(\"[next-auth][error][\".concat(code, \"]\"), \"\\nhttps://next-auth.js.org/errors#\".concat(code.toLowerCase()), metadata.message, metadata);\n  },\n  warn: function warn(code) {\n    console.warn(\"[next-auth][warn][\".concat(code, \"]\"), \"\\nhttps://next-auth.js.org/warnings#\".concat(code.toLowerCase()));\n  },\n  debug: function debug(code, metadata) {\n    console.log(\"[next-auth][debug][\".concat(code, \"]\"), metadata);\n  }\n};\nfunction setLogger() {\n  var newLogger = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  var debug = arguments.length > 1 ? arguments[1] : undefined;\n  if (!debug) _logger.debug = function () {};\n  if (newLogger.error) _logger.error = newLogger.error;\n  if (newLogger.warn) _logger.warn = newLogger.warn;\n  if (newLogger.debug) _logger.debug = newLogger.debug;\n}\nvar _default = exports.default = _logger;\nfunction proxyLogger() {\n  var logger = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : _logger;\n  var basePath = arguments.length > 1 ? arguments[1] : undefined;\n  try {\n    if (typeof window === \"undefined\") {\n      return logger;\n    }\n    var clientLogger = {};\n    var _loop = function _loop(level) {\n      clientLogger[level] = function () {\n        var _ref = (0, _asyncToGenerator2.default)(_regenerator.default.mark(function _callee(code, metadata) {\n          var url, body;\n          return _regenerator.default.wrap(function _callee$(_context) {\n            while (1) switch (_context.prev = _context.next) {\n              case 0:\n                _logger[level](code, metadata);\n                if (level === \"error\") {\n                  metadata = formatError(metadata);\n                }\n                ;\n                metadata.client = true;\n                url = \"\".concat(basePath, \"/_log\");\n                body = new URLSearchParams(_objectSpread({\n                  level: level,\n                  code: code\n                }, metadata));\n                if (!navigator.sendBeacon) {\n                  _context.next = 8;\n                  break;\n                }\n                return _context.abrupt(\"return\", navigator.sendBeacon(url, body));\n              case 8:\n                _context.next = 10;\n                return fetch(url, {\n                  method: \"POST\",\n                  body: body,\n                  keepalive: true\n                });\n              case 10:\n                return _context.abrupt(\"return\", _context.sent);\n              case 11:\n              case \"end\":\n                return _context.stop();\n            }\n          }, _callee);\n        }));\n        return function (_x, _x2) {\n          return _ref.apply(this, arguments);\n        };\n      }();\n    };\n    for (var level in logger) {\n      _loop(level);\n    }\n    return clientLogger;\n  } catch (_unused) {\n    return _logger;\n  }\n}"], "names": [], "mappings": "AAAA;AAEA,IAAI;AACJ,OAAO,cAAc,CAAC,SAAS,cAAc;IAC3C,OAAO;AACT;AACA,QAAQ,OAAO,GAAG,KAAK;AACvB,QAAQ,WAAW,GAAG;AACtB,QAAQ,SAAS,GAAG;AACpB,IAAI,eAAe;AACnB,IAAI,mBAAmB;AACvB,IAAI,qBAAqB;AACzB,IAAI;AACJ,SAAS,QAAQ,CAAC,EAAE,CAAC;IAAI,IAAI,IAAI,OAAO,IAAI,CAAC;IAAI,IAAI,OAAO,qBAAqB,EAAE;QAAE,IAAI,IAAI,OAAO,qBAAqB,CAAC;QAAI,KAAK,CAAC,IAAI,EAAE,MAAM,CAAC,SAAU,CAAC;YAAI,OAAO,OAAO,wBAAwB,CAAC,GAAG,GAAG,UAAU;QAAE,EAAE,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG;IAAI;IAAE,OAAO;AAAG;AAC9P,SAAS,cAAc,CAAC;IAAI,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAAK;QAAE,IAAI,IAAI,QAAQ,SAAS,CAAC,EAAE,GAAG,SAAS,CAAC,EAAE,GAAG,CAAC;QAAG,IAAI,IAAI,QAAQ,OAAO,IAAI,CAAC,GAAG,OAAO,CAAC,SAAU,CAAC;YAAI,CAAC,GAAG,iBAAiB,OAAO,EAAE,GAAG,GAAG,CAAC,CAAC,EAAE;QAAG,KAAK,OAAO,yBAAyB,GAAG,OAAO,gBAAgB,CAAC,GAAG,OAAO,yBAAyB,CAAC,MAAM,QAAQ,OAAO,IAAI,OAAO,CAAC,SAAU,CAAC;YAAI,OAAO,cAAc,CAAC,GAAG,GAAG,OAAO,wBAAwB,CAAC,GAAG;QAAK;IAAI;IAAE,OAAO;AAAG;AACpc,SAAS,YAAY,CAAC;IACpB,IAAI,aAAa,SAAS,CAAC,CAAC,aAAa,QAAQ,YAAY,GAAG;QAC9D,OAAO;YACL,SAAS,EAAE,OAAO;YAClB,OAAO,EAAE,KAAK;YACd,MAAM,EAAE,IAAI;QACd;IACF;IACA,IAAI,iBAAiB,IAAI;QACvB,IAAI;QACJ,EAAE,KAAK,GAAG,YAAY,EAAE,KAAK;QAC7B,EAAE,OAAO,GAAG,CAAC,aAAa,EAAE,OAAO,MAAM,QAAQ,eAAe,KAAK,IAAI,aAAa,EAAE,KAAK,CAAC,OAAO;IACvG;IACA,OAAO;AACT;AACA,SAAS,iBAAiB,CAAC;IACzB,OAAO,CAAC,CAAC,CAAC,MAAM,QAAQ,MAAM,KAAK,KAAK,EAAE,KAAK;AACjD;AACA,IAAI,UAAU;IACZ,OAAO,SAAS,MAAM,IAAI,EAAE,QAAQ;QAClC,WAAW,YAAY;QACvB,QAAQ,KAAK,CAAC,sBAAsB,MAAM,CAAC,MAAM,MAAM,qCAAqC,MAAM,CAAC,KAAK,WAAW,KAAK,SAAS,OAAO,EAAE;IAC5I;IACA,MAAM,SAAS,KAAK,IAAI;QACtB,QAAQ,IAAI,CAAC,qBAAqB,MAAM,CAAC,MAAM,MAAM,uCAAuC,MAAM,CAAC,KAAK,WAAW;IACrH;IACA,OAAO,SAAS,MAAM,IAAI,EAAE,QAAQ;QAClC,QAAQ,GAAG,CAAC,sBAAsB,MAAM,CAAC,MAAM,MAAM;IACvD;AACF;AACA,SAAS;IACP,IAAI,YAAY,UAAU,MAAM,GAAG,KAAK,SAAS,CAAC,EAAE,KAAK,YAAY,SAAS,CAAC,EAAE,GAAG,CAAC;IACrF,IAAI,QAAQ,UAAU,MAAM,GAAG,IAAI,SAAS,CAAC,EAAE,GAAG;IAClD,IAAI,CAAC,OAAO,QAAQ,KAAK,GAAG,YAAa;IACzC,IAAI,UAAU,KAAK,EAAE,QAAQ,KAAK,GAAG,UAAU,KAAK;IACpD,IAAI,UAAU,IAAI,EAAE,QAAQ,IAAI,GAAG,UAAU,IAAI;IACjD,IAAI,UAAU,KAAK,EAAE,QAAQ,KAAK,GAAG,UAAU,KAAK;AACtD;AACA,IAAI,WAAW,QAAQ,OAAO,GAAG;AACjC,SAAS;IACP,IAAI,SAAS,UAAU,MAAM,GAAG,KAAK,SAAS,CAAC,EAAE,KAAK,YAAY,SAAS,CAAC,EAAE,GAAG;IACjF,IAAI,WAAW,UAAU,MAAM,GAAG,IAAI,SAAS,CAAC,EAAE,GAAG;IACrD,IAAI;QACF,wCAAmC;YACjC,OAAO;QACT;;QACA,IAAI;QACJ,IAAI;QA2CC,IAAI;IAIX,EAAE,OAAO,SAAS;QAChB,OAAO;IACT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 382, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/node_modules/next-auth/utils/detect-origin.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.detectOrigin = detectOrigin;\nfunction detectOrigin(forwardedHost, protocol) {\n  var _process$env$VERCEL;\n  if ((_process$env$VERCEL = process.env.VERCEL) !== null && _process$env$VERCEL !== void 0 ? _process$env$VERCEL : process.env.AUTH_TRUST_HOST) return `${protocol === \"http\" ? \"http\" : \"https\"}://${forwardedHost}`;\n  return process.env.NEXTAUTH_URL;\n}"], "names": [], "mappings": "AAAA;AAEA,OAAO,cAAc,CAAC,SAAS,cAAc;IAC3C,OAAO;AACT;AACA,QAAQ,YAAY,GAAG;AACvB,SAAS,aAAa,aAAa,EAAE,QAAQ;IAC3C,IAAI;IACJ,IAAI,CAAC,sBAAsB,QAAQ,GAAG,CAAC,MAAM,MAAM,QAAQ,wBAAwB,KAAK,IAAI,sBAAsB,QAAQ,GAAG,CAAC,eAAe,EAAE,OAAO,GAAG,aAAa,SAAS,SAAS,QAAQ,GAAG,EAAE,eAAe;IACpN,OAAO,QAAQ,GAAG,CAAC,YAAY;AACjC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 397, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/node_modules/next-auth/core/lib/oauth/client.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.openidClient = openidClient;\nvar _openidClient = require(\"openid-client\");\nasync function openidClient(options) {\n  const provider = options.provider;\n  if (provider.httpOptions) _openidClient.custom.setHttpOptionsDefaults(provider.httpOptions);\n  let issuer;\n  if (provider.wellKnown) {\n    issuer = await _openidClient.Issuer.discover(provider.wellKnown);\n  } else {\n    var _provider$authorizati, _provider$token, _provider$userinfo;\n    issuer = new _openidClient.Issuer({\n      issuer: provider.issuer,\n      authorization_endpoint: (_provider$authorizati = provider.authorization) === null || _provider$authorizati === void 0 ? void 0 : _provider$authorizati.url,\n      token_endpoint: (_provider$token = provider.token) === null || _provider$token === void 0 ? void 0 : _provider$token.url,\n      userinfo_endpoint: (_provider$userinfo = provider.userinfo) === null || _provider$userinfo === void 0 ? void 0 : _provider$userinfo.url,\n      jwks_uri: provider.jwks_endpoint\n    });\n  }\n  const client = new issuer.Client({\n    client_id: provider.clientId,\n    client_secret: provider.clientSecret,\n    redirect_uris: [provider.callbackUrl],\n    ...provider.client\n  }, provider.jwks);\n  client[_openidClient.custom.clock_tolerance] = 10;\n  return client;\n}"], "names": [], "mappings": "AAAA;AAEA,OAAO,cAAc,CAAC,SAAS,cAAc;IAC3C,OAAO;AACT;AACA,QAAQ,YAAY,GAAG;AACvB,IAAI;AACJ,eAAe,aAAa,OAAO;IACjC,MAAM,WAAW,QAAQ,QAAQ;IACjC,IAAI,SAAS,WAAW,EAAE,cAAc,MAAM,CAAC,sBAAsB,CAAC,SAAS,WAAW;IAC1F,IAAI;IACJ,IAAI,SAAS,SAAS,EAAE;QACtB,SAAS,MAAM,cAAc,MAAM,CAAC,QAAQ,CAAC,SAAS,SAAS;IACjE,OAAO;QACL,IAAI,uBAAuB,iBAAiB;QAC5C,SAAS,IAAI,cAAc,MAAM,CAAC;YAChC,QAAQ,SAAS,MAAM;YACvB,wBAAwB,CAAC,wBAAwB,SAAS,aAAa,MAAM,QAAQ,0BAA0B,KAAK,IAAI,KAAK,IAAI,sBAAsB,GAAG;YAC1J,gBAAgB,CAAC,kBAAkB,SAAS,KAAK,MAAM,QAAQ,oBAAoB,KAAK,IAAI,KAAK,IAAI,gBAAgB,GAAG;YACxH,mBAAmB,CAAC,qBAAqB,SAAS,QAAQ,MAAM,QAAQ,uBAAuB,KAAK,IAAI,KAAK,IAAI,mBAAmB,GAAG;YACvI,UAAU,SAAS,aAAa;QAClC;IACF;IACA,MAAM,SAAS,IAAI,OAAO,MAAM,CAAC;QAC/B,WAAW,SAAS,QAAQ;QAC5B,eAAe,SAAS,YAAY;QACpC,eAAe;YAAC,SAAS,WAAW;SAAC;QACrC,GAAG,SAAS,MAAM;IACpB,GAAG,SAAS,IAAI;IAChB,MAAM,CAAC,cAAc,MAAM,CAAC,eAAe,CAAC,GAAG;IAC/C,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 435, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/node_modules/next-auth/core/lib/oauth/client-legacy.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.oAuth1Client = oAuth1Client;\nexports.oAuth1TokenStore = void 0;\nvar _oauth = require(\"oauth\");\nfunction oAuth1Client(options) {\n  var _provider$version, _provider$encoding;\n  const provider = options.provider;\n  const oauth1Client = new _oauth.OAuth(provider.requestTokenUrl, provider.accessTokenUrl, provider.clientId, provider.clientSecret, (_provider$version = provider.version) !== null && _provider$version !== void 0 ? _provider$version : \"1.0\", provider.callbackUrl, (_provider$encoding = provider.encoding) !== null && _provider$encoding !== void 0 ? _provider$encoding : \"HMAC-SHA1\");\n  const originalGet = oauth1Client.get.bind(oauth1Client);\n  oauth1Client.get = async (...args) => {\n    return await new Promise((resolve, reject) => {\n      originalGet(...args, (error, result) => {\n        if (error) {\n          return reject(error);\n        }\n        resolve(result);\n      });\n    });\n  };\n  const originalGetOAuth1AccessToken = oauth1Client.getOAuthAccessToken.bind(oauth1Client);\n  oauth1Client.getOAuthAccessToken = async (...args) => {\n    return await new Promise((resolve, reject) => {\n      originalGetOAuth1AccessToken(...args, (error, oauth_token, oauth_token_secret) => {\n        if (error) {\n          return reject(error);\n        }\n        resolve({\n          oauth_token,\n          oauth_token_secret\n        });\n      });\n    });\n  };\n  const originalGetOAuthRequestToken = oauth1Client.getOAuthRequestToken.bind(oauth1Client);\n  oauth1Client.getOAuthRequestToken = async (params = {}) => {\n    return await new Promise((resolve, reject) => {\n      originalGetOAuthRequestToken(params, (error, oauth_token, oauth_token_secret, params) => {\n        if (error) {\n          return reject(error);\n        }\n        resolve({\n          oauth_token,\n          oauth_token_secret,\n          params\n        });\n      });\n    });\n  };\n  return oauth1Client;\n}\nconst oAuth1TokenStore = exports.oAuth1TokenStore = new Map();"], "names": [], "mappings": "AAAA;AAEA,OAAO,cAAc,CAAC,SAAS,cAAc;IAC3C,OAAO;AACT;AACA,QAAQ,YAAY,GAAG;AACvB,QAAQ,gBAAgB,GAAG,KAAK;AAChC,IAAI;AACJ,SAAS,aAAa,OAAO;IAC3B,IAAI,mBAAmB;IACvB,MAAM,WAAW,QAAQ,QAAQ;IACjC,MAAM,eAAe,IAAI,OAAO,KAAK,CAAC,SAAS,eAAe,EAAE,SAAS,cAAc,EAAE,SAAS,QAAQ,EAAE,SAAS,YAAY,EAAE,CAAC,oBAAoB,SAAS,OAAO,MAAM,QAAQ,sBAAsB,KAAK,IAAI,oBAAoB,OAAO,SAAS,WAAW,EAAE,CAAC,qBAAqB,SAAS,QAAQ,MAAM,QAAQ,uBAAuB,KAAK,IAAI,qBAAqB;IAChX,MAAM,cAAc,aAAa,GAAG,CAAC,IAAI,CAAC;IAC1C,aAAa,GAAG,GAAG,OAAO,GAAG;QAC3B,OAAO,MAAM,IAAI,QAAQ,CAAC,SAAS;YACjC,eAAe,MAAM,CAAC,OAAO;gBAC3B,IAAI,OAAO;oBACT,OAAO,OAAO;gBAChB;gBACA,QAAQ;YACV;QACF;IACF;IACA,MAAM,+BAA+B,aAAa,mBAAmB,CAAC,IAAI,CAAC;IAC3E,aAAa,mBAAmB,GAAG,OAAO,GAAG;QAC3C,OAAO,MAAM,IAAI,QAAQ,CAAC,SAAS;YACjC,gCAAgC,MAAM,CAAC,OAAO,aAAa;gBACzD,IAAI,OAAO;oBACT,OAAO,OAAO;gBAChB;gBACA,QAAQ;oBACN;oBACA;gBACF;YACF;QACF;IACF;IACA,MAAM,+BAA+B,aAAa,oBAAoB,CAAC,IAAI,CAAC;IAC5E,aAAa,oBAAoB,GAAG,OAAO,SAAS,CAAC,CAAC;QACpD,OAAO,MAAM,IAAI,QAAQ,CAAC,SAAS;YACjC,6BAA6B,QAAQ,CAAC,OAAO,aAAa,oBAAoB;gBAC5E,IAAI,OAAO;oBACT,OAAO,OAAO;gBAChB;gBACA,QAAQ;oBACN;oBACA;oBACA;gBACF;YACF;QACF;IACF;IACA,OAAO;AACT;AACA,MAAM,mBAAmB,QAAQ,gBAAgB,GAAG,IAAI", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 494, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/node_modules/next-auth/core/lib/cookie.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.SessionStore = void 0;\nexports.defaultCookies = defaultCookies;\nfunction _classPrivateMethodInitSpec(e, a) { _checkPrivateRedeclaration(e, a), a.add(e); }\nfunction _classPrivateFieldInitSpec(e, t, a) { _checkPrivateRedeclaration(e, t), t.set(e, a); }\nfunction _checkPrivateRedeclaration(e, t) { if (t.has(e)) throw new TypeError(\"Cannot initialize the same private elements twice on an object\"); }\nfunction _classPrivateFieldGet(s, a) { return s.get(_assertClassBrand(s, a)); }\nfunction _classPrivateFieldSet(s, a, r) { return s.set(_assertClassBrand(s, a), r), r; }\nfunction _assertClassBrand(e, t, n) { if (\"function\" == typeof e ? e === t : e.has(t)) return arguments.length < 3 ? t : n; throw new TypeError(\"Private element is not present on this object\"); }\nconst ALLOWED_COOKIE_SIZE = 4096;\nconst ESTIMATED_EMPTY_COOKIE_SIZE = 163;\nconst CHUNK_SIZE = ALLOWED_COOKIE_SIZE - ESTIMATED_EMPTY_COOKIE_SIZE;\nfunction defaultCookies(useSecureCookies) {\n  const cookiePrefix = useSecureCookies ? \"__Secure-\" : \"\";\n  return {\n    sessionToken: {\n      name: `${cookiePrefix}next-auth.session-token`,\n      options: {\n        httpOnly: true,\n        sameSite: \"lax\",\n        path: \"/\",\n        secure: useSecureCookies\n      }\n    },\n    callbackUrl: {\n      name: `${cookiePrefix}next-auth.callback-url`,\n      options: {\n        httpOnly: true,\n        sameSite: \"lax\",\n        path: \"/\",\n        secure: useSecureCookies\n      }\n    },\n    csrfToken: {\n      name: `${useSecureCookies ? \"__Host-\" : \"\"}next-auth.csrf-token`,\n      options: {\n        httpOnly: true,\n        sameSite: \"lax\",\n        path: \"/\",\n        secure: useSecureCookies\n      }\n    },\n    pkceCodeVerifier: {\n      name: `${cookiePrefix}next-auth.pkce.code_verifier`,\n      options: {\n        httpOnly: true,\n        sameSite: \"lax\",\n        path: \"/\",\n        secure: useSecureCookies,\n        maxAge: 60 * 15\n      }\n    },\n    state: {\n      name: `${cookiePrefix}next-auth.state`,\n      options: {\n        httpOnly: true,\n        sameSite: \"lax\",\n        path: \"/\",\n        secure: useSecureCookies,\n        maxAge: 60 * 15\n      }\n    },\n    nonce: {\n      name: `${cookiePrefix}next-auth.nonce`,\n      options: {\n        httpOnly: true,\n        sameSite: \"lax\",\n        path: \"/\",\n        secure: useSecureCookies\n      }\n    }\n  };\n}\nvar _chunks = new WeakMap();\nvar _option = new WeakMap();\nvar _logger = new WeakMap();\nvar _SessionStore_brand = new WeakSet();\nclass SessionStore {\n  constructor(option, req, logger) {\n    _classPrivateMethodInitSpec(this, _SessionStore_brand);\n    _classPrivateFieldInitSpec(this, _chunks, {});\n    _classPrivateFieldInitSpec(this, _option, void 0);\n    _classPrivateFieldInitSpec(this, _logger, void 0);\n    _classPrivateFieldSet(_logger, this, logger);\n    _classPrivateFieldSet(_option, this, option);\n    const {\n      cookies: _cookies\n    } = req;\n    const {\n      name: cookieName\n    } = option;\n    if (typeof (_cookies === null || _cookies === void 0 ? void 0 : _cookies.getAll) === \"function\") {\n      for (const {\n        name,\n        value\n      } of _cookies.getAll()) {\n        if (name.startsWith(cookieName)) {\n          _classPrivateFieldGet(_chunks, this)[name] = value;\n        }\n      }\n    } else if (_cookies instanceof Map) {\n      for (const name of _cookies.keys()) {\n        if (name.startsWith(cookieName)) _classPrivateFieldGet(_chunks, this)[name] = _cookies.get(name);\n      }\n    } else {\n      for (const name in _cookies) {\n        if (name.startsWith(cookieName)) _classPrivateFieldGet(_chunks, this)[name] = _cookies[name];\n      }\n    }\n  }\n  get value() {\n    const sortedKeys = Object.keys(_classPrivateFieldGet(_chunks, this)).sort((a, b) => {\n      var _a$split$pop, _b$split$pop;\n      const aSuffix = parseInt((_a$split$pop = a.split(\".\").pop()) !== null && _a$split$pop !== void 0 ? _a$split$pop : \"0\");\n      const bSuffix = parseInt((_b$split$pop = b.split(\".\").pop()) !== null && _b$split$pop !== void 0 ? _b$split$pop : \"0\");\n      return aSuffix - bSuffix;\n    });\n    return sortedKeys.map(key => _classPrivateFieldGet(_chunks, this)[key]).join(\"\");\n  }\n  chunk(value, options) {\n    const cookies = _assertClassBrand(_SessionStore_brand, this, _clean).call(this);\n    const chunked = _assertClassBrand(_SessionStore_brand, this, _chunk).call(this, {\n      name: _classPrivateFieldGet(_option, this).name,\n      value,\n      options: {\n        ..._classPrivateFieldGet(_option, this).options,\n        ...options\n      }\n    });\n    for (const chunk of chunked) {\n      cookies[chunk.name] = chunk;\n    }\n    return Object.values(cookies);\n  }\n  clean() {\n    return Object.values(_assertClassBrand(_SessionStore_brand, this, _clean).call(this));\n  }\n}\nexports.SessionStore = SessionStore;\nfunction _chunk(cookie) {\n  const chunkCount = Math.ceil(cookie.value.length / CHUNK_SIZE);\n  if (chunkCount === 1) {\n    _classPrivateFieldGet(_chunks, this)[cookie.name] = cookie.value;\n    return [cookie];\n  }\n  const cookies = [];\n  for (let i = 0; i < chunkCount; i++) {\n    const name = `${cookie.name}.${i}`;\n    const value = cookie.value.substr(i * CHUNK_SIZE, CHUNK_SIZE);\n    cookies.push({\n      ...cookie,\n      name,\n      value\n    });\n    _classPrivateFieldGet(_chunks, this)[name] = value;\n  }\n  _classPrivateFieldGet(_logger, this).debug(\"CHUNKING_SESSION_COOKIE\", {\n    message: `Session cookie exceeds allowed ${ALLOWED_COOKIE_SIZE} bytes.`,\n    emptyCookieSize: ESTIMATED_EMPTY_COOKIE_SIZE,\n    valueSize: cookie.value.length,\n    chunks: cookies.map(c => c.value.length + ESTIMATED_EMPTY_COOKIE_SIZE)\n  });\n  return cookies;\n}\nfunction _clean() {\n  const cleanedChunks = {};\n  for (const name in _classPrivateFieldGet(_chunks, this)) {\n    var _classPrivateFieldGet2;\n    (_classPrivateFieldGet2 = _classPrivateFieldGet(_chunks, this)) === null || _classPrivateFieldGet2 === void 0 || delete _classPrivateFieldGet2[name];\n    cleanedChunks[name] = {\n      name,\n      value: \"\",\n      options: {\n        ..._classPrivateFieldGet(_option, this).options,\n        maxAge: 0\n      }\n    };\n  }\n  return cleanedChunks;\n}"], "names": [], "mappings": "AAAA;AAEA,OAAO,cAAc,CAAC,SAAS,cAAc;IAC3C,OAAO;AACT;AACA,QAAQ,YAAY,GAAG,KAAK;AAC5B,QAAQ,cAAc,GAAG;AACzB,SAAS,4BAA4B,CAAC,EAAE,CAAC;IAAI,2BAA2B,GAAG,IAAI,EAAE,GAAG,CAAC;AAAI;AACzF,SAAS,2BAA2B,CAAC,EAAE,CAAC,EAAE,CAAC;IAAI,2BAA2B,GAAG,IAAI,EAAE,GAAG,CAAC,GAAG;AAAI;AAC9F,SAAS,2BAA2B,CAAC,EAAE,CAAC;IAAI,IAAI,EAAE,GAAG,CAAC,IAAI,MAAM,IAAI,UAAU;AAAmE;AACjJ,SAAS,sBAAsB,CAAC,EAAE,CAAC;IAAI,OAAO,EAAE,GAAG,CAAC,kBAAkB,GAAG;AAAK;AAC9E,SAAS,sBAAsB,CAAC,EAAE,CAAC,EAAE,CAAC;IAAI,OAAO,EAAE,GAAG,CAAC,kBAAkB,GAAG,IAAI,IAAI;AAAG;AACvF,SAAS,kBAAkB,CAAC,EAAE,CAAC,EAAE,CAAC;IAAI,IAAI,cAAc,OAAO,IAAI,MAAM,IAAI,EAAE,GAAG,CAAC,IAAI,OAAO,UAAU,MAAM,GAAG,IAAI,IAAI;IAAG,MAAM,IAAI,UAAU;AAAkD;AAClM,MAAM,sBAAsB;AAC5B,MAAM,8BAA8B;AACpC,MAAM,aAAa,sBAAsB;AACzC,SAAS,eAAe,gBAAgB;IACtC,MAAM,eAAe,mBAAmB,cAAc;IACtD,OAAO;QACL,cAAc;YACZ,MAAM,GAAG,aAAa,uBAAuB,CAAC;YAC9C,SAAS;gBACP,UAAU;gBACV,UAAU;gBACV,MAAM;gBACN,QAAQ;YACV;QACF;QACA,aAAa;YACX,MAAM,GAAG,aAAa,sBAAsB,CAAC;YAC7C,SAAS;gBACP,UAAU;gBACV,UAAU;gBACV,MAAM;gBACN,QAAQ;YACV;QACF;QACA,WAAW;YACT,MAAM,GAAG,mBAAmB,YAAY,GAAG,oBAAoB,CAAC;YAChE,SAAS;gBACP,UAAU;gBACV,UAAU;gBACV,MAAM;gBACN,QAAQ;YACV;QACF;QACA,kBAAkB;YAChB,MAAM,GAAG,aAAa,4BAA4B,CAAC;YACnD,SAAS;gBACP,UAAU;gBACV,UAAU;gBACV,MAAM;gBACN,QAAQ;gBACR,QAAQ,KAAK;YACf;QACF;QACA,OAAO;YACL,MAAM,GAAG,aAAa,eAAe,CAAC;YACtC,SAAS;gBACP,UAAU;gBACV,UAAU;gBACV,MAAM;gBACN,QAAQ;gBACR,QAAQ,KAAK;YACf;QACF;QACA,OAAO;YACL,MAAM,GAAG,aAAa,eAAe,CAAC;YACtC,SAAS;gBACP,UAAU;gBACV,UAAU;gBACV,MAAM;gBACN,QAAQ;YACV;QACF;IACF;AACF;AACA,IAAI,UAAU,IAAI;AAClB,IAAI,UAAU,IAAI;AAClB,IAAI,UAAU,IAAI;AAClB,IAAI,sBAAsB,IAAI;AAC9B,MAAM;IACJ,YAAY,MAAM,EAAE,GAAG,EAAE,MAAM,CAAE;QAC/B,4BAA4B,IAAI,EAAE;QAClC,2BAA2B,IAAI,EAAE,SAAS,CAAC;QAC3C,2BAA2B,IAAI,EAAE,SAAS,KAAK;QAC/C,2BAA2B,IAAI,EAAE,SAAS,KAAK;QAC/C,sBAAsB,SAAS,IAAI,EAAE;QACrC,sBAAsB,SAAS,IAAI,EAAE;QACrC,MAAM,EACJ,SAAS,QAAQ,EAClB,GAAG;QACJ,MAAM,EACJ,MAAM,UAAU,EACjB,GAAG;QACJ,IAAI,OAAO,CAAC,aAAa,QAAQ,aAAa,KAAK,IAAI,KAAK,IAAI,SAAS,MAAM,MAAM,YAAY;YAC/F,KAAK,MAAM,EACT,IAAI,EACJ,KAAK,EACN,IAAI,SAAS,MAAM,GAAI;gBACtB,IAAI,KAAK,UAAU,CAAC,aAAa;oBAC/B,sBAAsB,SAAS,IAAI,CAAC,CAAC,KAAK,GAAG;gBAC/C;YACF;QACF,OAAO,IAAI,oBAAoB,KAAK;YAClC,KAAK,MAAM,QAAQ,SAAS,IAAI,GAAI;gBAClC,IAAI,KAAK,UAAU,CAAC,aAAa,sBAAsB,SAAS,IAAI,CAAC,CAAC,KAAK,GAAG,SAAS,GAAG,CAAC;YAC7F;QACF,OAAO;YACL,IAAK,MAAM,QAAQ,SAAU;gBAC3B,IAAI,KAAK,UAAU,CAAC,aAAa,sBAAsB,SAAS,IAAI,CAAC,CAAC,KAAK,GAAG,QAAQ,CAAC,KAAK;YAC9F;QACF;IACF;IACA,IAAI,QAAQ;QACV,MAAM,aAAa,OAAO,IAAI,CAAC,sBAAsB,SAAS,IAAI,GAAG,IAAI,CAAC,CAAC,GAAG;YAC5E,IAAI,cAAc;YAClB,MAAM,UAAU,SAAS,CAAC,eAAe,EAAE,KAAK,CAAC,KAAK,GAAG,EAAE,MAAM,QAAQ,iBAAiB,KAAK,IAAI,eAAe;YAClH,MAAM,UAAU,SAAS,CAAC,eAAe,EAAE,KAAK,CAAC,KAAK,GAAG,EAAE,MAAM,QAAQ,iBAAiB,KAAK,IAAI,eAAe;YAClH,OAAO,UAAU;QACnB;QACA,OAAO,WAAW,GAAG,CAAC,CAAA,MAAO,sBAAsB,SAAS,IAAI,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC;IAC/E;IACA,MAAM,KAAK,EAAE,OAAO,EAAE;QACpB,MAAM,UAAU,kBAAkB,qBAAqB,IAAI,EAAE,QAAQ,IAAI,CAAC,IAAI;QAC9E,MAAM,UAAU,kBAAkB,qBAAqB,IAAI,EAAE,QAAQ,IAAI,CAAC,IAAI,EAAE;YAC9E,MAAM,sBAAsB,SAAS,IAAI,EAAE,IAAI;YAC/C;YACA,SAAS;gBACP,GAAG,sBAAsB,SAAS,IAAI,EAAE,OAAO;gBAC/C,GAAG,OAAO;YACZ;QACF;QACA,KAAK,MAAM,SAAS,QAAS;YAC3B,OAAO,CAAC,MAAM,IAAI,CAAC,GAAG;QACxB;QACA,OAAO,OAAO,MAAM,CAAC;IACvB;IACA,QAAQ;QACN,OAAO,OAAO,MAAM,CAAC,kBAAkB,qBAAqB,IAAI,EAAE,QAAQ,IAAI,CAAC,IAAI;IACrF;AACF;AACA,QAAQ,YAAY,GAAG;AACvB,SAAS,OAAO,MAAM;IACpB,MAAM,aAAa,KAAK,IAAI,CAAC,OAAO,KAAK,CAAC,MAAM,GAAG;IACnD,IAAI,eAAe,GAAG;QACpB,sBAAsB,SAAS,IAAI,CAAC,CAAC,OAAO,IAAI,CAAC,GAAG,OAAO,KAAK;QAChE,OAAO;YAAC;SAAO;IACjB;IACA,MAAM,UAAU,EAAE;IAClB,IAAK,IAAI,IAAI,GAAG,IAAI,YAAY,IAAK;QACnC,MAAM,OAAO,GAAG,OAAO,IAAI,CAAC,CAAC,EAAE,GAAG;QAClC,MAAM,QAAQ,OAAO,KAAK,CAAC,MAAM,CAAC,IAAI,YAAY;QAClD,QAAQ,IAAI,CAAC;YACX,GAAG,MAAM;YACT;YACA;QACF;QACA,sBAAsB,SAAS,IAAI,CAAC,CAAC,KAAK,GAAG;IAC/C;IACA,sBAAsB,SAAS,IAAI,EAAE,KAAK,CAAC,2BAA2B;QACpE,SAAS,CAAC,+BAA+B,EAAE,oBAAoB,OAAO,CAAC;QACvE,iBAAiB;QACjB,WAAW,OAAO,KAAK,CAAC,MAAM;QAC9B,QAAQ,QAAQ,GAAG,CAAC,CAAA,IAAK,EAAE,KAAK,CAAC,MAAM,GAAG;IAC5C;IACA,OAAO;AACT;AACA,SAAS;IACP,MAAM,gBAAgB,CAAC;IACvB,IAAK,MAAM,QAAQ,sBAAsB,SAAS,IAAI,EAAG;QACvD,IAAI;QACJ,CAAC,yBAAyB,sBAAsB,SAAS,IAAI,CAAC,MAAM,QAAQ,2BAA2B,KAAK,KAAK,OAAO,sBAAsB,CAAC,KAAK;QACpJ,aAAa,CAAC,KAAK,GAAG;YACpB;YACA,OAAO;YACP,SAAS;gBACP,GAAG,sBAAsB,SAAS,IAAI,EAAE,OAAO;gBAC/C,QAAQ;YACV;QACF;IACF;IACA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 690, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/node_modules/next-auth/jwt/types.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});"], "names": [], "mappings": "AAAA;AAEA,OAAO,cAAc,CAAC,SAAS,cAAc;IAC3C,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 699, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/node_modules/next-auth/jwt/index.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nvar _exportNames = {\n  encode: true,\n  decode: true,\n  getToken: true\n};\nexports.decode = decode;\nexports.encode = encode;\nexports.getToken = getToken;\nvar _jose = require(\"jose\");\nvar _hkdf = _interopRequireDefault(require(\"@panva/hkdf\"));\nvar _uuid = require(\"uuid\");\nvar _cookie = require(\"../core/lib/cookie\");\nvar _types = require(\"./types\");\nObject.keys(_types).forEach(function (key) {\n  if (key === \"default\" || key === \"__esModule\") return;\n  if (Object.prototype.hasOwnProperty.call(_exportNames, key)) return;\n  if (key in exports && exports[key] === _types[key]) return;\n  Object.defineProperty(exports, key, {\n    enumerable: true,\n    get: function () {\n      return _types[key];\n    }\n  });\n});\nconst DEFAULT_MAX_AGE = 30 * 24 * 60 * 60;\nconst now = () => Date.now() / 1000 | 0;\nasync function encode(params) {\n  const {\n    token = {},\n    secret,\n    maxAge = DEFAULT_MAX_AGE,\n    salt = \"\"\n  } = params;\n  const encryptionSecret = await getDerivedEncryptionKey(secret, salt);\n  return await new _jose.EncryptJWT(token).setProtectedHeader({\n    alg: \"dir\",\n    enc: \"A256GCM\"\n  }).setIssuedAt().setExpirationTime(now() + maxAge).setJti((0, _uuid.v4)()).encrypt(encryptionSecret);\n}\nasync function decode(params) {\n  const {\n    token,\n    secret,\n    salt = \"\"\n  } = params;\n  if (!token) return null;\n  const encryptionSecret = await getDerivedEncryptionKey(secret, salt);\n  const {\n    payload\n  } = await (0, _jose.jwtDecrypt)(token, encryptionSecret, {\n    clockTolerance: 15\n  });\n  return payload;\n}\nasync function getToken(params) {\n  var _process$env$NEXTAUTH, _process$env$NEXTAUTH2, _process$env$NEXTAUTH3, _req$headers;\n  const {\n    req,\n    secureCookie = (_process$env$NEXTAUTH = (_process$env$NEXTAUTH2 = process.env.NEXTAUTH_URL) === null || _process$env$NEXTAUTH2 === void 0 ? void 0 : _process$env$NEXTAUTH2.startsWith(\"https://\")) !== null && _process$env$NEXTAUTH !== void 0 ? _process$env$NEXTAUTH : !!process.env.VERCEL,\n    cookieName = secureCookie ? \"__Secure-next-auth.session-token\" : \"next-auth.session-token\",\n    raw,\n    decode: _decode = decode,\n    logger = console,\n    secret = (_process$env$NEXTAUTH3 = process.env.NEXTAUTH_SECRET) !== null && _process$env$NEXTAUTH3 !== void 0 ? _process$env$NEXTAUTH3 : process.env.AUTH_SECRET\n  } = params;\n  if (!req) throw new Error(\"Must pass `req` to JWT getToken()\");\n  const sessionStore = new _cookie.SessionStore({\n    name: cookieName,\n    options: {\n      secure: secureCookie\n    }\n  }, {\n    cookies: req.cookies,\n    headers: req.headers\n  }, logger);\n  let token = sessionStore.value;\n  const authorizationHeader = req.headers instanceof Headers ? req.headers.get(\"authorization\") : (_req$headers = req.headers) === null || _req$headers === void 0 ? void 0 : _req$headers.authorization;\n  if (!token && (authorizationHeader === null || authorizationHeader === void 0 ? void 0 : authorizationHeader.split(\" \")[0]) === \"Bearer\") {\n    const urlEncodedToken = authorizationHeader.split(\" \")[1];\n    token = decodeURIComponent(urlEncodedToken);\n  }\n  if (!token) return null;\n  if (raw) return token;\n  try {\n    return await _decode({\n      token,\n      secret\n    });\n  } catch (_unused) {\n    return null;\n  }\n}\nasync function getDerivedEncryptionKey(keyMaterial, salt) {\n  return await (0, _hkdf.default)(\"sha256\", keyMaterial, salt, `NextAuth.js Generated Encryption Key${salt ? ` (${salt})` : \"\"}`, 32);\n}"], "names": [], "mappings": "AAAA;AAEA,IAAI;AACJ,OAAO,cAAc,CAAC,SAAS,cAAc;IAC3C,OAAO;AACT;AACA,IAAI,eAAe;IACjB,QAAQ;IACR,QAAQ;IACR,UAAU;AACZ;AACA,QAAQ,MAAM,GAAG;AACjB,QAAQ,MAAM,GAAG;AACjB,QAAQ,QAAQ,GAAG;AACnB,IAAI;AACJ,IAAI,QAAQ;AACZ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,OAAO,IAAI,CAAC,QAAQ,OAAO,CAAC,SAAU,GAAG;IACvC,IAAI,QAAQ,aAAa,QAAQ,cAAc;IAC/C,IAAI,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,cAAc,MAAM;IAC7D,IAAI,OAAO,WAAW,OAAO,CAAC,IAAI,KAAK,MAAM,CAAC,IAAI,EAAE;IACpD,OAAO,cAAc,CAAC,SAAS,KAAK;QAClC,YAAY;QACZ,KAAK;YACH,OAAO,MAAM,CAAC,IAAI;QACpB;IACF;AACF;AACA,MAAM,kBAAkB,KAAK,KAAK,KAAK;AACvC,MAAM,MAAM,IAAM,KAAK,GAAG,KAAK,OAAO;AACtC,eAAe,OAAO,MAAM;IAC1B,MAAM,EACJ,QAAQ,CAAC,CAAC,EACV,MAAM,EACN,SAAS,eAAe,EACxB,OAAO,EAAE,EACV,GAAG;IACJ,MAAM,mBAAmB,MAAM,wBAAwB,QAAQ;IAC/D,OAAO,MAAM,IAAI,MAAM,UAAU,CAAC,OAAO,kBAAkB,CAAC;QAC1D,KAAK;QACL,KAAK;IACP,GAAG,WAAW,GAAG,iBAAiB,CAAC,QAAQ,QAAQ,MAAM,CAAC,CAAC,GAAG,MAAM,EAAE,KAAK,OAAO,CAAC;AACrF;AACA,eAAe,OAAO,MAAM;IAC1B,MAAM,EACJ,KAAK,EACL,MAAM,EACN,OAAO,EAAE,EACV,GAAG;IACJ,IAAI,CAAC,OAAO,OAAO;IACnB,MAAM,mBAAmB,MAAM,wBAAwB,QAAQ;IAC/D,MAAM,EACJ,OAAO,EACR,GAAG,MAAM,CAAC,GAAG,MAAM,UAAU,EAAE,OAAO,kBAAkB;QACvD,gBAAgB;IAClB;IACA,OAAO;AACT;AACA,eAAe,SAAS,MAAM;IAC5B,IAAI,uBAAuB,wBAAwB,wBAAwB;IAC3E,MAAM,EACJ,GAAG,EACH,eAAe,CAAC,wBAAwB,CAAC,yBAAyB,QAAQ,GAAG,CAAC,YAAY,MAAM,QAAQ,2BAA2B,KAAK,IAAI,KAAK,IAAI,uBAAuB,UAAU,CAAC,WAAW,MAAM,QAAQ,0BAA0B,KAAK,IAAI,wBAAwB,CAAC,CAAC,QAAQ,GAAG,CAAC,MAAM,EAC/R,aAAa,eAAe,qCAAqC,yBAAyB,EAC1F,GAAG,EACH,QAAQ,UAAU,MAAM,EACxB,SAAS,OAAO,EAChB,SAAS,CAAC,yBAAyB,QAAQ,GAAG,CAAC,eAAe,MAAM,QAAQ,2BAA2B,KAAK,IAAI,yBAAyB,QAAQ,GAAG,CAAC,WAAW,EACjK,GAAG;IACJ,IAAI,CAAC,KAAK,MAAM,IAAI,MAAM;IAC1B,MAAM,eAAe,IAAI,QAAQ,YAAY,CAAC;QAC5C,MAAM;QACN,SAAS;YACP,QAAQ;QACV;IACF,GAAG;QACD,SAAS,IAAI,OAAO;QACpB,SAAS,IAAI,OAAO;IACtB,GAAG;IACH,IAAI,QAAQ,aAAa,KAAK;IAC9B,MAAM,sBAAsB,IAAI,OAAO,YAAY,UAAU,IAAI,OAAO,CAAC,GAAG,CAAC,mBAAmB,CAAC,eAAe,IAAI,OAAO,MAAM,QAAQ,iBAAiB,KAAK,IAAI,KAAK,IAAI,aAAa,aAAa;IACtM,IAAI,CAAC,SAAS,CAAC,wBAAwB,QAAQ,wBAAwB,KAAK,IAAI,KAAK,IAAI,oBAAoB,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,UAAU;QACxI,MAAM,kBAAkB,oBAAoB,KAAK,CAAC,IAAI,CAAC,EAAE;QACzD,QAAQ,mBAAmB;IAC7B;IACA,IAAI,CAAC,OAAO,OAAO;IACnB,IAAI,KAAK,OAAO;IAChB,IAAI;QACF,OAAO,MAAM,QAAQ;YACnB;YACA;QACF;IACF,EAAE,OAAO,SAAS;QAChB,OAAO;IACT;AACF;AACA,eAAe,wBAAwB,WAAW,EAAE,IAAI;IACtD,OAAO,MAAM,CAAC,GAAG,MAAM,OAAO,EAAE,UAAU,aAAa,MAAM,CAAC,oCAAoC,EAAE,OAAO,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC,GAAG,IAAI,EAAE;AAClI", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 785, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/node_modules/next-auth/core/lib/oauth/checks.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.pkce = exports.nonce = exports.PKCE_CODE_CHALLENGE_METHOD = void 0;\nexports.signCookie = signCookie;\nexports.state = void 0;\nvar _openidClient = require(\"openid-client\");\nvar jwt = _interopRequireWildcard(require(\"../../../jwt\"));\nfunction _getRequireWildcardCache(e) { if (\"function\" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function (e) { return e ? t : r; })(e); }\nfunction _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if (\"default\" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }\nasync function signCookie(type, value, maxAge, options) {\n  const {\n    cookies,\n    logger\n  } = options;\n  logger.debug(`CREATE_${type.toUpperCase()}`, {\n    value,\n    maxAge\n  });\n  const {\n    name\n  } = cookies[type];\n  const expires = new Date();\n  expires.setTime(expires.getTime() + maxAge * 1000);\n  return {\n    name,\n    value: await jwt.encode({\n      ...options.jwt,\n      maxAge,\n      token: {\n        value\n      },\n      salt: name\n    }),\n    options: {\n      ...cookies[type].options,\n      expires\n    }\n  };\n}\nconst PKCE_MAX_AGE = 60 * 15;\nconst PKCE_CODE_CHALLENGE_METHOD = exports.PKCE_CODE_CHALLENGE_METHOD = \"S256\";\nconst pkce = exports.pkce = {\n  async create(options, cookies, resParams) {\n    var _options$provider, _options$cookies$pkce;\n    if (!((_options$provider = options.provider) !== null && _options$provider !== void 0 && (_options$provider = _options$provider.checks) !== null && _options$provider !== void 0 && _options$provider.includes(\"pkce\"))) return;\n    const code_verifier = _openidClient.generators.codeVerifier();\n    const value = _openidClient.generators.codeChallenge(code_verifier);\n    resParams.code_challenge = value;\n    resParams.code_challenge_method = PKCE_CODE_CHALLENGE_METHOD;\n    const maxAge = (_options$cookies$pkce = options.cookies.pkceCodeVerifier.options.maxAge) !== null && _options$cookies$pkce !== void 0 ? _options$cookies$pkce : PKCE_MAX_AGE;\n    cookies.push(await signCookie(\"pkceCodeVerifier\", code_verifier, maxAge, options));\n  },\n  async use(cookies, resCookies, options, checks) {\n    var _options$provider2;\n    if (!((_options$provider2 = options.provider) !== null && _options$provider2 !== void 0 && (_options$provider2 = _options$provider2.checks) !== null && _options$provider2 !== void 0 && _options$provider2.includes(\"pkce\"))) return;\n    const codeVerifier = cookies === null || cookies === void 0 ? void 0 : cookies[options.cookies.pkceCodeVerifier.name];\n    if (!codeVerifier) throw new TypeError(\"PKCE code_verifier cookie was missing.\");\n    const {\n      name\n    } = options.cookies.pkceCodeVerifier;\n    const value = await jwt.decode({\n      ...options.jwt,\n      token: codeVerifier,\n      salt: name\n    });\n    if (!(value !== null && value !== void 0 && value.value)) throw new TypeError(\"PKCE code_verifier value could not be parsed.\");\n    resCookies.push({\n      name,\n      value: \"\",\n      options: {\n        ...options.cookies.pkceCodeVerifier.options,\n        maxAge: 0\n      }\n    });\n    checks.code_verifier = value.value;\n  }\n};\nconst STATE_MAX_AGE = 60 * 15;\nconst state = exports.state = {\n  async create(options, cookies, resParams) {\n    var _options$provider$che, _options$cookies$stat;\n    if (!((_options$provider$che = options.provider.checks) !== null && _options$provider$che !== void 0 && _options$provider$che.includes(\"state\"))) return;\n    const value = _openidClient.generators.state();\n    resParams.state = value;\n    const maxAge = (_options$cookies$stat = options.cookies.state.options.maxAge) !== null && _options$cookies$stat !== void 0 ? _options$cookies$stat : STATE_MAX_AGE;\n    cookies.push(await signCookie(\"state\", value, maxAge, options));\n  },\n  async use(cookies, resCookies, options, checks) {\n    var _options$provider$che2;\n    if (!((_options$provider$che2 = options.provider.checks) !== null && _options$provider$che2 !== void 0 && _options$provider$che2.includes(\"state\"))) return;\n    const state = cookies === null || cookies === void 0 ? void 0 : cookies[options.cookies.state.name];\n    if (!state) throw new TypeError(\"State cookie was missing.\");\n    const {\n      name\n    } = options.cookies.state;\n    const value = await jwt.decode({\n      ...options.jwt,\n      token: state,\n      salt: name\n    });\n    if (!(value !== null && value !== void 0 && value.value)) throw new TypeError(\"State value could not be parsed.\");\n    resCookies.push({\n      name,\n      value: \"\",\n      options: {\n        ...options.cookies.state.options,\n        maxAge: 0\n      }\n    });\n    checks.state = value.value;\n  }\n};\nconst NONCE_MAX_AGE = 60 * 15;\nconst nonce = exports.nonce = {\n  async create(options, cookies, resParams) {\n    var _options$provider$che3, _options$cookies$nonc;\n    if (!((_options$provider$che3 = options.provider.checks) !== null && _options$provider$che3 !== void 0 && _options$provider$che3.includes(\"nonce\"))) return;\n    const value = _openidClient.generators.nonce();\n    resParams.nonce = value;\n    const maxAge = (_options$cookies$nonc = options.cookies.nonce.options.maxAge) !== null && _options$cookies$nonc !== void 0 ? _options$cookies$nonc : NONCE_MAX_AGE;\n    cookies.push(await signCookie(\"nonce\", value, maxAge, options));\n  },\n  async use(cookies, resCookies, options, checks) {\n    var _options$provider3;\n    if (!((_options$provider3 = options.provider) !== null && _options$provider3 !== void 0 && (_options$provider3 = _options$provider3.checks) !== null && _options$provider3 !== void 0 && _options$provider3.includes(\"nonce\"))) return;\n    const nonce = cookies === null || cookies === void 0 ? void 0 : cookies[options.cookies.nonce.name];\n    if (!nonce) throw new TypeError(\"Nonce cookie was missing.\");\n    const {\n      name\n    } = options.cookies.nonce;\n    const value = await jwt.decode({\n      ...options.jwt,\n      token: nonce,\n      salt: name\n    });\n    if (!(value !== null && value !== void 0 && value.value)) throw new TypeError(\"Nonce value could not be parsed.\");\n    resCookies.push({\n      name,\n      value: \"\",\n      options: {\n        ...options.cookies.nonce.options,\n        maxAge: 0\n      }\n    });\n    checks.nonce = value.value;\n  }\n};"], "names": [], "mappings": "AAAA;AAEA,OAAO,cAAc,CAAC,SAAS,cAAc;IAC3C,OAAO;AACT;AACA,QAAQ,IAAI,GAAG,QAAQ,KAAK,GAAG,QAAQ,0BAA0B,GAAG,KAAK;AACzE,QAAQ,UAAU,GAAG;AACrB,QAAQ,KAAK,GAAG,KAAK;AACrB,IAAI;AACJ,IAAI,MAAM;AACV,SAAS,yBAAyB,CAAC;IAAI,IAAI,cAAc,OAAO,SAAS,OAAO;IAAM,IAAI,IAAI,IAAI,WAAW,IAAI,IAAI;IAAW,OAAO,CAAC,2BAA2B,SAAU,CAAC;QAAI,OAAO,IAAI,IAAI;IAAG,CAAC,EAAE;AAAI;AAC3M,SAAS,wBAAwB,CAAC,EAAE,CAAC;IAAI,IAAI,CAAC,KAAK,KAAK,EAAE,UAAU,EAAE,OAAO;IAAG,IAAI,SAAS,KAAK,YAAY,OAAO,KAAK,cAAc,OAAO,GAAG,OAAO;QAAE,SAAS;IAAE;IAAG,IAAI,IAAI,yBAAyB;IAAI,IAAI,KAAK,EAAE,GAAG,CAAC,IAAI,OAAO,EAAE,GAAG,CAAC;IAAI,IAAI,IAAI;QAAE,WAAW;IAAK,GAAG,IAAI,OAAO,cAAc,IAAI,OAAO,wBAAwB;IAAE,IAAK,IAAI,KAAK,EAAG,IAAI,cAAc,KAAK,CAAA,CAAC,CAAA,EAAE,cAAc,CAAC,IAAI,CAAC,GAAG,IAAI;QAAE,IAAI,IAAI,IAAI,OAAO,wBAAwB,CAAC,GAAG,KAAK;QAAM,KAAK,CAAC,EAAE,GAAG,IAAI,EAAE,GAAG,IAAI,OAAO,cAAc,CAAC,GAAG,GAAG,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;IAAE;IAAE,OAAO,EAAE,OAAO,GAAG,GAAG,KAAK,EAAE,GAAG,CAAC,GAAG,IAAI;AAAG;AAClkB,eAAe,WAAW,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO;IACpD,MAAM,EACJ,OAAO,EACP,MAAM,EACP,GAAG;IACJ,OAAO,KAAK,CAAC,CAAC,OAAO,EAAE,KAAK,WAAW,IAAI,EAAE;QAC3C;QACA;IACF;IACA,MAAM,EACJ,IAAI,EACL,GAAG,OAAO,CAAC,KAAK;IACjB,MAAM,UAAU,IAAI;IACpB,QAAQ,OAAO,CAAC,QAAQ,OAAO,KAAK,SAAS;IAC7C,OAAO;QACL;QACA,OAAO,MAAM,IAAI,MAAM,CAAC;YACtB,GAAG,QAAQ,GAAG;YACd;YACA,OAAO;gBACL;YACF;YACA,MAAM;QACR;QACA,SAAS;YACP,GAAG,OAAO,CAAC,KAAK,CAAC,OAAO;YACxB;QACF;IACF;AACF;AACA,MAAM,eAAe,KAAK;AAC1B,MAAM,6BAA6B,QAAQ,0BAA0B,GAAG;AACxE,MAAM,OAAO,QAAQ,IAAI,GAAG;IAC1B,MAAM,QAAO,OAAO,EAAE,OAAO,EAAE,SAAS;QACtC,IAAI,mBAAmB;QACvB,IAAI,CAAC,CAAC,CAAC,oBAAoB,QAAQ,QAAQ,MAAM,QAAQ,sBAAsB,KAAK,KAAK,CAAC,oBAAoB,kBAAkB,MAAM,MAAM,QAAQ,sBAAsB,KAAK,KAAK,kBAAkB,QAAQ,CAAC,OAAO,GAAG;QACzN,MAAM,gBAAgB,cAAc,UAAU,CAAC,YAAY;QAC3D,MAAM,QAAQ,cAAc,UAAU,CAAC,aAAa,CAAC;QACrD,UAAU,cAAc,GAAG;QAC3B,UAAU,qBAAqB,GAAG;QAClC,MAAM,SAAS,CAAC,wBAAwB,QAAQ,OAAO,CAAC,gBAAgB,CAAC,OAAO,CAAC,MAAM,MAAM,QAAQ,0BAA0B,KAAK,IAAI,wBAAwB;QAChK,QAAQ,IAAI,CAAC,MAAM,WAAW,oBAAoB,eAAe,QAAQ;IAC3E;IACA,MAAM,KAAI,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM;QAC5C,IAAI;QACJ,IAAI,CAAC,CAAC,CAAC,qBAAqB,QAAQ,QAAQ,MAAM,QAAQ,uBAAuB,KAAK,KAAK,CAAC,qBAAqB,mBAAmB,MAAM,MAAM,QAAQ,uBAAuB,KAAK,KAAK,mBAAmB,QAAQ,CAAC,OAAO,GAAG;QAC/N,MAAM,eAAe,YAAY,QAAQ,YAAY,KAAK,IAAI,KAAK,IAAI,OAAO,CAAC,QAAQ,OAAO,CAAC,gBAAgB,CAAC,IAAI,CAAC;QACrH,IAAI,CAAC,cAAc,MAAM,IAAI,UAAU;QACvC,MAAM,EACJ,IAAI,EACL,GAAG,QAAQ,OAAO,CAAC,gBAAgB;QACpC,MAAM,QAAQ,MAAM,IAAI,MAAM,CAAC;YAC7B,GAAG,QAAQ,GAAG;YACd,OAAO;YACP,MAAM;QACR;QACA,IAAI,CAAC,CAAC,UAAU,QAAQ,UAAU,KAAK,KAAK,MAAM,KAAK,GAAG,MAAM,IAAI,UAAU;QAC9E,WAAW,IAAI,CAAC;YACd;YACA,OAAO;YACP,SAAS;gBACP,GAAG,QAAQ,OAAO,CAAC,gBAAgB,CAAC,OAAO;gBAC3C,QAAQ;YACV;QACF;QACA,OAAO,aAAa,GAAG,MAAM,KAAK;IACpC;AACF;AACA,MAAM,gBAAgB,KAAK;AAC3B,MAAM,QAAQ,QAAQ,KAAK,GAAG;IAC5B,MAAM,QAAO,OAAO,EAAE,OAAO,EAAE,SAAS;QACtC,IAAI,uBAAuB;QAC3B,IAAI,CAAC,CAAC,CAAC,wBAAwB,QAAQ,QAAQ,CAAC,MAAM,MAAM,QAAQ,0BAA0B,KAAK,KAAK,sBAAsB,QAAQ,CAAC,QAAQ,GAAG;QAClJ,MAAM,QAAQ,cAAc,UAAU,CAAC,KAAK;QAC5C,UAAU,KAAK,GAAG;QAClB,MAAM,SAAS,CAAC,wBAAwB,QAAQ,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,MAAM,QAAQ,0BAA0B,KAAK,IAAI,wBAAwB;QACrJ,QAAQ,IAAI,CAAC,MAAM,WAAW,SAAS,OAAO,QAAQ;IACxD;IACA,MAAM,KAAI,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM;QAC5C,IAAI;QACJ,IAAI,CAAC,CAAC,CAAC,yBAAyB,QAAQ,QAAQ,CAAC,MAAM,MAAM,QAAQ,2BAA2B,KAAK,KAAK,uBAAuB,QAAQ,CAAC,QAAQ,GAAG;QACrJ,MAAM,QAAQ,YAAY,QAAQ,YAAY,KAAK,IAAI,KAAK,IAAI,OAAO,CAAC,QAAQ,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC;QACnG,IAAI,CAAC,OAAO,MAAM,IAAI,UAAU;QAChC,MAAM,EACJ,IAAI,EACL,GAAG,QAAQ,OAAO,CAAC,KAAK;QACzB,MAAM,QAAQ,MAAM,IAAI,MAAM,CAAC;YAC7B,GAAG,QAAQ,GAAG;YACd,OAAO;YACP,MAAM;QACR;QACA,IAAI,CAAC,CAAC,UAAU,QAAQ,UAAU,KAAK,KAAK,MAAM,KAAK,GAAG,MAAM,IAAI,UAAU;QAC9E,WAAW,IAAI,CAAC;YACd;YACA,OAAO;YACP,SAAS;gBACP,GAAG,QAAQ,OAAO,CAAC,KAAK,CAAC,OAAO;gBAChC,QAAQ;YACV;QACF;QACA,OAAO,KAAK,GAAG,MAAM,KAAK;IAC5B;AACF;AACA,MAAM,gBAAgB,KAAK;AAC3B,MAAM,QAAQ,QAAQ,KAAK,GAAG;IAC5B,MAAM,QAAO,OAAO,EAAE,OAAO,EAAE,SAAS;QACtC,IAAI,wBAAwB;QAC5B,IAAI,CAAC,CAAC,CAAC,yBAAyB,QAAQ,QAAQ,CAAC,MAAM,MAAM,QAAQ,2BAA2B,KAAK,KAAK,uBAAuB,QAAQ,CAAC,QAAQ,GAAG;QACrJ,MAAM,QAAQ,cAAc,UAAU,CAAC,KAAK;QAC5C,UAAU,KAAK,GAAG;QAClB,MAAM,SAAS,CAAC,wBAAwB,QAAQ,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,MAAM,QAAQ,0BAA0B,KAAK,IAAI,wBAAwB;QACrJ,QAAQ,IAAI,CAAC,MAAM,WAAW,SAAS,OAAO,QAAQ;IACxD;IACA,MAAM,KAAI,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM;QAC5C,IAAI;QACJ,IAAI,CAAC,CAAC,CAAC,qBAAqB,QAAQ,QAAQ,MAAM,QAAQ,uBAAuB,KAAK,KAAK,CAAC,qBAAqB,mBAAmB,MAAM,MAAM,QAAQ,uBAAuB,KAAK,KAAK,mBAAmB,QAAQ,CAAC,QAAQ,GAAG;QAChO,MAAM,QAAQ,YAAY,QAAQ,YAAY,KAAK,IAAI,KAAK,IAAI,OAAO,CAAC,QAAQ,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC;QACnG,IAAI,CAAC,OAAO,MAAM,IAAI,UAAU;QAChC,MAAM,EACJ,IAAI,EACL,GAAG,QAAQ,OAAO,CAAC,KAAK;QACzB,MAAM,QAAQ,MAAM,IAAI,MAAM,CAAC;YAC7B,GAAG,QAAQ,GAAG;YACd,OAAO;YACP,MAAM;QACR;QACA,IAAI,CAAC,CAAC,UAAU,QAAQ,UAAU,KAAK,KAAK,MAAM,KAAK,GAAG,MAAM,IAAI,UAAU;QAC9E,WAAW,IAAI,CAAC;YACd;YACA,OAAO;YACP,SAAS;gBACP,GAAG,QAAQ,OAAO,CAAC,KAAK,CAAC,OAAO;gBAChC,QAAQ;YACV;QACF;QACA,OAAO,KAAK,GAAG,MAAM,KAAK;IAC5B;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 949, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/node_modules/next-auth/core/lib/oauth/callback.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = oAuthCallback;\nvar _openidClient = require(\"openid-client\");\nvar _client = require(\"./client\");\nvar _clientLegacy = require(\"./client-legacy\");\nvar _checks = _interopRequireWildcard(require(\"./checks\"));\nvar _errors = require(\"../../errors\");\nfunction _getRequireWildcardCache(e) { if (\"function\" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function (e) { return e ? t : r; })(e); }\nfunction _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if (\"default\" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }\nasync function oAuthCallback(params) {\n  var _body$error, _provider$version;\n  const {\n    options,\n    query,\n    body,\n    method,\n    cookies\n  } = params;\n  const {\n    logger,\n    provider\n  } = options;\n  const errorMessage = (_body$error = body === null || body === void 0 ? void 0 : body.error) !== null && _body$error !== void 0 ? _body$error : query === null || query === void 0 ? void 0 : query.error;\n  if (errorMessage) {\n    const error = new Error(errorMessage);\n    logger.error(\"OAUTH_CALLBACK_HANDLER_ERROR\", {\n      error,\n      error_description: query === null || query === void 0 ? void 0 : query.error_description,\n      providerId: provider.id\n    });\n    logger.debug(\"OAUTH_CALLBACK_HANDLER_ERROR\", {\n      body\n    });\n    throw error;\n  }\n  if ((_provider$version = provider.version) !== null && _provider$version !== void 0 && _provider$version.startsWith(\"1.\")) {\n    try {\n      const client = await (0, _clientLegacy.oAuth1Client)(options);\n      const {\n        oauth_token,\n        oauth_verifier\n      } = query !== null && query !== void 0 ? query : {};\n      const tokens = await client.getOAuthAccessToken(oauth_token, _clientLegacy.oAuth1TokenStore.get(oauth_token), oauth_verifier);\n      let profile = await client.get(provider.profileUrl, tokens.oauth_token, tokens.oauth_token_secret);\n      if (typeof profile === \"string\") {\n        profile = JSON.parse(profile);\n      }\n      const newProfile = await getProfile({\n        profile,\n        tokens,\n        provider,\n        logger\n      });\n      return {\n        ...newProfile,\n        cookies: []\n      };\n    } catch (error) {\n      logger.error(\"OAUTH_V1_GET_ACCESS_TOKEN_ERROR\", error);\n      throw error;\n    }\n  }\n  if (query !== null && query !== void 0 && query.oauth_token) _clientLegacy.oAuth1TokenStore.delete(query.oauth_token);\n  try {\n    var _provider$token, _provider$token2, _provider$userinfo;\n    const client = await (0, _client.openidClient)(options);\n    let tokens;\n    const checks = {};\n    const resCookies = [];\n    await _checks.state.use(cookies, resCookies, options, checks);\n    await _checks.pkce.use(cookies, resCookies, options, checks);\n    await _checks.nonce.use(cookies, resCookies, options, checks);\n    const params = {\n      ...client.callbackParams({\n        url: `http://n?${new URLSearchParams(query)}`,\n        body,\n        method\n      }),\n      ...((_provider$token = provider.token) === null || _provider$token === void 0 ? void 0 : _provider$token.params)\n    };\n    if ((_provider$token2 = provider.token) !== null && _provider$token2 !== void 0 && _provider$token2.request) {\n      const response = await provider.token.request({\n        provider,\n        params,\n        checks,\n        client\n      });\n      tokens = new _openidClient.TokenSet(response.tokens);\n    } else if (provider.idToken) {\n      tokens = await client.callback(provider.callbackUrl, params, checks);\n    } else {\n      tokens = await client.oauthCallback(provider.callbackUrl, params, checks);\n    }\n    if (Array.isArray(tokens.scope)) {\n      tokens.scope = tokens.scope.join(\" \");\n    }\n    let profile;\n    if ((_provider$userinfo = provider.userinfo) !== null && _provider$userinfo !== void 0 && _provider$userinfo.request) {\n      profile = await provider.userinfo.request({\n        provider,\n        tokens,\n        client\n      });\n    } else if (provider.idToken) {\n      profile = tokens.claims();\n    } else {\n      var _provider$userinfo2;\n      profile = await client.userinfo(tokens, {\n        params: (_provider$userinfo2 = provider.userinfo) === null || _provider$userinfo2 === void 0 ? void 0 : _provider$userinfo2.params\n      });\n    }\n    const profileResult = await getProfile({\n      profile,\n      provider,\n      tokens,\n      logger\n    });\n    return {\n      ...profileResult,\n      cookies: resCookies\n    };\n  } catch (error) {\n    throw new _errors.OAuthCallbackError(error);\n  }\n}\nasync function getProfile({\n  profile: OAuthProfile,\n  tokens,\n  provider,\n  logger\n}) {\n  try {\n    var _profile$email;\n    logger.debug(\"PROFILE_DATA\", {\n      OAuthProfile\n    });\n    const profile = await provider.profile(OAuthProfile, tokens);\n    profile.email = (_profile$email = profile.email) === null || _profile$email === void 0 ? void 0 : _profile$email.toLowerCase();\n    if (!profile.id) throw new TypeError(`Profile id is missing in ${provider.name} OAuth profile response`);\n    return {\n      profile,\n      account: {\n        provider: provider.id,\n        type: provider.type,\n        providerAccountId: profile.id.toString(),\n        ...tokens\n      },\n      OAuthProfile\n    };\n  } catch (error) {\n    logger.error(\"OAUTH_PARSE_PROFILE_ERROR\", {\n      error: error,\n      OAuthProfile\n    });\n  }\n}"], "names": [], "mappings": "AAAA;AAEA,OAAO,cAAc,CAAC,SAAS,cAAc;IAC3C,OAAO;AACT;AACA,QAAQ,OAAO,GAAG;AAClB,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI,UAAU;AACd,IAAI;AACJ,SAAS,yBAAyB,CAAC;IAAI,IAAI,cAAc,OAAO,SAAS,OAAO;IAAM,IAAI,IAAI,IAAI,WAAW,IAAI,IAAI;IAAW,OAAO,CAAC,2BAA2B,SAAU,CAAC;QAAI,OAAO,IAAI,IAAI;IAAG,CAAC,EAAE;AAAI;AAC3M,SAAS,wBAAwB,CAAC,EAAE,CAAC;IAAI,IAAI,CAAC,KAAK,KAAK,EAAE,UAAU,EAAE,OAAO;IAAG,IAAI,SAAS,KAAK,YAAY,OAAO,KAAK,cAAc,OAAO,GAAG,OAAO;QAAE,SAAS;IAAE;IAAG,IAAI,IAAI,yBAAyB;IAAI,IAAI,KAAK,EAAE,GAAG,CAAC,IAAI,OAAO,EAAE,GAAG,CAAC;IAAI,IAAI,IAAI;QAAE,WAAW;IAAK,GAAG,IAAI,OAAO,cAAc,IAAI,OAAO,wBAAwB;IAAE,IAAK,IAAI,KAAK,EAAG,IAAI,cAAc,KAAK,CAAA,CAAC,CAAA,EAAE,cAAc,CAAC,IAAI,CAAC,GAAG,IAAI;QAAE,IAAI,IAAI,IAAI,OAAO,wBAAwB,CAAC,GAAG,KAAK;QAAM,KAAK,CAAC,EAAE,GAAG,IAAI,EAAE,GAAG,IAAI,OAAO,cAAc,CAAC,GAAG,GAAG,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;IAAE;IAAE,OAAO,EAAE,OAAO,GAAG,GAAG,KAAK,EAAE,GAAG,CAAC,GAAG,IAAI;AAAG;AAClkB,eAAe,cAAc,MAAM;IACjC,IAAI,aAAa;IACjB,MAAM,EACJ,OAAO,EACP,KAAK,EACL,IAAI,EACJ,MAAM,EACN,OAAO,EACR,GAAG;IACJ,MAAM,EACJ,MAAM,EACN,QAAQ,EACT,GAAG;IACJ,MAAM,eAAe,CAAC,cAAc,SAAS,QAAQ,SAAS,KAAK,IAAI,KAAK,IAAI,KAAK,KAAK,MAAM,QAAQ,gBAAgB,KAAK,IAAI,cAAc,UAAU,QAAQ,UAAU,KAAK,IAAI,KAAK,IAAI,MAAM,KAAK;IACxM,IAAI,cAAc;QAChB,MAAM,QAAQ,IAAI,MAAM;QACxB,OAAO,KAAK,CAAC,gCAAgC;YAC3C;YACA,mBAAmB,UAAU,QAAQ,UAAU,KAAK,IAAI,KAAK,IAAI,MAAM,iBAAiB;YACxF,YAAY,SAAS,EAAE;QACzB;QACA,OAAO,KAAK,CAAC,gCAAgC;YAC3C;QACF;QACA,MAAM;IACR;IACA,IAAI,CAAC,oBAAoB,SAAS,OAAO,MAAM,QAAQ,sBAAsB,KAAK,KAAK,kBAAkB,UAAU,CAAC,OAAO;QACzH,IAAI;YACF,MAAM,SAAS,MAAM,CAAC,GAAG,cAAc,YAAY,EAAE;YACrD,MAAM,EACJ,WAAW,EACX,cAAc,EACf,GAAG,UAAU,QAAQ,UAAU,KAAK,IAAI,QAAQ,CAAC;YAClD,MAAM,SAAS,MAAM,OAAO,mBAAmB,CAAC,aAAa,cAAc,gBAAgB,CAAC,GAAG,CAAC,cAAc;YAC9G,IAAI,UAAU,MAAM,OAAO,GAAG,CAAC,SAAS,UAAU,EAAE,OAAO,WAAW,EAAE,OAAO,kBAAkB;YACjG,IAAI,OAAO,YAAY,UAAU;gBAC/B,UAAU,KAAK,KAAK,CAAC;YACvB;YACA,MAAM,aAAa,MAAM,WAAW;gBAClC;gBACA;gBACA;gBACA;YACF;YACA,OAAO;gBACL,GAAG,UAAU;gBACb,SAAS,EAAE;YACb;QACF,EAAE,OAAO,OAAO;YACd,OAAO,KAAK,CAAC,mCAAmC;YAChD,MAAM;QACR;IACF;IACA,IAAI,UAAU,QAAQ,UAAU,KAAK,KAAK,MAAM,WAAW,EAAE,cAAc,gBAAgB,CAAC,MAAM,CAAC,MAAM,WAAW;IACpH,IAAI;QACF,IAAI,iBAAiB,kBAAkB;QACvC,MAAM,SAAS,MAAM,CAAC,GAAG,QAAQ,YAAY,EAAE;QAC/C,IAAI;QACJ,MAAM,SAAS,CAAC;QAChB,MAAM,aAAa,EAAE;QACrB,MAAM,QAAQ,KAAK,CAAC,GAAG,CAAC,SAAS,YAAY,SAAS;QACtD,MAAM,QAAQ,IAAI,CAAC,GAAG,CAAC,SAAS,YAAY,SAAS;QACrD,MAAM,QAAQ,KAAK,CAAC,GAAG,CAAC,SAAS,YAAY,SAAS;QACtD,MAAM,SAAS;YACb,GAAG,OAAO,cAAc,CAAC;gBACvB,KAAK,CAAC,SAAS,EAAE,IAAI,gBAAgB,QAAQ;gBAC7C;gBACA;YACF,EAAE;YACF,GAAI,CAAC,kBAAkB,SAAS,KAAK,MAAM,QAAQ,oBAAoB,KAAK,IAAI,KAAK,IAAI,gBAAgB,MAAM;QACjH;QACA,IAAI,CAAC,mBAAmB,SAAS,KAAK,MAAM,QAAQ,qBAAqB,KAAK,KAAK,iBAAiB,OAAO,EAAE;YAC3G,MAAM,WAAW,MAAM,SAAS,KAAK,CAAC,OAAO,CAAC;gBAC5C;gBACA;gBACA;gBACA;YACF;YACA,SAAS,IAAI,cAAc,QAAQ,CAAC,SAAS,MAAM;QACrD,OAAO,IAAI,SAAS,OAAO,EAAE;YAC3B,SAAS,MAAM,OAAO,QAAQ,CAAC,SAAS,WAAW,EAAE,QAAQ;QAC/D,OAAO;YACL,SAAS,MAAM,OAAO,aAAa,CAAC,SAAS,WAAW,EAAE,QAAQ;QACpE;QACA,IAAI,MAAM,OAAO,CAAC,OAAO,KAAK,GAAG;YAC/B,OAAO,KAAK,GAAG,OAAO,KAAK,CAAC,IAAI,CAAC;QACnC;QACA,IAAI;QACJ,IAAI,CAAC,qBAAqB,SAAS,QAAQ,MAAM,QAAQ,uBAAuB,KAAK,KAAK,mBAAmB,OAAO,EAAE;YACpH,UAAU,MAAM,SAAS,QAAQ,CAAC,OAAO,CAAC;gBACxC;gBACA;gBACA;YACF;QACF,OAAO,IAAI,SAAS,OAAO,EAAE;YAC3B,UAAU,OAAO,MAAM;QACzB,OAAO;YACL,IAAI;YACJ,UAAU,MAAM,OAAO,QAAQ,CAAC,QAAQ;gBACtC,QAAQ,CAAC,sBAAsB,SAAS,QAAQ,MAAM,QAAQ,wBAAwB,KAAK,IAAI,KAAK,IAAI,oBAAoB,MAAM;YACpI;QACF;QACA,MAAM,gBAAgB,MAAM,WAAW;YACrC;YACA;YACA;YACA;QACF;QACA,OAAO;YACL,GAAG,aAAa;YAChB,SAAS;QACX;IACF,EAAE,OAAO,OAAO;QACd,MAAM,IAAI,QAAQ,kBAAkB,CAAC;IACvC;AACF;AACA,eAAe,WAAW,EACxB,SAAS,YAAY,EACrB,MAAM,EACN,QAAQ,EACR,MAAM,EACP;IACC,IAAI;QACF,IAAI;QACJ,OAAO,KAAK,CAAC,gBAAgB;YAC3B;QACF;QACA,MAAM,UAAU,MAAM,SAAS,OAAO,CAAC,cAAc;QACrD,QAAQ,KAAK,GAAG,CAAC,iBAAiB,QAAQ,KAAK,MAAM,QAAQ,mBAAmB,KAAK,IAAI,KAAK,IAAI,eAAe,WAAW;QAC5H,IAAI,CAAC,QAAQ,EAAE,EAAE,MAAM,IAAI,UAAU,CAAC,yBAAyB,EAAE,SAAS,IAAI,CAAC,uBAAuB,CAAC;QACvG,OAAO;YACL;YACA,SAAS;gBACP,UAAU,SAAS,EAAE;gBACrB,MAAM,SAAS,IAAI;gBACnB,mBAAmB,QAAQ,EAAE,CAAC,QAAQ;gBACtC,GAAG,MAAM;YACX;YACA;QACF;IACF,EAAE,OAAO,OAAO;QACd,OAAO,KAAK,CAAC,6BAA6B;YACxC,OAAO;YACP;QACF;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1117, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/node_modules/next-auth/core/lib/utils.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.createSecret = createSecret;\nexports.fromDate = fromDate;\nexports.hashToken = hashToken;\nvar _crypto = require(\"crypto\");\nfunction fromDate(time, date = Date.now()) {\n  return new Date(date + time * 1000);\n}\nfunction hashToken(token, options) {\n  var _provider$secret;\n  const {\n    provider,\n    secret\n  } = options;\n  return (0, _crypto.createHash)(\"sha256\").update(`${token}${(_provider$secret = provider.secret) !== null && _provider$secret !== void 0 ? _provider$secret : secret}`).digest(\"hex\");\n}\nfunction createSecret(params) {\n  var _authOptions$secret;\n  const {\n    authOptions,\n    url\n  } = params;\n  return (_authOptions$secret = authOptions.secret) !== null && _authOptions$secret !== void 0 ? _authOptions$secret : (0, _crypto.createHash)(\"sha256\").update(JSON.stringify({\n    ...url,\n    ...authOptions\n  })).digest(\"hex\");\n}"], "names": [], "mappings": "AAAA;AAEA,OAAO,cAAc,CAAC,SAAS,cAAc;IAC3C,OAAO;AACT;AACA,QAAQ,YAAY,GAAG;AACvB,QAAQ,QAAQ,GAAG;AACnB,QAAQ,SAAS,GAAG;AACpB,IAAI;AACJ,SAAS,SAAS,IAAI,EAAE,OAAO,KAAK,GAAG,EAAE;IACvC,OAAO,IAAI,KAAK,OAAO,OAAO;AAChC;AACA,SAAS,UAAU,KAAK,EAAE,OAAO;IAC/B,IAAI;IACJ,MAAM,EACJ,QAAQ,EACR,MAAM,EACP,GAAG;IACJ,OAAO,CAAC,GAAG,QAAQ,UAAU,EAAE,UAAU,MAAM,CAAC,GAAG,QAAQ,CAAC,mBAAmB,SAAS,MAAM,MAAM,QAAQ,qBAAqB,KAAK,IAAI,mBAAmB,QAAQ,EAAE,MAAM,CAAC;AAChL;AACA,SAAS,aAAa,MAAM;IAC1B,IAAI;IACJ,MAAM,EACJ,WAAW,EACX,GAAG,EACJ,GAAG;IACJ,OAAO,CAAC,sBAAsB,YAAY,MAAM,MAAM,QAAQ,wBAAwB,KAAK,IAAI,sBAAsB,CAAC,GAAG,QAAQ,UAAU,EAAE,UAAU,MAAM,CAAC,KAAK,SAAS,CAAC;QAC3K,GAAG,GAAG;QACN,GAAG,WAAW;IAChB,IAAI,MAAM,CAAC;AACb", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1146, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/node_modules/next-auth/core/lib/callback-handler.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = callbackHandler;\nvar _errors = require(\"../errors\");\nvar _utils = require(\"./utils\");\nasync function callbackHandler(params) {\n  const {\n    sessionToken,\n    profile: _profile,\n    account,\n    options\n  } = params;\n  if (!(account !== null && account !== void 0 && account.providerAccountId) || !account.type) throw new Error(\"Missing or invalid provider account\");\n  if (![\"email\", \"oauth\"].includes(account.type)) throw new Error(\"Provider not supported\");\n  const {\n    adapter,\n    jwt,\n    events,\n    session: {\n      strategy: sessionStrategy,\n      generateSessionToken\n    }\n  } = options;\n  if (!adapter) {\n    return {\n      user: _profile,\n      account\n    };\n  }\n  const profile = _profile;\n  const {\n    createUser,\n    updateUser,\n    getUser,\n    getUserByAccount,\n    getUserByEmail,\n    linkAccount,\n    createSession,\n    getSessionAndUser,\n    deleteSession\n  } = adapter;\n  let session = null;\n  let user = null;\n  let isNewUser = false;\n  const useJwtSession = sessionStrategy === \"jwt\";\n  if (sessionToken) {\n    if (useJwtSession) {\n      try {\n        session = await jwt.decode({\n          ...jwt,\n          token: sessionToken\n        });\n        if (session && \"sub\" in session && session.sub) {\n          user = await getUser(session.sub);\n        }\n      } catch (_unused) {}\n    } else {\n      const userAndSession = await getSessionAndUser(sessionToken);\n      if (userAndSession) {\n        session = userAndSession.session;\n        user = userAndSession.user;\n      }\n    }\n  }\n  if (account.type === \"email\") {\n    const userByEmail = await getUserByEmail(profile.email);\n    if (userByEmail) {\n      var _user, _events$updateUser;\n      if (((_user = user) === null || _user === void 0 ? void 0 : _user.id) !== userByEmail.id && !useJwtSession && sessionToken) {\n        await deleteSession(sessionToken);\n      }\n      user = await updateUser({\n        id: userByEmail.id,\n        emailVerified: new Date()\n      });\n      await ((_events$updateUser = events.updateUser) === null || _events$updateUser === void 0 ? void 0 : _events$updateUser.call(events, {\n        user\n      }));\n    } else {\n      var _events$createUser;\n      const {\n        id: _,\n        ...newUser\n      } = {\n        ...profile,\n        emailVerified: new Date()\n      };\n      user = await createUser(newUser);\n      await ((_events$createUser = events.createUser) === null || _events$createUser === void 0 ? void 0 : _events$createUser.call(events, {\n        user\n      }));\n      isNewUser = true;\n    }\n    session = useJwtSession ? {} : await createSession({\n      sessionToken: await generateSessionToken(),\n      userId: user.id,\n      expires: (0, _utils.fromDate)(options.session.maxAge)\n    });\n    return {\n      session,\n      user,\n      isNewUser\n    };\n  } else if (account.type === \"oauth\") {\n    const userByAccount = await getUserByAccount({\n      providerAccountId: account.providerAccountId,\n      provider: account.provider\n    });\n    if (userByAccount) {\n      if (user) {\n        if (userByAccount.id === user.id) {\n          return {\n            session,\n            user,\n            isNewUser\n          };\n        }\n        throw new _errors.AccountNotLinkedError(\"The account is already associated with another user\");\n      }\n      session = useJwtSession ? {} : await createSession({\n        sessionToken: await generateSessionToken(),\n        userId: userByAccount.id,\n        expires: (0, _utils.fromDate)(options.session.maxAge)\n      });\n      return {\n        session,\n        user: userByAccount,\n        isNewUser\n      };\n    } else {\n      var _events$createUser2, _events$linkAccount2;\n      if (user) {\n        var _events$linkAccount;\n        await linkAccount({\n          ...account,\n          userId: user.id\n        });\n        await ((_events$linkAccount = events.linkAccount) === null || _events$linkAccount === void 0 ? void 0 : _events$linkAccount.call(events, {\n          user,\n          account,\n          profile\n        }));\n        return {\n          session,\n          user,\n          isNewUser\n        };\n      }\n      const userByEmail = profile.email ? await getUserByEmail(profile.email) : null;\n      if (userByEmail) {\n        const provider = options.provider;\n        if (provider !== null && provider !== void 0 && provider.allowDangerousEmailAccountLinking) {\n          user = userByEmail;\n        } else {\n          throw new _errors.AccountNotLinkedError(\"Another account already exists with the same e-mail address\");\n        }\n      } else {\n        const {\n          id: _,\n          ...newUser\n        } = {\n          ...profile,\n          emailVerified: null\n        };\n        user = await createUser(newUser);\n      }\n      await ((_events$createUser2 = events.createUser) === null || _events$createUser2 === void 0 ? void 0 : _events$createUser2.call(events, {\n        user\n      }));\n      await linkAccount({\n        ...account,\n        userId: user.id\n      });\n      await ((_events$linkAccount2 = events.linkAccount) === null || _events$linkAccount2 === void 0 ? void 0 : _events$linkAccount2.call(events, {\n        user,\n        account,\n        profile\n      }));\n      session = useJwtSession ? {} : await createSession({\n        sessionToken: await generateSessionToken(),\n        userId: user.id,\n        expires: (0, _utils.fromDate)(options.session.maxAge)\n      });\n      return {\n        session,\n        user,\n        isNewUser: true\n      };\n    }\n  }\n  throw new Error(\"Unsupported account type\");\n}"], "names": [], "mappings": "AAAA;AAEA,OAAO,cAAc,CAAC,SAAS,cAAc;IAC3C,OAAO;AACT;AACA,QAAQ,OAAO,GAAG;AAClB,IAAI;AACJ,IAAI;AACJ,eAAe,gBAAgB,MAAM;IACnC,MAAM,EACJ,YAAY,EACZ,SAAS,QAAQ,EACjB,OAAO,EACP,OAAO,EACR,GAAG;IACJ,IAAI,CAAC,CAAC,YAAY,QAAQ,YAAY,KAAK,KAAK,QAAQ,iBAAiB,KAAK,CAAC,QAAQ,IAAI,EAAE,MAAM,IAAI,MAAM;IAC7G,IAAI,CAAC;QAAC;QAAS;KAAQ,CAAC,QAAQ,CAAC,QAAQ,IAAI,GAAG,MAAM,IAAI,MAAM;IAChE,MAAM,EACJ,OAAO,EACP,GAAG,EACH,MAAM,EACN,SAAS,EACP,UAAU,eAAe,EACzB,oBAAoB,EACrB,EACF,GAAG;IACJ,IAAI,CAAC,SAAS;QACZ,OAAO;YACL,MAAM;YACN;QACF;IACF;IACA,MAAM,UAAU;IAChB,MAAM,EACJ,UAAU,EACV,UAAU,EACV,OAAO,EACP,gBAAgB,EAChB,cAAc,EACd,WAAW,EACX,aAAa,EACb,iBAAiB,EACjB,aAAa,EACd,GAAG;IACJ,IAAI,UAAU;IACd,IAAI,OAAO;IACX,IAAI,YAAY;IAChB,MAAM,gBAAgB,oBAAoB;IAC1C,IAAI,cAAc;QAChB,IAAI,eAAe;YACjB,IAAI;gBACF,UAAU,MAAM,IAAI,MAAM,CAAC;oBACzB,GAAG,GAAG;oBACN,OAAO;gBACT;gBACA,IAAI,WAAW,SAAS,WAAW,QAAQ,GAAG,EAAE;oBAC9C,OAAO,MAAM,QAAQ,QAAQ,GAAG;gBAClC;YACF,EAAE,OAAO,SAAS,CAAC;QACrB,OAAO;YACL,MAAM,iBAAiB,MAAM,kBAAkB;YAC/C,IAAI,gBAAgB;gBAClB,UAAU,eAAe,OAAO;gBAChC,OAAO,eAAe,IAAI;YAC5B;QACF;IACF;IACA,IAAI,QAAQ,IAAI,KAAK,SAAS;QAC5B,MAAM,cAAc,MAAM,eAAe,QAAQ,KAAK;QACtD,IAAI,aAAa;YACf,IAAI,OAAO;YACX,IAAI,CAAC,CAAC,QAAQ,IAAI,MAAM,QAAQ,UAAU,KAAK,IAAI,KAAK,IAAI,MAAM,EAAE,MAAM,YAAY,EAAE,IAAI,CAAC,iBAAiB,cAAc;gBAC1H,MAAM,cAAc;YACtB;YACA,OAAO,MAAM,WAAW;gBACtB,IAAI,YAAY,EAAE;gBAClB,eAAe,IAAI;YACrB;YACA,MAAM,CAAC,CAAC,qBAAqB,OAAO,UAAU,MAAM,QAAQ,uBAAuB,KAAK,IAAI,KAAK,IAAI,mBAAmB,IAAI,CAAC,QAAQ;gBACnI;YACF,EAAE;QACJ,OAAO;YACL,IAAI;YACJ,MAAM,EACJ,IAAI,CAAC,EACL,GAAG,SACJ,GAAG;gBACF,GAAG,OAAO;gBACV,eAAe,IAAI;YACrB;YACA,OAAO,MAAM,WAAW;YACxB,MAAM,CAAC,CAAC,qBAAqB,OAAO,UAAU,MAAM,QAAQ,uBAAuB,KAAK,IAAI,KAAK,IAAI,mBAAmB,IAAI,CAAC,QAAQ;gBACnI;YACF,EAAE;YACF,YAAY;QACd;QACA,UAAU,gBAAgB,CAAC,IAAI,MAAM,cAAc;YACjD,cAAc,MAAM;YACpB,QAAQ,KAAK,EAAE;YACf,SAAS,CAAC,GAAG,OAAO,QAAQ,EAAE,QAAQ,OAAO,CAAC,MAAM;QACtD;QACA,OAAO;YACL;YACA;YACA;QACF;IACF,OAAO,IAAI,QAAQ,IAAI,KAAK,SAAS;QACnC,MAAM,gBAAgB,MAAM,iBAAiB;YAC3C,mBAAmB,QAAQ,iBAAiB;YAC5C,UAAU,QAAQ,QAAQ;QAC5B;QACA,IAAI,eAAe;YACjB,IAAI,MAAM;gBACR,IAAI,cAAc,EAAE,KAAK,KAAK,EAAE,EAAE;oBAChC,OAAO;wBACL;wBACA;wBACA;oBACF;gBACF;gBACA,MAAM,IAAI,QAAQ,qBAAqB,CAAC;YAC1C;YACA,UAAU,gBAAgB,CAAC,IAAI,MAAM,cAAc;gBACjD,cAAc,MAAM;gBACpB,QAAQ,cAAc,EAAE;gBACxB,SAAS,CAAC,GAAG,OAAO,QAAQ,EAAE,QAAQ,OAAO,CAAC,MAAM;YACtD;YACA,OAAO;gBACL;gBACA,MAAM;gBACN;YACF;QACF,OAAO;YACL,IAAI,qBAAqB;YACzB,IAAI,MAAM;gBACR,IAAI;gBACJ,MAAM,YAAY;oBAChB,GAAG,OAAO;oBACV,QAAQ,KAAK,EAAE;gBACjB;gBACA,MAAM,CAAC,CAAC,sBAAsB,OAAO,WAAW,MAAM,QAAQ,wBAAwB,KAAK,IAAI,KAAK,IAAI,oBAAoB,IAAI,CAAC,QAAQ;oBACvI;oBACA;oBACA;gBACF,EAAE;gBACF,OAAO;oBACL;oBACA;oBACA;gBACF;YACF;YACA,MAAM,cAAc,QAAQ,KAAK,GAAG,MAAM,eAAe,QAAQ,KAAK,IAAI;YAC1E,IAAI,aAAa;gBACf,MAAM,WAAW,QAAQ,QAAQ;gBACjC,IAAI,aAAa,QAAQ,aAAa,KAAK,KAAK,SAAS,iCAAiC,EAAE;oBAC1F,OAAO;gBACT,OAAO;oBACL,MAAM,IAAI,QAAQ,qBAAqB,CAAC;gBAC1C;YACF,OAAO;gBACL,MAAM,EACJ,IAAI,CAAC,EACL,GAAG,SACJ,GAAG;oBACF,GAAG,OAAO;oBACV,eAAe;gBACjB;gBACA,OAAO,MAAM,WAAW;YAC1B;YACA,MAAM,CAAC,CAAC,sBAAsB,OAAO,UAAU,MAAM,QAAQ,wBAAwB,KAAK,IAAI,KAAK,IAAI,oBAAoB,IAAI,CAAC,QAAQ;gBACtI;YACF,EAAE;YACF,MAAM,YAAY;gBAChB,GAAG,OAAO;gBACV,QAAQ,KAAK,EAAE;YACjB;YACA,MAAM,CAAC,CAAC,uBAAuB,OAAO,WAAW,MAAM,QAAQ,yBAAyB,KAAK,IAAI,KAAK,IAAI,qBAAqB,IAAI,CAAC,QAAQ;gBAC1I;gBACA;gBACA;YACF,EAAE;YACF,UAAU,gBAAgB,CAAC,IAAI,MAAM,cAAc;gBACjD,cAAc,MAAM;gBACpB,QAAQ,KAAK,EAAE;gBACf,SAAS,CAAC,GAAG,OAAO,QAAQ,EAAE,QAAQ,OAAO,CAAC,MAAM;YACtD;YACA,OAAO;gBACL;gBACA;gBACA,WAAW;YACb;QACF;IACF;IACA,MAAM,IAAI,MAAM;AAClB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1319, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/node_modules/next-auth/core/lib/email/getUserFromEmail.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = getAdapterUserFromEmail;\nasync function getAdapterUserFromEmail({\n  email,\n  adapter\n}) {\n  const {\n    getUserByEmail\n  } = adapter;\n  const adapterUser = email ? await getUserByEmail(email) : null;\n  if (adapterUser) return adapterUser;\n  return {\n    id: email,\n    email,\n    emailVerified: null\n  };\n}"], "names": [], "mappings": "AAAA;AAEA,OAAO,cAAc,CAAC,SAAS,cAAc;IAC3C,OAAO;AACT;AACA,QAAQ,OAAO,GAAG;AAClB,eAAe,wBAAwB,EACrC,KAAK,EACL,OAAO,EACR;IACC,MAAM,EACJ,cAAc,EACf,GAAG;IACJ,MAAM,cAAc,QAAQ,MAAM,eAAe,SAAS;IAC1D,IAAI,aAAa,OAAO;IACxB,OAAO;QACL,IAAI;QACJ;QACA,eAAe;IACjB;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1339, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/node_modules/next-auth/core/routes/callback.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = callback;\nvar _callback = _interopRequireDefault(require(\"../lib/oauth/callback\"));\nvar _callbackHandler = _interopRequireDefault(require(\"../lib/callback-handler\"));\nvar _utils = require(\"../lib/utils\");\nvar _getUserFromEmail = _interopRequireDefault(require(\"../lib/email/getUserFromEmail\"));\nasync function callback(params) {\n  const {\n    options,\n    query,\n    body,\n    method,\n    headers,\n    sessionStore\n  } = params;\n  const {\n    provider,\n    adapter,\n    url,\n    callbackUrl,\n    pages,\n    jwt,\n    events,\n    callbacks,\n    session: {\n      strategy: sessionStrategy,\n      maxAge: sessionMaxAge\n    },\n    logger\n  } = options;\n  const cookies = [];\n  const useJwtSession = sessionStrategy === \"jwt\";\n  if (provider.type === \"oauth\") {\n    try {\n      const {\n        profile,\n        account,\n        OAuthProfile,\n        cookies: oauthCookies\n      } = await (0, _callback.default)({\n        query,\n        body,\n        method,\n        options,\n        cookies: params.cookies\n      });\n      if (oauthCookies.length) cookies.push(...oauthCookies);\n      try {\n        var _events$signIn;\n        logger.debug(\"OAUTH_CALLBACK_RESPONSE\", {\n          profile,\n          account,\n          OAuthProfile\n        });\n        if (!profile || !account || !OAuthProfile) {\n          return {\n            redirect: `${url}/signin`,\n            cookies\n          };\n        }\n        let userOrProfile = profile;\n        if (adapter) {\n          const {\n            getUserByAccount\n          } = adapter;\n          const userByAccount = await getUserByAccount({\n            providerAccountId: account.providerAccountId,\n            provider: provider.id\n          });\n          if (userByAccount) userOrProfile = userByAccount;\n        }\n        try {\n          const isAllowed = await callbacks.signIn({\n            user: userOrProfile,\n            account,\n            profile: OAuthProfile\n          });\n          if (!isAllowed) {\n            return {\n              redirect: `${url}/error?error=AccessDenied`,\n              cookies\n            };\n          } else if (typeof isAllowed === \"string\") {\n            return {\n              redirect: isAllowed,\n              cookies\n            };\n          }\n        } catch (error) {\n          return {\n            redirect: `${url}/error?error=${encodeURIComponent(error.message)}`,\n            cookies\n          };\n        }\n        const {\n          user,\n          session,\n          isNewUser\n        } = await (0, _callbackHandler.default)({\n          sessionToken: sessionStore.value,\n          profile,\n          account,\n          options\n        });\n        if (useJwtSession) {\n          var _user$id;\n          const defaultToken = {\n            name: user.name,\n            email: user.email,\n            picture: user.image,\n            sub: (_user$id = user.id) === null || _user$id === void 0 ? void 0 : _user$id.toString()\n          };\n          const token = await callbacks.jwt({\n            token: defaultToken,\n            user,\n            account,\n            profile: OAuthProfile,\n            isNewUser,\n            trigger: isNewUser ? \"signUp\" : \"signIn\"\n          });\n          const newToken = await jwt.encode({\n            ...jwt,\n            token\n          });\n          const cookieExpires = new Date();\n          cookieExpires.setTime(cookieExpires.getTime() + sessionMaxAge * 1000);\n          const sessionCookies = sessionStore.chunk(newToken, {\n            expires: cookieExpires\n          });\n          cookies.push(...sessionCookies);\n        } else {\n          cookies.push({\n            name: options.cookies.sessionToken.name,\n            value: session.sessionToken,\n            options: {\n              ...options.cookies.sessionToken.options,\n              expires: session.expires\n            }\n          });\n        }\n        await ((_events$signIn = events.signIn) === null || _events$signIn === void 0 ? void 0 : _events$signIn.call(events, {\n          user,\n          account,\n          profile,\n          isNewUser\n        }));\n        if (isNewUser && pages.newUser) {\n          return {\n            redirect: `${pages.newUser}${pages.newUser.includes(\"?\") ? \"&\" : \"?\"}callbackUrl=${encodeURIComponent(callbackUrl)}`,\n            cookies\n          };\n        }\n        return {\n          redirect: callbackUrl,\n          cookies\n        };\n      } catch (error) {\n        if (error.name === \"AccountNotLinkedError\") {\n          return {\n            redirect: `${url}/error?error=OAuthAccountNotLinked`,\n            cookies\n          };\n        } else if (error.name === \"CreateUserError\") {\n          return {\n            redirect: `${url}/error?error=OAuthCreateAccount`,\n            cookies\n          };\n        }\n        logger.error(\"OAUTH_CALLBACK_HANDLER_ERROR\", error);\n        return {\n          redirect: `${url}/error?error=Callback`,\n          cookies\n        };\n      }\n    } catch (error) {\n      if (error.name === \"OAuthCallbackError\") {\n        logger.error(\"OAUTH_CALLBACK_ERROR\", {\n          error: error,\n          providerId: provider.id\n        });\n        return {\n          redirect: `${url}/error?error=OAuthCallback`,\n          cookies\n        };\n      }\n      logger.error(\"OAUTH_CALLBACK_ERROR\", error);\n      return {\n        redirect: `${url}/error?error=Callback`,\n        cookies\n      };\n    }\n  } else if (provider.type === \"email\") {\n    try {\n      var _events$signIn2;\n      const paramToken = query === null || query === void 0 ? void 0 : query.token;\n      const paramIdentifier = query === null || query === void 0 ? void 0 : query.email;\n      if (!paramToken) {\n        return {\n          redirect: `${url}/error?error=configuration`,\n          cookies\n        };\n      }\n      const invite = await adapter.useVerificationToken({\n        identifier: paramIdentifier,\n        token: (0, _utils.hashToken)(paramToken, options)\n      });\n      const invalidInvite = !invite || invite.expires.valueOf() < Date.now() || paramIdentifier && invite.identifier !== paramIdentifier;\n      if (invalidInvite) {\n        return {\n          redirect: `${url}/error?error=Verification`,\n          cookies\n        };\n      }\n      const profile = await (0, _getUserFromEmail.default)({\n        email: invite.identifier,\n        adapter\n      });\n      const account = {\n        providerAccountId: profile.email,\n        type: \"email\",\n        provider: provider.id\n      };\n      try {\n        const signInCallbackResponse = await callbacks.signIn({\n          user: profile,\n          account\n        });\n        if (!signInCallbackResponse) {\n          return {\n            redirect: `${url}/error?error=AccessDenied`,\n            cookies\n          };\n        } else if (typeof signInCallbackResponse === \"string\") {\n          return {\n            redirect: signInCallbackResponse,\n            cookies\n          };\n        }\n      } catch (error) {\n        return {\n          redirect: `${url}/error?error=${encodeURIComponent(error.message)}`,\n          cookies\n        };\n      }\n      const {\n        user,\n        session,\n        isNewUser\n      } = await (0, _callbackHandler.default)({\n        sessionToken: sessionStore.value,\n        profile,\n        account,\n        options\n      });\n      if (useJwtSession) {\n        var _user$id2;\n        const defaultToken = {\n          name: user.name,\n          email: user.email,\n          picture: user.image,\n          sub: (_user$id2 = user.id) === null || _user$id2 === void 0 ? void 0 : _user$id2.toString()\n        };\n        const token = await callbacks.jwt({\n          token: defaultToken,\n          user,\n          account,\n          isNewUser,\n          trigger: isNewUser ? \"signUp\" : \"signIn\"\n        });\n        const newToken = await jwt.encode({\n          ...jwt,\n          token\n        });\n        const cookieExpires = new Date();\n        cookieExpires.setTime(cookieExpires.getTime() + sessionMaxAge * 1000);\n        const sessionCookies = sessionStore.chunk(newToken, {\n          expires: cookieExpires\n        });\n        cookies.push(...sessionCookies);\n      } else {\n        cookies.push({\n          name: options.cookies.sessionToken.name,\n          value: session.sessionToken,\n          options: {\n            ...options.cookies.sessionToken.options,\n            expires: session.expires\n          }\n        });\n      }\n      await ((_events$signIn2 = events.signIn) === null || _events$signIn2 === void 0 ? void 0 : _events$signIn2.call(events, {\n        user,\n        account,\n        isNewUser\n      }));\n      if (isNewUser && pages.newUser) {\n        return {\n          redirect: `${pages.newUser}${pages.newUser.includes(\"?\") ? \"&\" : \"?\"}callbackUrl=${encodeURIComponent(callbackUrl)}`,\n          cookies\n        };\n      }\n      return {\n        redirect: callbackUrl,\n        cookies\n      };\n    } catch (error) {\n      if (error.name === \"CreateUserError\") {\n        return {\n          redirect: `${url}/error?error=EmailCreateAccount`,\n          cookies\n        };\n      }\n      logger.error(\"CALLBACK_EMAIL_ERROR\", error);\n      return {\n        redirect: `${url}/error?error=Callback`,\n        cookies\n      };\n    }\n  } else if (provider.type === \"credentials\" && method === \"POST\") {\n    var _user$id3, _events$signIn3;\n    const credentials = body;\n    let user;\n    try {\n      user = await provider.authorize(credentials, {\n        query,\n        body,\n        headers,\n        method\n      });\n      if (!user) {\n        return {\n          status: 401,\n          redirect: `${url}/error?${new URLSearchParams({\n            error: \"CredentialsSignin\",\n            provider: provider.id\n          })}`,\n          cookies\n        };\n      }\n    } catch (error) {\n      return {\n        status: 401,\n        redirect: `${url}/error?error=${encodeURIComponent(error.message)}`,\n        cookies\n      };\n    }\n    const account = {\n      providerAccountId: user.id,\n      type: \"credentials\",\n      provider: provider.id\n    };\n    try {\n      const isAllowed = await callbacks.signIn({\n        user,\n        account,\n        credentials\n      });\n      if (!isAllowed) {\n        return {\n          status: 403,\n          redirect: `${url}/error?error=AccessDenied`,\n          cookies\n        };\n      } else if (typeof isAllowed === \"string\") {\n        return {\n          redirect: isAllowed,\n          cookies\n        };\n      }\n    } catch (error) {\n      return {\n        redirect: `${url}/error?error=${encodeURIComponent(error.message)}`,\n        cookies\n      };\n    }\n    const defaultToken = {\n      name: user.name,\n      email: user.email,\n      picture: user.image,\n      sub: (_user$id3 = user.id) === null || _user$id3 === void 0 ? void 0 : _user$id3.toString()\n    };\n    const token = await callbacks.jwt({\n      token: defaultToken,\n      user,\n      account,\n      isNewUser: false,\n      trigger: \"signIn\"\n    });\n    const newToken = await jwt.encode({\n      ...jwt,\n      token\n    });\n    const cookieExpires = new Date();\n    cookieExpires.setTime(cookieExpires.getTime() + sessionMaxAge * 1000);\n    const sessionCookies = sessionStore.chunk(newToken, {\n      expires: cookieExpires\n    });\n    cookies.push(...sessionCookies);\n    await ((_events$signIn3 = events.signIn) === null || _events$signIn3 === void 0 ? void 0 : _events$signIn3.call(events, {\n      user,\n      account\n    }));\n    return {\n      redirect: callbackUrl,\n      cookies\n    };\n  }\n  return {\n    status: 500,\n    body: `Error: Callback for provider type ${provider.type} not supported`,\n    cookies\n  };\n}"], "names": [], "mappings": "AAAA;AAEA,IAAI;AACJ,OAAO,cAAc,CAAC,SAAS,cAAc;IAC3C,OAAO;AACT;AACA,QAAQ,OAAO,GAAG;AAClB,IAAI,YAAY;AAChB,IAAI,mBAAmB;AACvB,IAAI;AACJ,IAAI,oBAAoB;AACxB,eAAe,SAAS,MAAM;IAC5B,MAAM,EACJ,OAAO,EACP,KAAK,EACL,IAAI,EACJ,MAAM,EACN,OAAO,EACP,YAAY,EACb,GAAG;IACJ,MAAM,EACJ,QAAQ,EACR,OAAO,EACP,GAAG,EACH,WAAW,EACX,KAAK,EACL,GAAG,EACH,MAAM,EACN,SAAS,EACT,SAAS,EACP,UAAU,eAAe,EACzB,QAAQ,aAAa,EACtB,EACD,MAAM,EACP,GAAG;IACJ,MAAM,UAAU,EAAE;IAClB,MAAM,gBAAgB,oBAAoB;IAC1C,IAAI,SAAS,IAAI,KAAK,SAAS;QAC7B,IAAI;YACF,MAAM,EACJ,OAAO,EACP,OAAO,EACP,YAAY,EACZ,SAAS,YAAY,EACtB,GAAG,MAAM,CAAC,GAAG,UAAU,OAAO,EAAE;gBAC/B;gBACA;gBACA;gBACA;gBACA,SAAS,OAAO,OAAO;YACzB;YACA,IAAI,aAAa,MAAM,EAAE,QAAQ,IAAI,IAAI;YACzC,IAAI;gBACF,IAAI;gBACJ,OAAO,KAAK,CAAC,2BAA2B;oBACtC;oBACA;oBACA;gBACF;gBACA,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,cAAc;oBACzC,OAAO;wBACL,UAAU,GAAG,IAAI,OAAO,CAAC;wBACzB;oBACF;gBACF;gBACA,IAAI,gBAAgB;gBACpB,IAAI,SAAS;oBACX,MAAM,EACJ,gBAAgB,EACjB,GAAG;oBACJ,MAAM,gBAAgB,MAAM,iBAAiB;wBAC3C,mBAAmB,QAAQ,iBAAiB;wBAC5C,UAAU,SAAS,EAAE;oBACvB;oBACA,IAAI,eAAe,gBAAgB;gBACrC;gBACA,IAAI;oBACF,MAAM,YAAY,MAAM,UAAU,MAAM,CAAC;wBACvC,MAAM;wBACN;wBACA,SAAS;oBACX;oBACA,IAAI,CAAC,WAAW;wBACd,OAAO;4BACL,UAAU,GAAG,IAAI,yBAAyB,CAAC;4BAC3C;wBACF;oBACF,OAAO,IAAI,OAAO,cAAc,UAAU;wBACxC,OAAO;4BACL,UAAU;4BACV;wBACF;oBACF;gBACF,EAAE,OAAO,OAAO;oBACd,OAAO;wBACL,UAAU,GAAG,IAAI,aAAa,EAAE,mBAAmB,MAAM,OAAO,GAAG;wBACnE;oBACF;gBACF;gBACA,MAAM,EACJ,IAAI,EACJ,OAAO,EACP,SAAS,EACV,GAAG,MAAM,CAAC,GAAG,iBAAiB,OAAO,EAAE;oBACtC,cAAc,aAAa,KAAK;oBAChC;oBACA;oBACA;gBACF;gBACA,IAAI,eAAe;oBACjB,IAAI;oBACJ,MAAM,eAAe;wBACnB,MAAM,KAAK,IAAI;wBACf,OAAO,KAAK,KAAK;wBACjB,SAAS,KAAK,KAAK;wBACnB,KAAK,CAAC,WAAW,KAAK,EAAE,MAAM,QAAQ,aAAa,KAAK,IAAI,KAAK,IAAI,SAAS,QAAQ;oBACxF;oBACA,MAAM,QAAQ,MAAM,UAAU,GAAG,CAAC;wBAChC,OAAO;wBACP;wBACA;wBACA,SAAS;wBACT;wBACA,SAAS,YAAY,WAAW;oBAClC;oBACA,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC;wBAChC,GAAG,GAAG;wBACN;oBACF;oBACA,MAAM,gBAAgB,IAAI;oBAC1B,cAAc,OAAO,CAAC,cAAc,OAAO,KAAK,gBAAgB;oBAChE,MAAM,iBAAiB,aAAa,KAAK,CAAC,UAAU;wBAClD,SAAS;oBACX;oBACA,QAAQ,IAAI,IAAI;gBAClB,OAAO;oBACL,QAAQ,IAAI,CAAC;wBACX,MAAM,QAAQ,OAAO,CAAC,YAAY,CAAC,IAAI;wBACvC,OAAO,QAAQ,YAAY;wBAC3B,SAAS;4BACP,GAAG,QAAQ,OAAO,CAAC,YAAY,CAAC,OAAO;4BACvC,SAAS,QAAQ,OAAO;wBAC1B;oBACF;gBACF;gBACA,MAAM,CAAC,CAAC,iBAAiB,OAAO,MAAM,MAAM,QAAQ,mBAAmB,KAAK,IAAI,KAAK,IAAI,eAAe,IAAI,CAAC,QAAQ;oBACnH;oBACA;oBACA;oBACA;gBACF,EAAE;gBACF,IAAI,aAAa,MAAM,OAAO,EAAE;oBAC9B,OAAO;wBACL,UAAU,GAAG,MAAM,OAAO,GAAG,MAAM,OAAO,CAAC,QAAQ,CAAC,OAAO,MAAM,IAAI,YAAY,EAAE,mBAAmB,cAAc;wBACpH;oBACF;gBACF;gBACA,OAAO;oBACL,UAAU;oBACV;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,IAAI,MAAM,IAAI,KAAK,yBAAyB;oBAC1C,OAAO;wBACL,UAAU,GAAG,IAAI,kCAAkC,CAAC;wBACpD;oBACF;gBACF,OAAO,IAAI,MAAM,IAAI,KAAK,mBAAmB;oBAC3C,OAAO;wBACL,UAAU,GAAG,IAAI,+BAA+B,CAAC;wBACjD;oBACF;gBACF;gBACA,OAAO,KAAK,CAAC,gCAAgC;gBAC7C,OAAO;oBACL,UAAU,GAAG,IAAI,qBAAqB,CAAC;oBACvC;gBACF;YACF;QACF,EAAE,OAAO,OAAO;YACd,IAAI,MAAM,IAAI,KAAK,sBAAsB;gBACvC,OAAO,KAAK,CAAC,wBAAwB;oBACnC,OAAO;oBACP,YAAY,SAAS,EAAE;gBACzB;gBACA,OAAO;oBACL,UAAU,GAAG,IAAI,0BAA0B,CAAC;oBAC5C;gBACF;YACF;YACA,OAAO,KAAK,CAAC,wBAAwB;YACrC,OAAO;gBACL,UAAU,GAAG,IAAI,qBAAqB,CAAC;gBACvC;YACF;QACF;IACF,OAAO,IAAI,SAAS,IAAI,KAAK,SAAS;QACpC,IAAI;YACF,IAAI;YACJ,MAAM,aAAa,UAAU,QAAQ,UAAU,KAAK,IAAI,KAAK,IAAI,MAAM,KAAK;YAC5E,MAAM,kBAAkB,UAAU,QAAQ,UAAU,KAAK,IAAI,KAAK,IAAI,MAAM,KAAK;YACjF,IAAI,CAAC,YAAY;gBACf,OAAO;oBACL,UAAU,GAAG,IAAI,0BAA0B,CAAC;oBAC5C;gBACF;YACF;YACA,MAAM,SAAS,MAAM,QAAQ,oBAAoB,CAAC;gBAChD,YAAY;gBACZ,OAAO,CAAC,GAAG,OAAO,SAAS,EAAE,YAAY;YAC3C;YACA,MAAM,gBAAgB,CAAC,UAAU,OAAO,OAAO,CAAC,OAAO,KAAK,KAAK,GAAG,MAAM,mBAAmB,OAAO,UAAU,KAAK;YACnH,IAAI,eAAe;gBACjB,OAAO;oBACL,UAAU,GAAG,IAAI,yBAAyB,CAAC;oBAC3C;gBACF;YACF;YACA,MAAM,UAAU,MAAM,CAAC,GAAG,kBAAkB,OAAO,EAAE;gBACnD,OAAO,OAAO,UAAU;gBACxB;YACF;YACA,MAAM,UAAU;gBACd,mBAAmB,QAAQ,KAAK;gBAChC,MAAM;gBACN,UAAU,SAAS,EAAE;YACvB;YACA,IAAI;gBACF,MAAM,yBAAyB,MAAM,UAAU,MAAM,CAAC;oBACpD,MAAM;oBACN;gBACF;gBACA,IAAI,CAAC,wBAAwB;oBAC3B,OAAO;wBACL,UAAU,GAAG,IAAI,yBAAyB,CAAC;wBAC3C;oBACF;gBACF,OAAO,IAAI,OAAO,2BAA2B,UAAU;oBACrD,OAAO;wBACL,UAAU;wBACV;oBACF;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,OAAO;oBACL,UAAU,GAAG,IAAI,aAAa,EAAE,mBAAmB,MAAM,OAAO,GAAG;oBACnE;gBACF;YACF;YACA,MAAM,EACJ,IAAI,EACJ,OAAO,EACP,SAAS,EACV,GAAG,MAAM,CAAC,GAAG,iBAAiB,OAAO,EAAE;gBACtC,cAAc,aAAa,KAAK;gBAChC;gBACA;gBACA;YACF;YACA,IAAI,eAAe;gBACjB,IAAI;gBACJ,MAAM,eAAe;oBACnB,MAAM,KAAK,IAAI;oBACf,OAAO,KAAK,KAAK;oBACjB,SAAS,KAAK,KAAK;oBACnB,KAAK,CAAC,YAAY,KAAK,EAAE,MAAM,QAAQ,cAAc,KAAK,IAAI,KAAK,IAAI,UAAU,QAAQ;gBAC3F;gBACA,MAAM,QAAQ,MAAM,UAAU,GAAG,CAAC;oBAChC,OAAO;oBACP;oBACA;oBACA;oBACA,SAAS,YAAY,WAAW;gBAClC;gBACA,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC;oBAChC,GAAG,GAAG;oBACN;gBACF;gBACA,MAAM,gBAAgB,IAAI;gBAC1B,cAAc,OAAO,CAAC,cAAc,OAAO,KAAK,gBAAgB;gBAChE,MAAM,iBAAiB,aAAa,KAAK,CAAC,UAAU;oBAClD,SAAS;gBACX;gBACA,QAAQ,IAAI,IAAI;YAClB,OAAO;gBACL,QAAQ,IAAI,CAAC;oBACX,MAAM,QAAQ,OAAO,CAAC,YAAY,CAAC,IAAI;oBACvC,OAAO,QAAQ,YAAY;oBAC3B,SAAS;wBACP,GAAG,QAAQ,OAAO,CAAC,YAAY,CAAC,OAAO;wBACvC,SAAS,QAAQ,OAAO;oBAC1B;gBACF;YACF;YACA,MAAM,CAAC,CAAC,kBAAkB,OAAO,MAAM,MAAM,QAAQ,oBAAoB,KAAK,IAAI,KAAK,IAAI,gBAAgB,IAAI,CAAC,QAAQ;gBACtH;gBACA;gBACA;YACF,EAAE;YACF,IAAI,aAAa,MAAM,OAAO,EAAE;gBAC9B,OAAO;oBACL,UAAU,GAAG,MAAM,OAAO,GAAG,MAAM,OAAO,CAAC,QAAQ,CAAC,OAAO,MAAM,IAAI,YAAY,EAAE,mBAAmB,cAAc;oBACpH;gBACF;YACF;YACA,OAAO;gBACL,UAAU;gBACV;YACF;QACF,EAAE,OAAO,OAAO;YACd,IAAI,MAAM,IAAI,KAAK,mBAAmB;gBACpC,OAAO;oBACL,UAAU,GAAG,IAAI,+BAA+B,CAAC;oBACjD;gBACF;YACF;YACA,OAAO,KAAK,CAAC,wBAAwB;YACrC,OAAO;gBACL,UAAU,GAAG,IAAI,qBAAqB,CAAC;gBACvC;YACF;QACF;IACF,OAAO,IAAI,SAAS,IAAI,KAAK,iBAAiB,WAAW,QAAQ;QAC/D,IAAI,WAAW;QACf,MAAM,cAAc;QACpB,IAAI;QACJ,IAAI;YACF,OAAO,MAAM,SAAS,SAAS,CAAC,aAAa;gBAC3C;gBACA;gBACA;gBACA;YACF;YACA,IAAI,CAAC,MAAM;gBACT,OAAO;oBACL,QAAQ;oBACR,UAAU,GAAG,IAAI,OAAO,EAAE,IAAI,gBAAgB;wBAC5C,OAAO;wBACP,UAAU,SAAS,EAAE;oBACvB,IAAI;oBACJ;gBACF;YACF;QACF,EAAE,OAAO,OAAO;YACd,OAAO;gBACL,QAAQ;gBACR,UAAU,GAAG,IAAI,aAAa,EAAE,mBAAmB,MAAM,OAAO,GAAG;gBACnE;YACF;QACF;QACA,MAAM,UAAU;YACd,mBAAmB,KAAK,EAAE;YAC1B,MAAM;YACN,UAAU,SAAS,EAAE;QACvB;QACA,IAAI;YACF,MAAM,YAAY,MAAM,UAAU,MAAM,CAAC;gBACvC;gBACA;gBACA;YACF;YACA,IAAI,CAAC,WAAW;gBACd,OAAO;oBACL,QAAQ;oBACR,UAAU,GAAG,IAAI,yBAAyB,CAAC;oBAC3C;gBACF;YACF,OAAO,IAAI,OAAO,cAAc,UAAU;gBACxC,OAAO;oBACL,UAAU;oBACV;gBACF;YACF;QACF,EAAE,OAAO,OAAO;YACd,OAAO;gBACL,UAAU,GAAG,IAAI,aAAa,EAAE,mBAAmB,MAAM,OAAO,GAAG;gBACnE;YACF;QACF;QACA,MAAM,eAAe;YACnB,MAAM,KAAK,IAAI;YACf,OAAO,KAAK,KAAK;YACjB,SAAS,KAAK,KAAK;YACnB,KAAK,CAAC,YAAY,KAAK,EAAE,MAAM,QAAQ,cAAc,KAAK,IAAI,KAAK,IAAI,UAAU,QAAQ;QAC3F;QACA,MAAM,QAAQ,MAAM,UAAU,GAAG,CAAC;YAChC,OAAO;YACP;YACA;YACA,WAAW;YACX,SAAS;QACX;QACA,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC;YAChC,GAAG,GAAG;YACN;QACF;QACA,MAAM,gBAAgB,IAAI;QAC1B,cAAc,OAAO,CAAC,cAAc,OAAO,KAAK,gBAAgB;QAChE,MAAM,iBAAiB,aAAa,KAAK,CAAC,UAAU;YAClD,SAAS;QACX;QACA,QAAQ,IAAI,IAAI;QAChB,MAAM,CAAC,CAAC,kBAAkB,OAAO,MAAM,MAAM,QAAQ,oBAAoB,KAAK,IAAI,KAAK,IAAI,gBAAgB,IAAI,CAAC,QAAQ;YACtH;YACA;QACF,EAAE;QACF,OAAO;YACL,UAAU;YACV;QACF;IACF;IACA,OAAO;QACL,QAAQ;QACR,MAAM,CAAC,kCAAkC,EAAE,SAAS,IAAI,CAAC,cAAc,CAAC;QACxE;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1724, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/node_modules/next-auth/core/lib/oauth/authorization-url.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = getAuthorizationUrl;\nvar _client = require(\"./client\");\nvar _clientLegacy = require(\"./client-legacy\");\nvar checks = _interopRequireWildcard(require(\"./checks\"));\nfunction _getRequireWildcardCache(e) { if (\"function\" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function (e) { return e ? t : r; })(e); }\nfunction _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if (\"default\" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }\nasync function getAuthorizationUrl({\n  options,\n  query\n}) {\n  var _provider$version;\n  const {\n    logger,\n    provider\n  } = options;\n  let params = {};\n  if (typeof provider.authorization === \"string\") {\n    const parsedUrl = new URL(provider.authorization);\n    const parsedParams = Object.fromEntries(parsedUrl.searchParams);\n    params = {\n      ...params,\n      ...parsedParams\n    };\n  } else {\n    var _provider$authorizati;\n    params = {\n      ...params,\n      ...((_provider$authorizati = provider.authorization) === null || _provider$authorizati === void 0 ? void 0 : _provider$authorizati.params)\n    };\n  }\n  params = {\n    ...params,\n    ...query\n  };\n  if ((_provider$version = provider.version) !== null && _provider$version !== void 0 && _provider$version.startsWith(\"1.\")) {\n    var _provider$authorizati2;\n    const client = (0, _clientLegacy.oAuth1Client)(options);\n    const tokens = await client.getOAuthRequestToken(params);\n    const url = `${(_provider$authorizati2 = provider.authorization) === null || _provider$authorizati2 === void 0 ? void 0 : _provider$authorizati2.url}?${new URLSearchParams({\n      oauth_token: tokens.oauth_token,\n      oauth_token_secret: tokens.oauth_token_secret,\n      ...tokens.params\n    })}`;\n    _clientLegacy.oAuth1TokenStore.set(tokens.oauth_token, tokens.oauth_token_secret);\n    logger.debug(\"GET_AUTHORIZATION_URL\", {\n      url,\n      provider\n    });\n    return {\n      redirect: url\n    };\n  }\n  const client = await (0, _client.openidClient)(options);\n  const authorizationParams = params;\n  const cookies = [];\n  await checks.state.create(options, cookies, authorizationParams);\n  await checks.pkce.create(options, cookies, authorizationParams);\n  await checks.nonce.create(options, cookies, authorizationParams);\n  const url = client.authorizationUrl(authorizationParams);\n  logger.debug(\"GET_AUTHORIZATION_URL\", {\n    url,\n    cookies,\n    provider\n  });\n  return {\n    redirect: url,\n    cookies\n  };\n}"], "names": [], "mappings": "AAAA;AAEA,OAAO,cAAc,CAAC,SAAS,cAAc;IAC3C,OAAO;AACT;AACA,QAAQ,OAAO,GAAG;AAClB,IAAI;AACJ,IAAI;AACJ,IAAI,SAAS;AACb,SAAS,yBAAyB,CAAC;IAAI,IAAI,cAAc,OAAO,SAAS,OAAO;IAAM,IAAI,IAAI,IAAI,WAAW,IAAI,IAAI;IAAW,OAAO,CAAC,2BAA2B,SAAU,CAAC;QAAI,OAAO,IAAI,IAAI;IAAG,CAAC,EAAE;AAAI;AAC3M,SAAS,wBAAwB,CAAC,EAAE,CAAC;IAAI,IAAI,CAAC,KAAK,KAAK,EAAE,UAAU,EAAE,OAAO;IAAG,IAAI,SAAS,KAAK,YAAY,OAAO,KAAK,cAAc,OAAO,GAAG,OAAO;QAAE,SAAS;IAAE;IAAG,IAAI,IAAI,yBAAyB;IAAI,IAAI,KAAK,EAAE,GAAG,CAAC,IAAI,OAAO,EAAE,GAAG,CAAC;IAAI,IAAI,IAAI;QAAE,WAAW;IAAK,GAAG,IAAI,OAAO,cAAc,IAAI,OAAO,wBAAwB;IAAE,IAAK,IAAI,KAAK,EAAG,IAAI,cAAc,KAAK,CAAA,CAAC,CAAA,EAAE,cAAc,CAAC,IAAI,CAAC,GAAG,IAAI;QAAE,IAAI,IAAI,IAAI,OAAO,wBAAwB,CAAC,GAAG,KAAK;QAAM,KAAK,CAAC,EAAE,GAAG,IAAI,EAAE,GAAG,IAAI,OAAO,cAAc,CAAC,GAAG,GAAG,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;IAAE;IAAE,OAAO,EAAE,OAAO,GAAG,GAAG,KAAK,EAAE,GAAG,CAAC,GAAG,IAAI;AAAG;AAClkB,eAAe,oBAAoB,EACjC,OAAO,EACP,KAAK,EACN;IACC,IAAI;IACJ,MAAM,EACJ,MAAM,EACN,QAAQ,EACT,GAAG;IACJ,IAAI,SAAS,CAAC;IACd,IAAI,OAAO,SAAS,aAAa,KAAK,UAAU;QAC9C,MAAM,YAAY,IAAI,IAAI,SAAS,aAAa;QAChD,MAAM,eAAe,OAAO,WAAW,CAAC,UAAU,YAAY;QAC9D,SAAS;YACP,GAAG,MAAM;YACT,GAAG,YAAY;QACjB;IACF,OAAO;QACL,IAAI;QACJ,SAAS;YACP,GAAG,MAAM;YACT,GAAI,CAAC,wBAAwB,SAAS,aAAa,MAAM,QAAQ,0BAA0B,KAAK,IAAI,KAAK,IAAI,sBAAsB,MAAM;QAC3I;IACF;IACA,SAAS;QACP,GAAG,MAAM;QACT,GAAG,KAAK;IACV;IACA,IAAI,CAAC,oBAAoB,SAAS,OAAO,MAAM,QAAQ,sBAAsB,KAAK,KAAK,kBAAkB,UAAU,CAAC,OAAO;QACzH,IAAI;QACJ,MAAM,SAAS,CAAC,GAAG,cAAc,YAAY,EAAE;QAC/C,MAAM,SAAS,MAAM,OAAO,oBAAoB,CAAC;QACjD,MAAM,MAAM,GAAG,CAAC,yBAAyB,SAAS,aAAa,MAAM,QAAQ,2BAA2B,KAAK,IAAI,KAAK,IAAI,uBAAuB,GAAG,CAAC,CAAC,EAAE,IAAI,gBAAgB;YAC1K,aAAa,OAAO,WAAW;YAC/B,oBAAoB,OAAO,kBAAkB;YAC7C,GAAG,OAAO,MAAM;QAClB,IAAI;QACJ,cAAc,gBAAgB,CAAC,GAAG,CAAC,OAAO,WAAW,EAAE,OAAO,kBAAkB;QAChF,OAAO,KAAK,CAAC,yBAAyB;YACpC;YACA;QACF;QACA,OAAO;YACL,UAAU;QACZ;IACF;IACA,MAAM,SAAS,MAAM,CAAC,GAAG,QAAQ,YAAY,EAAE;IAC/C,MAAM,sBAAsB;IAC5B,MAAM,UAAU,EAAE;IAClB,MAAM,OAAO,KAAK,CAAC,MAAM,CAAC,SAAS,SAAS;IAC5C,MAAM,OAAO,IAAI,CAAC,MAAM,CAAC,SAAS,SAAS;IAC3C,MAAM,OAAO,KAAK,CAAC,MAAM,CAAC,SAAS,SAAS;IAC5C,MAAM,MAAM,OAAO,gBAAgB,CAAC;IACpC,OAAO,KAAK,CAAC,yBAAyB;QACpC;QACA;QACA;IACF;IACA,OAAO;QACL,UAAU;QACV;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1817, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/node_modules/next-auth/core/lib/email/signin.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = email;\nvar _crypto = require(\"crypto\");\nvar _utils = require(\"../utils\");\nasync function email(identifier, options) {\n  var _await$provider$gener, _provider$generateVer, _provider$maxAge, _adapter$createVerifi;\n  const {\n    url,\n    adapter,\n    provider,\n    callbackUrl,\n    theme\n  } = options;\n  const token = (_await$provider$gener = await ((_provider$generateVer = provider.generateVerificationToken) === null || _provider$generateVer === void 0 ? void 0 : _provider$generateVer.call(provider))) !== null && _await$provider$gener !== void 0 ? _await$provider$gener : (0, _crypto.randomBytes)(32).toString(\"hex\");\n  const ONE_DAY_IN_SECONDS = 86400;\n  const expires = new Date(Date.now() + ((_provider$maxAge = provider.maxAge) !== null && _provider$maxAge !== void 0 ? _provider$maxAge : ONE_DAY_IN_SECONDS) * 1000);\n  const params = new URLSearchParams({\n    callbackUrl,\n    token,\n    email: identifier\n  });\n  const _url = `${url}/callback/${provider.id}?${params}`;\n  await Promise.all([provider.sendVerificationRequest({\n    identifier,\n    token,\n    expires,\n    url: _url,\n    provider,\n    theme\n  }), (_adapter$createVerifi = adapter.createVerificationToken) === null || _adapter$createVerifi === void 0 ? void 0 : _adapter$createVerifi.call(adapter, {\n    identifier,\n    token: (0, _utils.hashToken)(token, options),\n    expires\n  })]);\n  return `${url}/verify-request?${new URLSearchParams({\n    provider: provider.id,\n    type: provider.type\n  })}`;\n}"], "names": [], "mappings": "AAAA;AAEA,OAAO,cAAc,CAAC,SAAS,cAAc;IAC3C,OAAO;AACT;AACA,QAAQ,OAAO,GAAG;AAClB,IAAI;AACJ,IAAI;AACJ,eAAe,MAAM,UAAU,EAAE,OAAO;IACtC,IAAI,uBAAuB,uBAAuB,kBAAkB;IACpE,MAAM,EACJ,GAAG,EACH,OAAO,EACP,QAAQ,EACR,WAAW,EACX,KAAK,EACN,GAAG;IACJ,MAAM,QAAQ,CAAC,wBAAwB,MAAM,CAAC,CAAC,wBAAwB,SAAS,yBAAyB,MAAM,QAAQ,0BAA0B,KAAK,IAAI,KAAK,IAAI,sBAAsB,IAAI,CAAC,SAAS,CAAC,MAAM,QAAQ,0BAA0B,KAAK,IAAI,wBAAwB,CAAC,GAAG,QAAQ,WAAW,EAAE,IAAI,QAAQ,CAAC;IACvT,MAAM,qBAAqB;IAC3B,MAAM,UAAU,IAAI,KAAK,KAAK,GAAG,KAAK,CAAC,CAAC,mBAAmB,SAAS,MAAM,MAAM,QAAQ,qBAAqB,KAAK,IAAI,mBAAmB,kBAAkB,IAAI;IAC/J,MAAM,SAAS,IAAI,gBAAgB;QACjC;QACA;QACA,OAAO;IACT;IACA,MAAM,OAAO,GAAG,IAAI,UAAU,EAAE,SAAS,EAAE,CAAC,CAAC,EAAE,QAAQ;IACvD,MAAM,QAAQ,GAAG,CAAC;QAAC,SAAS,uBAAuB,CAAC;YAClD;YACA;YACA;YACA,KAAK;YACL;YACA;QACF;QAAI,CAAC,wBAAwB,QAAQ,uBAAuB,MAAM,QAAQ,0BAA0B,KAAK,IAAI,KAAK,IAAI,sBAAsB,IAAI,CAAC,SAAS;YACxJ;YACA,OAAO,CAAC,GAAG,OAAO,SAAS,EAAE,OAAO;YACpC;QACF;KAAG;IACH,OAAO,GAAG,IAAI,gBAAgB,EAAE,IAAI,gBAAgB;QAClD,UAAU,SAAS,EAAE;QACrB,MAAM,SAAS,IAAI;IACrB,IAAI;AACN", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1861, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/node_modules/next-auth/core/routes/signin.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = signin;\nvar _authorizationUrl = _interopRequireDefault(require(\"../lib/oauth/authorization-url\"));\nvar _signin = _interopRequireDefault(require(\"../lib/email/signin\"));\nvar _getUserFromEmail = _interopRequireDefault(require(\"../lib/email/getUserFromEmail\"));\nasync function signin(params) {\n  const {\n    options,\n    query,\n    body\n  } = params;\n  const {\n    url,\n    callbacks,\n    logger,\n    provider\n  } = options;\n  if (!provider.type) {\n    return {\n      status: 500,\n      text: `Error: Type not specified for ${provider.name}`\n    };\n  }\n  if (provider.type === \"oauth\") {\n    try {\n      const response = await (0, _authorizationUrl.default)({\n        options,\n        query\n      });\n      return response;\n    } catch (error) {\n      logger.error(\"SIGNIN_OAUTH_ERROR\", {\n        error: error,\n        providerId: provider.id\n      });\n      return {\n        redirect: `${url}/error?error=OAuthSignin`\n      };\n    }\n  } else if (provider.type === \"email\") {\n    var _provider$normalizeId;\n    let email = body === null || body === void 0 ? void 0 : body.email;\n    if (!email) return {\n      redirect: `${url}/error?error=EmailSignin`\n    };\n    const normalizer = (_provider$normalizeId = provider.normalizeIdentifier) !== null && _provider$normalizeId !== void 0 ? _provider$normalizeId : identifier => {\n      let [local, domain] = identifier.toLowerCase().trim().split(\"@\");\n      domain = domain.split(\",\")[0];\n      return `${local}@${domain}`;\n    };\n    try {\n      email = normalizer(body === null || body === void 0 ? void 0 : body.email);\n    } catch (error) {\n      logger.error(\"SIGNIN_EMAIL_ERROR\", {\n        error,\n        providerId: provider.id\n      });\n      return {\n        redirect: `${url}/error?error=EmailSignin`\n      };\n    }\n    const user = await (0, _getUserFromEmail.default)({\n      email,\n      adapter: options.adapter\n    });\n    const account = {\n      providerAccountId: email,\n      userId: email,\n      type: \"email\",\n      provider: provider.id\n    };\n    try {\n      const signInCallbackResponse = await callbacks.signIn({\n        user,\n        account,\n        email: {\n          verificationRequest: true\n        }\n      });\n      if (!signInCallbackResponse) {\n        return {\n          redirect: `${url}/error?error=AccessDenied`\n        };\n      } else if (typeof signInCallbackResponse === \"string\") {\n        return {\n          redirect: signInCallbackResponse\n        };\n      }\n    } catch (error) {\n      return {\n        redirect: `${url}/error?${new URLSearchParams({\n          error: error\n        })}`\n      };\n    }\n    try {\n      const redirect = await (0, _signin.default)(email, options);\n      return {\n        redirect\n      };\n    } catch (error) {\n      logger.error(\"SIGNIN_EMAIL_ERROR\", {\n        error,\n        providerId: provider.id\n      });\n      return {\n        redirect: `${url}/error?error=EmailSignin`\n      };\n    }\n  }\n  return {\n    redirect: `${url}/signin`\n  };\n}"], "names": [], "mappings": "AAAA;AAEA,IAAI;AACJ,OAAO,cAAc,CAAC,SAAS,cAAc;IAC3C,OAAO;AACT;AACA,QAAQ,OAAO,GAAG;AAClB,IAAI,oBAAoB;AACxB,IAAI,UAAU;AACd,IAAI,oBAAoB;AACxB,eAAe,OAAO,MAAM;IAC1B,MAAM,EACJ,OAAO,EACP,KAAK,EACL,IAAI,EACL,GAAG;IACJ,MAAM,EACJ,GAAG,EACH,SAAS,EACT,MAAM,EACN,QAAQ,EACT,GAAG;IACJ,IAAI,CAAC,SAAS,IAAI,EAAE;QAClB,OAAO;YACL,QAAQ;YACR,MAAM,CAAC,8BAA8B,EAAE,SAAS,IAAI,EAAE;QACxD;IACF;IACA,IAAI,SAAS,IAAI,KAAK,SAAS;QAC7B,IAAI;YACF,MAAM,WAAW,MAAM,CAAC,GAAG,kBAAkB,OAAO,EAAE;gBACpD;gBACA;YACF;YACA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,OAAO,KAAK,CAAC,sBAAsB;gBACjC,OAAO;gBACP,YAAY,SAAS,EAAE;YACzB;YACA,OAAO;gBACL,UAAU,GAAG,IAAI,wBAAwB,CAAC;YAC5C;QACF;IACF,OAAO,IAAI,SAAS,IAAI,KAAK,SAAS;QACpC,IAAI;QACJ,IAAI,QAAQ,SAAS,QAAQ,SAAS,KAAK,IAAI,KAAK,IAAI,KAAK,KAAK;QAClE,IAAI,CAAC,OAAO,OAAO;YACjB,UAAU,GAAG,IAAI,wBAAwB,CAAC;QAC5C;QACA,MAAM,aAAa,CAAC,wBAAwB,SAAS,mBAAmB,MAAM,QAAQ,0BAA0B,KAAK,IAAI,wBAAwB,CAAA;YAC/I,IAAI,CAAC,OAAO,OAAO,GAAG,WAAW,WAAW,GAAG,IAAI,GAAG,KAAK,CAAC;YAC5D,SAAS,OAAO,KAAK,CAAC,IAAI,CAAC,EAAE;YAC7B,OAAO,GAAG,MAAM,CAAC,EAAE,QAAQ;QAC7B;QACA,IAAI;YACF,QAAQ,WAAW,SAAS,QAAQ,SAAS,KAAK,IAAI,KAAK,IAAI,KAAK,KAAK;QAC3E,EAAE,OAAO,OAAO;YACd,OAAO,KAAK,CAAC,sBAAsB;gBACjC;gBACA,YAAY,SAAS,EAAE;YACzB;YACA,OAAO;gBACL,UAAU,GAAG,IAAI,wBAAwB,CAAC;YAC5C;QACF;QACA,MAAM,OAAO,MAAM,CAAC,GAAG,kBAAkB,OAAO,EAAE;YAChD;YACA,SAAS,QAAQ,OAAO;QAC1B;QACA,MAAM,UAAU;YACd,mBAAmB;YACnB,QAAQ;YACR,MAAM;YACN,UAAU,SAAS,EAAE;QACvB;QACA,IAAI;YACF,MAAM,yBAAyB,MAAM,UAAU,MAAM,CAAC;gBACpD;gBACA;gBACA,OAAO;oBACL,qBAAqB;gBACvB;YACF;YACA,IAAI,CAAC,wBAAwB;gBAC3B,OAAO;oBACL,UAAU,GAAG,IAAI,yBAAyB,CAAC;gBAC7C;YACF,OAAO,IAAI,OAAO,2BAA2B,UAAU;gBACrD,OAAO;oBACL,UAAU;gBACZ;YACF;QACF,EAAE,OAAO,OAAO;YACd,OAAO;gBACL,UAAU,GAAG,IAAI,OAAO,EAAE,IAAI,gBAAgB;oBAC5C,OAAO;gBACT,IAAI;YACN;QACF;QACA,IAAI;YACF,MAAM,WAAW,MAAM,CAAC,GAAG,QAAQ,OAAO,EAAE,OAAO;YACnD,OAAO;gBACL;YACF;QACF,EAAE,OAAO,OAAO;YACd,OAAO,KAAK,CAAC,sBAAsB;gBACjC;gBACA,YAAY,SAAS,EAAE;YACzB;YACA,OAAO;gBACL,UAAU,GAAG,IAAI,wBAAwB,CAAC;YAC5C;QACF;IACF;IACA,OAAO;QACL,UAAU,GAAG,IAAI,OAAO,CAAC;IAC3B;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1975, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/node_modules/next-auth/core/routes/signout.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = signout;\nasync function signout(params) {\n  const {\n    options,\n    sessionStore\n  } = params;\n  const {\n    adapter,\n    events,\n    jwt,\n    callbackUrl,\n    logger,\n    session\n  } = options;\n  const sessionToken = sessionStore === null || sessionStore === void 0 ? void 0 : sessionStore.value;\n  if (!sessionToken) {\n    return {\n      redirect: callbackUrl\n    };\n  }\n  if (session.strategy === \"jwt\") {\n    try {\n      var _events$signOut;\n      const decodedJwt = await jwt.decode({\n        ...jwt,\n        token: sessionToken\n      });\n      await ((_events$signOut = events.signOut) === null || _events$signOut === void 0 ? void 0 : _events$signOut.call(events, {\n        token: decodedJwt\n      }));\n    } catch (error) {\n      logger.error(\"SIGNOUT_ERROR\", error);\n    }\n  } else {\n    try {\n      var _events$signOut2;\n      const session = await adapter.deleteSession(sessionToken);\n      await ((_events$signOut2 = events.signOut) === null || _events$signOut2 === void 0 ? void 0 : _events$signOut2.call(events, {\n        session\n      }));\n    } catch (error) {\n      logger.error(\"SIGNOUT_ERROR\", error);\n    }\n  }\n  const sessionCookies = sessionStore.clean();\n  return {\n    redirect: callbackUrl,\n    cookies: sessionCookies\n  };\n}"], "names": [], "mappings": "AAAA;AAEA,OAAO,cAAc,CAAC,SAAS,cAAc;IAC3C,OAAO;AACT;AACA,QAAQ,OAAO,GAAG;AAClB,eAAe,QAAQ,MAAM;IAC3B,MAAM,EACJ,OAAO,EACP,YAAY,EACb,GAAG;IACJ,MAAM,EACJ,OAAO,EACP,MAAM,EACN,GAAG,EACH,WAAW,EACX,MAAM,EACN,OAAO,EACR,GAAG;IACJ,MAAM,eAAe,iBAAiB,QAAQ,iBAAiB,KAAK,IAAI,KAAK,IAAI,aAAa,KAAK;IACnG,IAAI,CAAC,cAAc;QACjB,OAAO;YACL,UAAU;QACZ;IACF;IACA,IAAI,QAAQ,QAAQ,KAAK,OAAO;QAC9B,IAAI;YACF,IAAI;YACJ,MAAM,aAAa,MAAM,IAAI,MAAM,CAAC;gBAClC,GAAG,GAAG;gBACN,OAAO;YACT;YACA,MAAM,CAAC,CAAC,kBAAkB,OAAO,OAAO,MAAM,QAAQ,oBAAoB,KAAK,IAAI,KAAK,IAAI,gBAAgB,IAAI,CAAC,QAAQ;gBACvH,OAAO;YACT,EAAE;QACJ,EAAE,OAAO,OAAO;YACd,OAAO,KAAK,CAAC,iBAAiB;QAChC;IACF,OAAO;QACL,IAAI;YACF,IAAI;YACJ,MAAM,UAAU,MAAM,QAAQ,aAAa,CAAC;YAC5C,MAAM,CAAC,CAAC,mBAAmB,OAAO,OAAO,MAAM,QAAQ,qBAAqB,KAAK,IAAI,KAAK,IAAI,iBAAiB,IAAI,CAAC,QAAQ;gBAC1H;YACF,EAAE;QACJ,EAAE,OAAO,OAAO;YACd,OAAO,KAAK,CAAC,iBAAiB;QAChC;IACF;IACA,MAAM,iBAAiB,aAAa,KAAK;IACzC,OAAO;QACL,UAAU;QACV,SAAS;IACX;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2024, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/node_modules/next-auth/core/routes/session.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = session;\nvar _utils = require(\"../lib/utils\");\nasync function session(params) {\n  const {\n    options,\n    sessionStore,\n    newSession,\n    isUpdate\n  } = params;\n  const {\n    adapter,\n    jwt,\n    events,\n    callbacks,\n    logger,\n    session: {\n      strategy: sessionStrategy,\n      maxAge: sessionMaxAge\n    }\n  } = options;\n  const response = {\n    body: {},\n    headers: [{\n      key: \"Content-Type\",\n      value: \"application/json\"\n    }],\n    cookies: []\n  };\n  const sessionToken = sessionStore.value;\n  if (!sessionToken) return response;\n  if (sessionStrategy === \"jwt\") {\n    try {\n      var _response$cookies, _events$session;\n      const decodedToken = await jwt.decode({\n        ...jwt,\n        token: sessionToken\n      });\n      if (!decodedToken) throw new Error(\"JWT invalid\");\n      const token = await callbacks.jwt({\n        token: decodedToken,\n        ...(isUpdate && {\n          trigger: \"update\"\n        }),\n        session: newSession\n      });\n      const newExpires = (0, _utils.fromDate)(sessionMaxAge);\n      const updatedSession = await callbacks.session({\n        session: {\n          user: {\n            name: decodedToken === null || decodedToken === void 0 ? void 0 : decodedToken.name,\n            email: decodedToken === null || decodedToken === void 0 ? void 0 : decodedToken.email,\n            image: decodedToken === null || decodedToken === void 0 ? void 0 : decodedToken.picture\n          },\n          expires: newExpires.toISOString()\n        },\n        token\n      });\n      response.body = updatedSession;\n      const newToken = await jwt.encode({\n        ...jwt,\n        token,\n        maxAge: options.session.maxAge\n      });\n      const sessionCookies = sessionStore.chunk(newToken, {\n        expires: newExpires\n      });\n      (_response$cookies = response.cookies) === null || _response$cookies === void 0 || _response$cookies.push(...sessionCookies);\n      await ((_events$session = events.session) === null || _events$session === void 0 ? void 0 : _events$session.call(events, {\n        session: updatedSession,\n        token\n      }));\n    } catch (error) {\n      var _response$cookies2;\n      logger.error(\"JWT_SESSION_ERROR\", error);\n      (_response$cookies2 = response.cookies) === null || _response$cookies2 === void 0 || _response$cookies2.push(...sessionStore.clean());\n    }\n  } else {\n    try {\n      const {\n        getSessionAndUser,\n        deleteSession,\n        updateSession\n      } = adapter;\n      let userAndSession = await getSessionAndUser(sessionToken);\n      if (userAndSession && userAndSession.session.expires.valueOf() < Date.now()) {\n        await deleteSession(sessionToken);\n        userAndSession = null;\n      }\n      if (userAndSession) {\n        var _response$cookies3, _events$session2;\n        const {\n          user,\n          session\n        } = userAndSession;\n        const sessionUpdateAge = options.session.updateAge;\n        const sessionIsDueToBeUpdatedDate = session.expires.valueOf() - sessionMaxAge * 1000 + sessionUpdateAge * 1000;\n        const newExpires = (0, _utils.fromDate)(sessionMaxAge);\n        if (sessionIsDueToBeUpdatedDate <= Date.now()) {\n          await updateSession({\n            sessionToken,\n            expires: newExpires\n          });\n        }\n        const sessionPayload = await callbacks.session({\n          session: {\n            user: {\n              name: user.name,\n              email: user.email,\n              image: user.image\n            },\n            expires: session.expires.toISOString()\n          },\n          user,\n          newSession,\n          ...(isUpdate ? {\n            trigger: \"update\"\n          } : {})\n        });\n        response.body = sessionPayload;\n        (_response$cookies3 = response.cookies) === null || _response$cookies3 === void 0 || _response$cookies3.push({\n          name: options.cookies.sessionToken.name,\n          value: sessionToken,\n          options: {\n            ...options.cookies.sessionToken.options,\n            expires: newExpires\n          }\n        });\n        await ((_events$session2 = events.session) === null || _events$session2 === void 0 ? void 0 : _events$session2.call(events, {\n          session: sessionPayload\n        }));\n      } else if (sessionToken) {\n        var _response$cookies4;\n        (_response$cookies4 = response.cookies) === null || _response$cookies4 === void 0 || _response$cookies4.push(...sessionStore.clean());\n      }\n    } catch (error) {\n      logger.error(\"SESSION_ERROR\", error);\n    }\n  }\n  return response;\n}"], "names": [], "mappings": "AAAA;AAEA,OAAO,cAAc,CAAC,SAAS,cAAc;IAC3C,OAAO;AACT;AACA,QAAQ,OAAO,GAAG;AAClB,IAAI;AACJ,eAAe,QAAQ,MAAM;IAC3B,MAAM,EACJ,OAAO,EACP,YAAY,EACZ,UAAU,EACV,QAAQ,EACT,GAAG;IACJ,MAAM,EACJ,OAAO,EACP,GAAG,EACH,MAAM,EACN,SAAS,EACT,MAAM,EACN,SAAS,EACP,UAAU,eAAe,EACzB,QAAQ,aAAa,EACtB,EACF,GAAG;IACJ,MAAM,WAAW;QACf,MAAM,CAAC;QACP,SAAS;YAAC;gBACR,KAAK;gBACL,OAAO;YACT;SAAE;QACF,SAAS,EAAE;IACb;IACA,MAAM,eAAe,aAAa,KAAK;IACvC,IAAI,CAAC,cAAc,OAAO;IAC1B,IAAI,oBAAoB,OAAO;QAC7B,IAAI;YACF,IAAI,mBAAmB;YACvB,MAAM,eAAe,MAAM,IAAI,MAAM,CAAC;gBACpC,GAAG,GAAG;gBACN,OAAO;YACT;YACA,IAAI,CAAC,cAAc,MAAM,IAAI,MAAM;YACnC,MAAM,QAAQ,MAAM,UAAU,GAAG,CAAC;gBAChC,OAAO;gBACP,GAAI,YAAY;oBACd,SAAS;gBACX,CAAC;gBACD,SAAS;YACX;YACA,MAAM,aAAa,CAAC,GAAG,OAAO,QAAQ,EAAE;YACxC,MAAM,iBAAiB,MAAM,UAAU,OAAO,CAAC;gBAC7C,SAAS;oBACP,MAAM;wBACJ,MAAM,iBAAiB,QAAQ,iBAAiB,KAAK,IAAI,KAAK,IAAI,aAAa,IAAI;wBACnF,OAAO,iBAAiB,QAAQ,iBAAiB,KAAK,IAAI,KAAK,IAAI,aAAa,KAAK;wBACrF,OAAO,iBAAiB,QAAQ,iBAAiB,KAAK,IAAI,KAAK,IAAI,aAAa,OAAO;oBACzF;oBACA,SAAS,WAAW,WAAW;gBACjC;gBACA;YACF;YACA,SAAS,IAAI,GAAG;YAChB,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC;gBAChC,GAAG,GAAG;gBACN;gBACA,QAAQ,QAAQ,OAAO,CAAC,MAAM;YAChC;YACA,MAAM,iBAAiB,aAAa,KAAK,CAAC,UAAU;gBAClD,SAAS;YACX;YACA,CAAC,oBAAoB,SAAS,OAAO,MAAM,QAAQ,sBAAsB,KAAK,KAAK,kBAAkB,IAAI,IAAI;YAC7G,MAAM,CAAC,CAAC,kBAAkB,OAAO,OAAO,MAAM,QAAQ,oBAAoB,KAAK,IAAI,KAAK,IAAI,gBAAgB,IAAI,CAAC,QAAQ;gBACvH,SAAS;gBACT;YACF,EAAE;QACJ,EAAE,OAAO,OAAO;YACd,IAAI;YACJ,OAAO,KAAK,CAAC,qBAAqB;YAClC,CAAC,qBAAqB,SAAS,OAAO,MAAM,QAAQ,uBAAuB,KAAK,KAAK,mBAAmB,IAAI,IAAI,aAAa,KAAK;QACpI;IACF,OAAO;QACL,IAAI;YACF,MAAM,EACJ,iBAAiB,EACjB,aAAa,EACb,aAAa,EACd,GAAG;YACJ,IAAI,iBAAiB,MAAM,kBAAkB;YAC7C,IAAI,kBAAkB,eAAe,OAAO,CAAC,OAAO,CAAC,OAAO,KAAK,KAAK,GAAG,IAAI;gBAC3E,MAAM,cAAc;gBACpB,iBAAiB;YACnB;YACA,IAAI,gBAAgB;gBAClB,IAAI,oBAAoB;gBACxB,MAAM,EACJ,IAAI,EACJ,OAAO,EACR,GAAG;gBACJ,MAAM,mBAAmB,QAAQ,OAAO,CAAC,SAAS;gBAClD,MAAM,8BAA8B,QAAQ,OAAO,CAAC,OAAO,KAAK,gBAAgB,OAAO,mBAAmB;gBAC1G,MAAM,aAAa,CAAC,GAAG,OAAO,QAAQ,EAAE;gBACxC,IAAI,+BAA+B,KAAK,GAAG,IAAI;oBAC7C,MAAM,cAAc;wBAClB;wBACA,SAAS;oBACX;gBACF;gBACA,MAAM,iBAAiB,MAAM,UAAU,OAAO,CAAC;oBAC7C,SAAS;wBACP,MAAM;4BACJ,MAAM,KAAK,IAAI;4BACf,OAAO,KAAK,KAAK;4BACjB,OAAO,KAAK,KAAK;wBACnB;wBACA,SAAS,QAAQ,OAAO,CAAC,WAAW;oBACtC;oBACA;oBACA;oBACA,GAAI,WAAW;wBACb,SAAS;oBACX,IAAI,CAAC,CAAC;gBACR;gBACA,SAAS,IAAI,GAAG;gBAChB,CAAC,qBAAqB,SAAS,OAAO,MAAM,QAAQ,uBAAuB,KAAK,KAAK,mBAAmB,IAAI,CAAC;oBAC3G,MAAM,QAAQ,OAAO,CAAC,YAAY,CAAC,IAAI;oBACvC,OAAO;oBACP,SAAS;wBACP,GAAG,QAAQ,OAAO,CAAC,YAAY,CAAC,OAAO;wBACvC,SAAS;oBACX;gBACF;gBACA,MAAM,CAAC,CAAC,mBAAmB,OAAO,OAAO,MAAM,QAAQ,qBAAqB,KAAK,IAAI,KAAK,IAAI,iBAAiB,IAAI,CAAC,QAAQ;oBAC1H,SAAS;gBACX,EAAE;YACJ,OAAO,IAAI,cAAc;gBACvB,IAAI;gBACJ,CAAC,qBAAqB,SAAS,OAAO,MAAM,QAAQ,uBAAuB,KAAK,KAAK,mBAAmB,IAAI,IAAI,aAAa,KAAK;YACpI;QACF,EAAE,OAAO,OAAO;YACd,OAAO,KAAK,CAAC,iBAAiB;QAChC;IACF;IACA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2153, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/node_modules/next-auth/core/routes/providers.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = providers;\nfunction providers(providers) {\n  return {\n    headers: [{\n      key: \"Content-Type\",\n      value: \"application/json\"\n    }],\n    body: providers.reduce((acc, {\n      id,\n      name,\n      type,\n      signinUrl,\n      callbackUrl\n    }) => {\n      acc[id] = {\n        id,\n        name,\n        type,\n        signinUrl,\n        callbackUrl\n      };\n      return acc;\n    }, {})\n  };\n}"], "names": [], "mappings": "AAAA;AAEA,OAAO,cAAc,CAAC,SAAS,cAAc;IAC3C,OAAO;AACT;AACA,QAAQ,OAAO,GAAG;AAClB,SAAS,UAAU,SAAS;IAC1B,OAAO;QACL,SAAS;YAAC;gBACR,KAAK;gBACL,OAAO;YACT;SAAE;QACF,MAAM,UAAU,MAAM,CAAC,CAAC,KAAK,EAC3B,EAAE,EACF,IAAI,EACJ,IAAI,EACJ,SAAS,EACT,WAAW,EACZ;YACC,GAAG,CAAC,GAAG,GAAG;gBACR;gBACA;gBACA;gBACA;gBACA;YACF;YACA,OAAO;QACT,GAAG,CAAC;IACN;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2183, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/node_modules/next-auth/core/routes/index.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nObject.defineProperty(exports, \"callback\", {\n  enumerable: true,\n  get: function () {\n    return _callback.default;\n  }\n});\nObject.defineProperty(exports, \"providers\", {\n  enumerable: true,\n  get: function () {\n    return _providers.default;\n  }\n});\nObject.defineProperty(exports, \"session\", {\n  enumerable: true,\n  get: function () {\n    return _session.default;\n  }\n});\nObject.defineProperty(exports, \"signin\", {\n  enumerable: true,\n  get: function () {\n    return _signin.default;\n  }\n});\nObject.defineProperty(exports, \"signout\", {\n  enumerable: true,\n  get: function () {\n    return _signout.default;\n  }\n});\nvar _callback = _interopRequireDefault(require(\"./callback\"));\nvar _signin = _interopRequireDefault(require(\"./signin\"));\nvar _signout = _interopRequireDefault(require(\"./signout\"));\nvar _session = _interopRequireDefault(require(\"./session\"));\nvar _providers = _interopRequireDefault(require(\"./providers\"));"], "names": [], "mappings": "AAAA;AAEA,IAAI;AACJ,OAAO,cAAc,CAAC,SAAS,cAAc;IAC3C,OAAO;AACT;AACA,OAAO,cAAc,CAAC,SAAS,YAAY;IACzC,YAAY;IACZ,KAAK;QACH,OAAO,UAAU,OAAO;IAC1B;AACF;AACA,OAAO,cAAc,CAAC,SAAS,aAAa;IAC1C,YAAY;IACZ,KAAK;QACH,OAAO,WAAW,OAAO;IAC3B;AACF;AACA,OAAO,cAAc,CAAC,SAAS,WAAW;IACxC,YAAY;IACZ,KAAK;QACH,OAAO,SAAS,OAAO;IACzB;AACF;AACA,OAAO,cAAc,CAAC,SAAS,UAAU;IACvC,YAAY;IACZ,KAAK;QACH,OAAO,QAAQ,OAAO;IACxB;AACF;AACA,OAAO,cAAc,CAAC,SAAS,WAAW;IACxC,YAAY;IACZ,KAAK;QACH,OAAO,SAAS,OAAO;IACzB;AACF;AACA,IAAI,YAAY;AAChB,IAAI,UAAU;AACd,IAAI,WAAW;AACf,IAAI,WAAW;AACf,IAAI,aAAa", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2228, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/node_modules/next-auth/core/pages/signin.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = SigninPage;\nvar _preact = require(\"preact\");\nvar _extends2 = _interopRequireDefault(require(\"@babel/runtime/helpers/extends\"));\nfunction hexToRgba(hex, alpha = 1) {\n  if (!hex) {\n    return;\n  }\n  hex = hex.replace(/^#/, \"\");\n  if (hex.length === 3) {\n    hex = hex[0] + hex[0] + hex[1] + hex[1] + hex[2] + hex[2];\n  }\n  const bigint = parseInt(hex, 16);\n  const r = bigint >> 16 & 255;\n  const g = bigint >> 8 & 255;\n  const b = bigint & 255;\n  alpha = Math.min(Math.max(alpha, 0), 1);\n  const rgba = `rgba(${r}, ${g}, ${b}, ${alpha})`;\n  return rgba;\n}\nfunction SigninPage(props) {\n  var _errors$errorType;\n  const {\n    csrfToken,\n    providers,\n    callbackUrl,\n    theme,\n    email,\n    error: errorType\n  } = props;\n  const providersToRender = providers.filter(provider => {\n    if (provider.type === \"oauth\" || provider.type === \"email\") {\n      return true;\n    } else if (provider.type === \"credentials\" && provider.credentials) {\n      return true;\n    }\n    return false;\n  });\n  if (typeof document !== \"undefined\" && theme.buttonText) {\n    document.documentElement.style.setProperty(\"--button-text-color\", theme.buttonText);\n  }\n  if (typeof document !== \"undefined\" && theme.brandColor) {\n    document.documentElement.style.setProperty(\"--brand-color\", theme.brandColor);\n  }\n  const errors = {\n    Signin: \"Try signing in with a different account.\",\n    OAuthSignin: \"Try signing in with a different account.\",\n    OAuthCallback: \"Try signing in with a different account.\",\n    OAuthCreateAccount: \"Try signing in with a different account.\",\n    EmailCreateAccount: \"Try signing in with a different account.\",\n    Callback: \"Try signing in with a different account.\",\n    OAuthAccountNotLinked: \"To confirm your identity, sign in with the same account you used originally.\",\n    EmailSignin: \"The e-mail could not be sent.\",\n    CredentialsSignin: \"Sign in failed. Check the details you provided are correct.\",\n    SessionRequired: \"Please sign in to access this page.\",\n    default: \"Unable to sign in.\"\n  };\n  const error = errorType && ((_errors$errorType = errors[errorType]) !== null && _errors$errorType !== void 0 ? _errors$errorType : errors.default);\n  const providerLogoPath = \"https://authjs.dev/img/providers\";\n  return (0, _preact.h)(\"div\", {\n    className: \"signin\"\n  }, theme.brandColor && (0, _preact.h)(\"style\", {\n    dangerouslySetInnerHTML: {\n      __html: `\n        :root {\n          --brand-color: ${theme.brandColor}\n        }\n      `\n    }\n  }), theme.buttonText && (0, _preact.h)(\"style\", {\n    dangerouslySetInnerHTML: {\n      __html: `\n        :root {\n          --button-text-color: ${theme.buttonText}\n        }\n      `\n    }\n  }), (0, _preact.h)(\"div\", {\n    className: \"card\"\n  }, theme.logo && (0, _preact.h)(\"img\", {\n    src: theme.logo,\n    alt: \"Logo\",\n    className: \"logo\"\n  }), error && (0, _preact.h)(\"div\", {\n    className: \"error\"\n  }, (0, _preact.h)(\"p\", null, error)), providersToRender.map((provider, i) => {\n    let bg, text, logo, logoDark, bgDark, textDark;\n    if (provider.type === \"oauth\") {\n      var _provider$style;\n      ;\n      ({\n        bg = \"\",\n        text = \"\",\n        logo = \"\",\n        bgDark = bg,\n        textDark = text,\n        logoDark = \"\"\n      } = (_provider$style = provider.style) !== null && _provider$style !== void 0 ? _provider$style : {});\n      logo = logo.startsWith(\"/\") ? `${providerLogoPath}${logo}` : logo;\n      logoDark = logoDark.startsWith(\"/\") ? `${providerLogoPath}${logoDark}` : logoDark || logo;\n      logoDark || (logoDark = logo);\n    }\n    return (0, _preact.h)(\"div\", {\n      key: provider.id,\n      className: \"provider\"\n    }, provider.type === \"oauth\" && (0, _preact.h)(\"form\", {\n      action: provider.signinUrl,\n      method: \"POST\"\n    }, (0, _preact.h)(\"input\", {\n      type: \"hidden\",\n      name: \"csrfToken\",\n      value: csrfToken\n    }), callbackUrl && (0, _preact.h)(\"input\", {\n      type: \"hidden\",\n      name: \"callbackUrl\",\n      value: callbackUrl\n    }), (0, _preact.h)(\"button\", {\n      type: \"submit\",\n      className: \"button\",\n      style: {\n        \"--provider-bg\": bg,\n        \"--provider-dark-bg\": bgDark,\n        \"--provider-color\": text,\n        \"--provider-dark-color\": textDark,\n        \"--provider-bg-hover\": hexToRgba(bg, 0.8),\n        \"--provider-dark-bg-hover\": hexToRgba(bgDark, 0.8)\n      }\n    }, logo && (0, _preact.h)(\"img\", {\n      loading: \"lazy\",\n      height: 24,\n      width: 24,\n      id: \"provider-logo\",\n      src: `${logo.startsWith(\"/\") ? providerLogoPath : \"\"}${logo}`\n    }), logoDark && (0, _preact.h)(\"img\", {\n      loading: \"lazy\",\n      height: 24,\n      width: 24,\n      id: \"provider-logo-dark\",\n      src: `${logo.startsWith(\"/\") ? providerLogoPath : \"\"}${logoDark}`\n    }), (0, _preact.h)(\"span\", null, \"Sign in with \", provider.name))), (provider.type === \"email\" || provider.type === \"credentials\") && i > 0 && providersToRender[i - 1].type !== \"email\" && providersToRender[i - 1].type !== \"credentials\" && (0, _preact.h)(\"hr\", null), provider.type === \"email\" && (0, _preact.h)(\"form\", {\n      action: provider.signinUrl,\n      method: \"POST\"\n    }, (0, _preact.h)(\"input\", {\n      type: \"hidden\",\n      name: \"csrfToken\",\n      value: csrfToken\n    }), (0, _preact.h)(\"label\", {\n      className: \"section-header\",\n      htmlFor: `input-email-for-${provider.id}-provider`\n    }, \"Email\"), (0, _preact.h)(\"input\", {\n      id: `input-email-for-${provider.id}-provider`,\n      autoFocus: true,\n      type: \"email\",\n      name: \"email\",\n      value: email,\n      placeholder: \"<EMAIL>\",\n      required: true\n    }), (0, _preact.h)(\"button\", {\n      id: \"submitButton\",\n      type: \"submit\"\n    }, \"Sign in with \", provider.name)), provider.type === \"credentials\" && (0, _preact.h)(\"form\", {\n      action: provider.callbackUrl,\n      method: \"POST\"\n    }, (0, _preact.h)(\"input\", {\n      type: \"hidden\",\n      name: \"csrfToken\",\n      value: csrfToken\n    }), Object.keys(provider.credentials).map(credential => {\n      var _provider$credentials, _provider$credentials2, _provider$credentials3;\n      return (0, _preact.h)(\"div\", {\n        key: `input-group-${provider.id}`\n      }, (0, _preact.h)(\"label\", {\n        className: \"section-header\",\n        htmlFor: `input-${credential}-for-${provider.id}-provider`\n      }, (_provider$credentials = provider.credentials[credential].label) !== null && _provider$credentials !== void 0 ? _provider$credentials : credential), (0, _preact.h)(\"input\", (0, _extends2.default)({\n        name: credential,\n        id: `input-${credential}-for-${provider.id}-provider`,\n        type: (_provider$credentials2 = provider.credentials[credential].type) !== null && _provider$credentials2 !== void 0 ? _provider$credentials2 : \"text\",\n        placeholder: (_provider$credentials3 = provider.credentials[credential].placeholder) !== null && _provider$credentials3 !== void 0 ? _provider$credentials3 : \"\"\n      }, provider.credentials[credential])));\n    }), (0, _preact.h)(\"button\", {\n      type: \"submit\"\n    }, \"Sign in with \", provider.name)), (provider.type === \"email\" || provider.type === \"credentials\") && i + 1 < providersToRender.length && (0, _preact.h)(\"hr\", null));\n  })));\n}"], "names": [], "mappings": "AAAA;AAEA,IAAI;AACJ,OAAO,cAAc,CAAC,SAAS,cAAc;IAC3C,OAAO;AACT;AACA,QAAQ,OAAO,GAAG;AAClB,IAAI;AACJ,IAAI,YAAY;AAChB,SAAS,UAAU,GAAG,EAAE,QAAQ,CAAC;IAC/B,IAAI,CAAC,KAAK;QACR;IACF;IACA,MAAM,IAAI,OAAO,CAAC,MAAM;IACxB,IAAI,IAAI,MAAM,KAAK,GAAG;QACpB,MAAM,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE;IAC3D;IACA,MAAM,SAAS,SAAS,KAAK;IAC7B,MAAM,IAAI,UAAU,KAAK;IACzB,MAAM,IAAI,UAAU,IAAI;IACxB,MAAM,IAAI,SAAS;IACnB,QAAQ,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,OAAO,IAAI;IACrC,MAAM,OAAO,CAAC,KAAK,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,MAAM,CAAC,CAAC;IAC/C,OAAO;AACT;AACA,SAAS,WAAW,KAAK;IACvB,IAAI;IACJ,MAAM,EACJ,SAAS,EACT,SAAS,EACT,WAAW,EACX,KAAK,EACL,KAAK,EACL,OAAO,SAAS,EACjB,GAAG;IACJ,MAAM,oBAAoB,UAAU,MAAM,CAAC,CAAA;QACzC,IAAI,SAAS,IAAI,KAAK,WAAW,SAAS,IAAI,KAAK,SAAS;YAC1D,OAAO;QACT,OAAO,IAAI,SAAS,IAAI,KAAK,iBAAiB,SAAS,WAAW,EAAE;YAClE,OAAO;QACT;QACA,OAAO;IACT;IACA,IAAI,OAAO,aAAa,eAAe,MAAM,UAAU,EAAE;QACvD,SAAS,eAAe,CAAC,KAAK,CAAC,WAAW,CAAC,uBAAuB,MAAM,UAAU;IACpF;IACA,IAAI,OAAO,aAAa,eAAe,MAAM,UAAU,EAAE;QACvD,SAAS,eAAe,CAAC,KAAK,CAAC,WAAW,CAAC,iBAAiB,MAAM,UAAU;IAC9E;IACA,MAAM,SAAS;QACb,QAAQ;QACR,aAAa;QACb,eAAe;QACf,oBAAoB;QACpB,oBAAoB;QACpB,UAAU;QACV,uBAAuB;QACvB,aAAa;QACb,mBAAmB;QACnB,iBAAiB;QACjB,SAAS;IACX;IACA,MAAM,QAAQ,aAAa,CAAC,CAAC,oBAAoB,MAAM,CAAC,UAAU,MAAM,QAAQ,sBAAsB,KAAK,IAAI,oBAAoB,OAAO,OAAO;IACjJ,MAAM,mBAAmB;IACzB,OAAO,CAAC,GAAG,QAAQ,CAAC,EAAE,OAAO;QAC3B,WAAW;IACb,GAAG,MAAM,UAAU,IAAI,CAAC,GAAG,QAAQ,CAAC,EAAE,SAAS;QAC7C,yBAAyB;YACvB,QAAQ,CAAC;;yBAEU,EAAE,MAAM,UAAU,CAAC;;MAEtC,CAAC;QACH;IACF,IAAI,MAAM,UAAU,IAAI,CAAC,GAAG,QAAQ,CAAC,EAAE,SAAS;QAC9C,yBAAyB;YACvB,QAAQ,CAAC;;+BAEgB,EAAE,MAAM,UAAU,CAAC;;MAE5C,CAAC;QACH;IACF,IAAI,CAAC,GAAG,QAAQ,CAAC,EAAE,OAAO;QACxB,WAAW;IACb,GAAG,MAAM,IAAI,IAAI,CAAC,GAAG,QAAQ,CAAC,EAAE,OAAO;QACrC,KAAK,MAAM,IAAI;QACf,KAAK;QACL,WAAW;IACb,IAAI,SAAS,CAAC,GAAG,QAAQ,CAAC,EAAE,OAAO;QACjC,WAAW;IACb,GAAG,CAAC,GAAG,QAAQ,CAAC,EAAE,KAAK,MAAM,SAAS,kBAAkB,GAAG,CAAC,CAAC,UAAU;QACrE,IAAI,IAAI,MAAM,MAAM,UAAU,QAAQ;QACtC,IAAI,SAAS,IAAI,KAAK,SAAS;YAC7B,IAAI;;YAEJ,CAAC,EACC,KAAK,EAAE,EACP,OAAO,EAAE,EACT,OAAO,EAAE,EACT,SAAS,EAAE,EACX,WAAW,IAAI,EACf,WAAW,EAAE,EACd,GAAG,CAAC,kBAAkB,SAAS,KAAK,MAAM,QAAQ,oBAAoB,KAAK,IAAI,kBAAkB,CAAC,CAAC;YACpG,OAAO,KAAK,UAAU,CAAC,OAAO,GAAG,mBAAmB,MAAM,GAAG;YAC7D,WAAW,SAAS,UAAU,CAAC,OAAO,GAAG,mBAAmB,UAAU,GAAG,YAAY;YACrF,YAAY,CAAC,WAAW,IAAI;QAC9B;QACA,OAAO,CAAC,GAAG,QAAQ,CAAC,EAAE,OAAO;YAC3B,KAAK,SAAS,EAAE;YAChB,WAAW;QACb,GAAG,SAAS,IAAI,KAAK,WAAW,CAAC,GAAG,QAAQ,CAAC,EAAE,QAAQ;YACrD,QAAQ,SAAS,SAAS;YAC1B,QAAQ;QACV,GAAG,CAAC,GAAG,QAAQ,CAAC,EAAE,SAAS;YACzB,MAAM;YACN,MAAM;YACN,OAAO;QACT,IAAI,eAAe,CAAC,GAAG,QAAQ,CAAC,EAAE,SAAS;YACzC,MAAM;YACN,MAAM;YACN,OAAO;QACT,IAAI,CAAC,GAAG,QAAQ,CAAC,EAAE,UAAU;YAC3B,MAAM;YACN,WAAW;YACX,OAAO;gBACL,iBAAiB;gBACjB,sBAAsB;gBACtB,oBAAoB;gBACpB,yBAAyB;gBACzB,uBAAuB,UAAU,IAAI;gBACrC,4BAA4B,UAAU,QAAQ;YAChD;QACF,GAAG,QAAQ,CAAC,GAAG,QAAQ,CAAC,EAAE,OAAO;YAC/B,SAAS;YACT,QAAQ;YACR,OAAO;YACP,IAAI;YACJ,KAAK,GAAG,KAAK,UAAU,CAAC,OAAO,mBAAmB,KAAK,MAAM;QAC/D,IAAI,YAAY,CAAC,GAAG,QAAQ,CAAC,EAAE,OAAO;YACpC,SAAS;YACT,QAAQ;YACR,OAAO;YACP,IAAI;YACJ,KAAK,GAAG,KAAK,UAAU,CAAC,OAAO,mBAAmB,KAAK,UAAU;QACnE,IAAI,CAAC,GAAG,QAAQ,CAAC,EAAE,QAAQ,MAAM,iBAAiB,SAAS,IAAI,KAAK,CAAC,SAAS,IAAI,KAAK,WAAW,SAAS,IAAI,KAAK,aAAa,KAAK,IAAI,KAAK,iBAAiB,CAAC,IAAI,EAAE,CAAC,IAAI,KAAK,WAAW,iBAAiB,CAAC,IAAI,EAAE,CAAC,IAAI,KAAK,iBAAiB,CAAC,GAAG,QAAQ,CAAC,EAAE,MAAM,OAAO,SAAS,IAAI,KAAK,WAAW,CAAC,GAAG,QAAQ,CAAC,EAAE,QAAQ;YAC7T,QAAQ,SAAS,SAAS;YAC1B,QAAQ;QACV,GAAG,CAAC,GAAG,QAAQ,CAAC,EAAE,SAAS;YACzB,MAAM;YACN,MAAM;YACN,OAAO;QACT,IAAI,CAAC,GAAG,QAAQ,CAAC,EAAE,SAAS;YAC1B,WAAW;YACX,SAAS,CAAC,gBAAgB,EAAE,SAAS,EAAE,CAAC,SAAS,CAAC;QACpD,GAAG,UAAU,CAAC,GAAG,QAAQ,CAAC,EAAE,SAAS;YACnC,IAAI,CAAC,gBAAgB,EAAE,SAAS,EAAE,CAAC,SAAS,CAAC;YAC7C,WAAW;YACX,MAAM;YACN,MAAM;YACN,OAAO;YACP,aAAa;YACb,UAAU;QACZ,IAAI,CAAC,GAAG,QAAQ,CAAC,EAAE,UAAU;YAC3B,IAAI;YACJ,MAAM;QACR,GAAG,iBAAiB,SAAS,IAAI,IAAI,SAAS,IAAI,KAAK,iBAAiB,CAAC,GAAG,QAAQ,CAAC,EAAE,QAAQ;YAC7F,QAAQ,SAAS,WAAW;YAC5B,QAAQ;QACV,GAAG,CAAC,GAAG,QAAQ,CAAC,EAAE,SAAS;YACzB,MAAM;YACN,MAAM;YACN,OAAO;QACT,IAAI,OAAO,IAAI,CAAC,SAAS,WAAW,EAAE,GAAG,CAAC,CAAA;YACxC,IAAI,uBAAuB,wBAAwB;YACnD,OAAO,CAAC,GAAG,QAAQ,CAAC,EAAE,OAAO;gBAC3B,KAAK,CAAC,YAAY,EAAE,SAAS,EAAE,EAAE;YACnC,GAAG,CAAC,GAAG,QAAQ,CAAC,EAAE,SAAS;gBACzB,WAAW;gBACX,SAAS,CAAC,MAAM,EAAE,WAAW,KAAK,EAAE,SAAS,EAAE,CAAC,SAAS,CAAC;YAC5D,GAAG,CAAC,wBAAwB,SAAS,WAAW,CAAC,WAAW,CAAC,KAAK,MAAM,QAAQ,0BAA0B,KAAK,IAAI,wBAAwB,aAAa,CAAC,GAAG,QAAQ,CAAC,EAAE,SAAS,CAAC,GAAG,UAAU,OAAO,EAAE;gBACrM,MAAM;gBACN,IAAI,CAAC,MAAM,EAAE,WAAW,KAAK,EAAE,SAAS,EAAE,CAAC,SAAS,CAAC;gBACrD,MAAM,CAAC,yBAAyB,SAAS,WAAW,CAAC,WAAW,CAAC,IAAI,MAAM,QAAQ,2BAA2B,KAAK,IAAI,yBAAyB;gBAChJ,aAAa,CAAC,yBAAyB,SAAS,WAAW,CAAC,WAAW,CAAC,WAAW,MAAM,QAAQ,2BAA2B,KAAK,IAAI,yBAAyB;YAChK,GAAG,SAAS,WAAW,CAAC,WAAW;QACrC,IAAI,CAAC,GAAG,QAAQ,CAAC,EAAE,UAAU;YAC3B,MAAM;QACR,GAAG,iBAAiB,SAAS,IAAI,IAAI,CAAC,SAAS,IAAI,KAAK,WAAW,SAAS,IAAI,KAAK,aAAa,KAAK,IAAI,IAAI,kBAAkB,MAAM,IAAI,CAAC,GAAG,QAAQ,CAAC,EAAE,MAAM;IAClK;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2408, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/node_modules/next-auth/core/pages/signout.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = SignoutPage;\nvar _preact = require(\"preact\");\nfunction SignoutPage(props) {\n  const {\n    url,\n    csrfToken,\n    theme\n  } = props;\n  return (0, _preact.h)(\"div\", {\n    className: \"signout\"\n  }, theme.brandColor && (0, _preact.h)(\"style\", {\n    dangerouslySetInnerHTML: {\n      __html: `\n        :root {\n          --brand-color: ${theme.brandColor}\n        }\n      `\n    }\n  }), theme.buttonText && (0, _preact.h)(\"style\", {\n    dangerouslySetInnerHTML: {\n      __html: `\n        :root {\n          --button-text-color: ${theme.buttonText}\n        }\n      `\n    }\n  }), (0, _preact.h)(\"div\", {\n    className: \"card\"\n  }, theme.logo && (0, _preact.h)(\"img\", {\n    src: theme.logo,\n    alt: \"Logo\",\n    className: \"logo\"\n  }), (0, _preact.h)(\"h1\", null, \"Signout\"), (0, _preact.h)(\"p\", null, \"Are you sure you want to sign out?\"), (0, _preact.h)(\"form\", {\n    action: `${url}/signout`,\n    method: \"POST\"\n  }, (0, _preact.h)(\"input\", {\n    type: \"hidden\",\n    name: \"csrfToken\",\n    value: csrfToken\n  }), (0, _preact.h)(\"button\", {\n    id: \"submitButton\",\n    type: \"submit\"\n  }, \"Sign out\"))));\n}"], "names": [], "mappings": "AAAA;AAEA,OAAO,cAAc,CAAC,SAAS,cAAc;IAC3C,OAAO;AACT;AACA,QAAQ,OAAO,GAAG;AAClB,IAAI;AACJ,SAAS,YAAY,KAAK;IACxB,MAAM,EACJ,GAAG,EACH,SAAS,EACT,KAAK,EACN,GAAG;IACJ,OAAO,CAAC,GAAG,QAAQ,CAAC,EAAE,OAAO;QAC3B,WAAW;IACb,GAAG,MAAM,UAAU,IAAI,CAAC,GAAG,QAAQ,CAAC,EAAE,SAAS;QAC7C,yBAAyB;YACvB,QAAQ,CAAC;;yBAEU,EAAE,MAAM,UAAU,CAAC;;MAEtC,CAAC;QACH;IACF,IAAI,MAAM,UAAU,IAAI,CAAC,GAAG,QAAQ,CAAC,EAAE,SAAS;QAC9C,yBAAyB;YACvB,QAAQ,CAAC;;+BAEgB,EAAE,MAAM,UAAU,CAAC;;MAE5C,CAAC;QACH;IACF,IAAI,CAAC,GAAG,QAAQ,CAAC,EAAE,OAAO;QACxB,WAAW;IACb,GAAG,MAAM,IAAI,IAAI,CAAC,GAAG,QAAQ,CAAC,EAAE,OAAO;QACrC,KAAK,MAAM,IAAI;QACf,KAAK;QACL,WAAW;IACb,IAAI,CAAC,GAAG,QAAQ,CAAC,EAAE,MAAM,MAAM,YAAY,CAAC,GAAG,QAAQ,CAAC,EAAE,KAAK,MAAM,uCAAuC,CAAC,GAAG,QAAQ,CAAC,EAAE,QAAQ;QACjI,QAAQ,GAAG,IAAI,QAAQ,CAAC;QACxB,QAAQ;IACV,GAAG,CAAC,GAAG,QAAQ,CAAC,EAAE,SAAS;QACzB,MAAM;QACN,MAAM;QACN,OAAO;IACT,IAAI,CAAC,GAAG,QAAQ,CAAC,EAAE,UAAU;QAC3B,IAAI;QACJ,MAAM;IACR,GAAG;AACL", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2457, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/node_modules/next-auth/core/pages/verify-request.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = VerifyRequestPage;\nvar _preact = require(\"preact\");\nfunction VerifyRequestPage(props) {\n  const {\n    url,\n    theme\n  } = props;\n  return (0, _preact.h)(\"div\", {\n    className: \"verify-request\"\n  }, theme.brandColor && (0, _preact.h)(\"style\", {\n    dangerouslySetInnerHTML: {\n      __html: `\n        :root {\n          --brand-color: ${theme.brandColor}\n        }\n      `\n    }\n  }), (0, _preact.h)(\"div\", {\n    className: \"card\"\n  }, theme.logo && (0, _preact.h)(\"img\", {\n    src: theme.logo,\n    alt: \"Logo\",\n    className: \"logo\"\n  }), (0, _preact.h)(\"h1\", null, \"Check your email\"), (0, _preact.h)(\"p\", null, \"A sign in link has been sent to your email address.\"), (0, _preact.h)(\"p\", null, (0, _preact.h)(\"a\", {\n    className: \"site\",\n    href: url.origin\n  }, url.host))));\n}"], "names": [], "mappings": "AAAA;AAEA,OAAO,cAAc,CAAC,SAAS,cAAc;IAC3C,OAAO;AACT;AACA,QAAQ,OAAO,GAAG;AAClB,IAAI;AACJ,SAAS,kBAAkB,KAAK;IAC9B,MAAM,EACJ,GAAG,EACH,KAAK,EACN,GAAG;IACJ,OAAO,CAAC,GAAG,QAAQ,CAAC,EAAE,OAAO;QAC3B,WAAW;IACb,GAAG,MAAM,UAAU,IAAI,CAAC,GAAG,QAAQ,CAAC,EAAE,SAAS;QAC7C,yBAAyB;YACvB,QAAQ,CAAC;;yBAEU,EAAE,MAAM,UAAU,CAAC;;MAEtC,CAAC;QACH;IACF,IAAI,CAAC,GAAG,QAAQ,CAAC,EAAE,OAAO;QACxB,WAAW;IACb,GAAG,MAAM,IAAI,IAAI,CAAC,GAAG,QAAQ,CAAC,EAAE,OAAO;QACrC,KAAK,MAAM,IAAI;QACf,KAAK;QACL,WAAW;IACb,IAAI,CAAC,GAAG,QAAQ,CAAC,EAAE,MAAM,MAAM,qBAAqB,CAAC,GAAG,QAAQ,CAAC,EAAE,KAAK,MAAM,wDAAwD,CAAC,GAAG,QAAQ,CAAC,EAAE,KAAK,MAAM,CAAC,GAAG,QAAQ,CAAC,EAAE,KAAK;QAClL,WAAW;QACX,MAAM,IAAI,MAAM;IAClB,GAAG,IAAI,IAAI;AACb", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2491, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/node_modules/next-auth/core/pages/error.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = ErrorPage;\nvar _preact = require(\"preact\");\nfunction ErrorPage(props) {\n  var _errors$error$toLower;\n  const {\n    url,\n    error = \"default\",\n    theme\n  } = props;\n  const signinPageUrl = `${url}/signin`;\n  const errors = {\n    default: {\n      status: 200,\n      heading: \"Error\",\n      message: (0, _preact.h)(\"p\", null, (0, _preact.h)(\"a\", {\n        className: \"site\",\n        href: url === null || url === void 0 ? void 0 : url.origin\n      }, url === null || url === void 0 ? void 0 : url.host))\n    },\n    configuration: {\n      status: 500,\n      heading: \"Server error\",\n      message: (0, _preact.h)(\"div\", null, (0, _preact.h)(\"p\", null, \"There is a problem with the server configuration.\"), (0, _preact.h)(\"p\", null, \"Check the server logs for more information.\"))\n    },\n    accessdenied: {\n      status: 403,\n      heading: \"Access Denied\",\n      message: (0, _preact.h)(\"div\", null, (0, _preact.h)(\"p\", null, \"You do not have permission to sign in.\"), (0, _preact.h)(\"p\", null, (0, _preact.h)(\"a\", {\n        className: \"button\",\n        href: signinPageUrl\n      }, \"Sign in\")))\n    },\n    verification: {\n      status: 403,\n      heading: \"Unable to sign in\",\n      message: (0, _preact.h)(\"div\", null, (0, _preact.h)(\"p\", null, \"The sign in link is no longer valid.\"), (0, _preact.h)(\"p\", null, \"It may have been used already or it may have expired.\")),\n      signin: (0, _preact.h)(\"a\", {\n        className: \"button\",\n        href: signinPageUrl\n      }, \"Sign in\")\n    }\n  };\n  const {\n    status,\n    heading,\n    message,\n    signin\n  } = (_errors$error$toLower = errors[error.toLowerCase()]) !== null && _errors$error$toLower !== void 0 ? _errors$error$toLower : errors.default;\n  return {\n    status,\n    html: (0, _preact.h)(\"div\", {\n      className: \"error\"\n    }, (theme === null || theme === void 0 ? void 0 : theme.brandColor) && (0, _preact.h)(\"style\", {\n      dangerouslySetInnerHTML: {\n        __html: `\n        :root {\n          --brand-color: ${theme === null || theme === void 0 ? void 0 : theme.brandColor}\n        }\n      `\n      }\n    }), (0, _preact.h)(\"div\", {\n      className: \"card\"\n    }, (theme === null || theme === void 0 ? void 0 : theme.logo) && (0, _preact.h)(\"img\", {\n      src: theme.logo,\n      alt: \"Logo\",\n      className: \"logo\"\n    }), (0, _preact.h)(\"h1\", null, heading), (0, _preact.h)(\"div\", {\n      className: \"message\"\n    }, message), signin))\n  };\n}"], "names": [], "mappings": "AAAA;AAEA,OAAO,cAAc,CAAC,SAAS,cAAc;IAC3C,OAAO;AACT;AACA,QAAQ,OAAO,GAAG;AAClB,IAAI;AACJ,SAAS,UAAU,KAAK;IACtB,IAAI;IACJ,MAAM,EACJ,GAAG,EACH,QAAQ,SAAS,EACjB,KAAK,EACN,GAAG;IACJ,MAAM,gBAAgB,GAAG,IAAI,OAAO,CAAC;IACrC,MAAM,SAAS;QACb,SAAS;YACP,QAAQ;YACR,SAAS;YACT,SAAS,CAAC,GAAG,QAAQ,CAAC,EAAE,KAAK,MAAM,CAAC,GAAG,QAAQ,CAAC,EAAE,KAAK;gBACrD,WAAW;gBACX,MAAM,QAAQ,QAAQ,QAAQ,KAAK,IAAI,KAAK,IAAI,IAAI,MAAM;YAC5D,GAAG,QAAQ,QAAQ,QAAQ,KAAK,IAAI,KAAK,IAAI,IAAI,IAAI;QACvD;QACA,eAAe;YACb,QAAQ;YACR,SAAS;YACT,SAAS,CAAC,GAAG,QAAQ,CAAC,EAAE,OAAO,MAAM,CAAC,GAAG,QAAQ,CAAC,EAAE,KAAK,MAAM,sDAAsD,CAAC,GAAG,QAAQ,CAAC,EAAE,KAAK,MAAM;QACjJ;QACA,cAAc;YACZ,QAAQ;YACR,SAAS;YACT,SAAS,CAAC,GAAG,QAAQ,CAAC,EAAE,OAAO,MAAM,CAAC,GAAG,QAAQ,CAAC,EAAE,KAAK,MAAM,2CAA2C,CAAC,GAAG,QAAQ,CAAC,EAAE,KAAK,MAAM,CAAC,GAAG,QAAQ,CAAC,EAAE,KAAK;gBACtJ,WAAW;gBACX,MAAM;YACR,GAAG;QACL;QACA,cAAc;YACZ,QAAQ;YACR,SAAS;YACT,SAAS,CAAC,GAAG,QAAQ,CAAC,EAAE,OAAO,MAAM,CAAC,GAAG,QAAQ,CAAC,EAAE,KAAK,MAAM,yCAAyC,CAAC,GAAG,QAAQ,CAAC,EAAE,KAAK,MAAM;YAClI,QAAQ,CAAC,GAAG,QAAQ,CAAC,EAAE,KAAK;gBAC1B,WAAW;gBACX,MAAM;YACR,GAAG;QACL;IACF;IACA,MAAM,EACJ,MAAM,EACN,OAAO,EACP,OAAO,EACP,MAAM,EACP,GAAG,CAAC,wBAAwB,MAAM,CAAC,MAAM,WAAW,GAAG,MAAM,QAAQ,0BAA0B,KAAK,IAAI,wBAAwB,OAAO,OAAO;IAC/I,OAAO;QACL;QACA,MAAM,CAAC,GAAG,QAAQ,CAAC,EAAE,OAAO;YAC1B,WAAW;QACb,GAAG,CAAC,UAAU,QAAQ,UAAU,KAAK,IAAI,KAAK,IAAI,MAAM,UAAU,KAAK,CAAC,GAAG,QAAQ,CAAC,EAAE,SAAS;YAC7F,yBAAyB;gBACvB,QAAQ,CAAC;;yBAEQ,EAAE,UAAU,QAAQ,UAAU,KAAK,IAAI,KAAK,IAAI,MAAM,UAAU,CAAC;;MAEpF,CAAC;YACD;QACF,IAAI,CAAC,GAAG,QAAQ,CAAC,EAAE,OAAO;YACxB,WAAW;QACb,GAAG,CAAC,UAAU,QAAQ,UAAU,KAAK,IAAI,KAAK,IAAI,MAAM,IAAI,KAAK,CAAC,GAAG,QAAQ,CAAC,EAAE,OAAO;YACrF,KAAK,MAAM,IAAI;YACf,KAAK;YACL,WAAW;QACb,IAAI,CAAC,GAAG,QAAQ,CAAC,EAAE,MAAM,MAAM,UAAU,CAAC,GAAG,QAAQ,CAAC,EAAE,OAAO;YAC7D,WAAW;QACb,GAAG,UAAU;IACf;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2562, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/node_modules/next-auth/css/index.js"], "sourcesContent": ["module.exports = function() { return \":root{--border-width:1px;--border-radius:0.5rem;--color-error:#c94b4b;--color-info:#157efb;--color-info-hover:#0f6ddb;--color-info-text:#fff}.__next-auth-theme-auto,.__next-auth-theme-light{--color-background:#ececec;--color-background-hover:hsla(0,0%,93%,.8);--color-background-card:#fff;--color-text:#000;--color-primary:#444;--color-control-border:#bbb;--color-button-active-background:#f9f9f9;--color-button-active-border:#aaa;--color-separator:#ccc}.__next-auth-theme-dark{--color-background:#161b22;--color-background-hover:rgba(22,27,34,.8);--color-background-card:#0d1117;--color-text:#fff;--color-primary:#ccc;--color-control-border:#555;--color-button-active-background:#060606;--color-button-active-border:#666;--color-separator:#444}@media (prefers-color-scheme:dark){.__next-auth-theme-auto{--color-background:#161b22;--color-background-hover:rgba(22,27,34,.8);--color-background-card:#0d1117;--color-text:#fff;--color-primary:#ccc;--color-control-border:#555;--color-button-active-background:#060606;--color-button-active-border:#666;--color-separator:#444}a.button,button{background-color:var(--provider-dark-bg,var(--color-background));color:var(--provider-dark-color,var(--color-primary))}a.button:hover,button:hover{background-color:var(--provider-dark-bg-hover,var(--color-background-hover))!important}#provider-logo{display:none!important}#provider-logo-dark{display:block!important;width:25px}}html{box-sizing:border-box}*,:after,:before{box-sizing:inherit;margin:0;padding:0}body{background-color:var(--color-background);font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji;margin:0;padding:0}h1{font-weight:400}h1,p{color:var(--color-text);margin-bottom:1.5rem;padding:0 1rem}form{margin:0;padding:0}label{font-weight:500;margin-bottom:.25rem;text-align:left}input[type],label{color:var(--color-text);display:block}input[type]{background:var(--color-background-card);border:var(--border-width) solid var(--color-control-border);border-radius:var(--border-radius);box-sizing:border-box;font-size:1rem;padding:.5rem 1rem;width:100%}input[type]:focus{box-shadow:none}p{font-size:1.1rem;line-height:2rem}a.button{line-height:1rem;text-decoration:none}a.button:link,a.button:visited{background-color:var(--color-background);color:var(--color-primary)}button span{flex-grow:1}a.button,button{align-items:center;background-color:var(--provider-bg);border-color:rgba(0,0,0,.1);border-radius:var(--border-radius);color:var(--provider-color,var(--color-primary));display:flex;font-size:1.1rem;font-weight:500;justify-content:center;min-height:62px;padding:.75rem 1rem;position:relative;transition:all .1s ease-in-out}a.button:hover,button:hover{background-color:var(--provider-bg-hover,var(--color-background-hover));cursor:pointer}a.button:active,button:active{cursor:pointer}a.button #provider-logo,button #provider-logo{display:block;width:25px}a.button #provider-logo-dark,button #provider-logo-dark{display:none}#submitButton{background-color:var(--brand-color,var(--color-info));color:var(--button-text-color,var(--color-info-text));width:100%}#submitButton:hover{background-color:var(--button-hover-bg,var(--color-info-hover))!important}a.site{color:var(--color-primary);font-size:1rem;line-height:2rem;text-decoration:none}a.site:hover{text-decoration:underline}.page{box-sizing:border-box;display:grid;height:100%;margin:0;padding:0;place-items:center;position:absolute;width:100%}.page>div{text-align:center}.error a.button{margin-top:.5rem;padding-left:2rem;padding-right:2rem}.error .message{margin-bottom:1.5rem}.signin input[type=text]{display:block;margin-left:auto;margin-right:auto}.signin hr{border:0;border-top:1px solid var(--color-separator);display:block;margin:2rem auto 1rem;overflow:visible}.signin hr:before{background:var(--color-background-card);color:#888;content:\\\"or\\\";padding:0 .4rem;position:relative;top:-.7rem}.signin .error{background:#f5f5f5;background:var(--color-error);border-radius:.3rem;font-weight:500}.signin .error p{color:var(--color-info-text);font-size:.9rem;line-height:1.2rem;padding:.5rem 1rem;text-align:left}.signin form,.signin>div{display:block}.signin form input[type],.signin>div input[type]{margin-bottom:.5rem}.signin form button,.signin>div button{width:100%}.signin .provider+.provider{margin-top:1rem}.logo{display:inline-block;margin:1.25rem 0;max-height:70px;max-width:150px}.card{background-color:var(--color-background-card);border-radius:2rem;padding:1.25rem 2rem}.card .header{color:var(--color-primary)}.section-header{color:var(--color-text)}@media screen and (min-width:450px){.card{margin:2rem 0;width:368px}}@media screen and (max-width:450px){.card{margin:1rem 0;width:343px}}\" }"], "names": [], "mappings": "AAAA,OAAO,OAAO,GAAG;IAAa,OAAO;AAAssJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2570, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/node_modules/next-auth/core/pages/index.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = renderPage;\nvar _preactRenderToString = _interopRequireDefault(require(\"preact-render-to-string\"));\nvar _signin = _interopRequireDefault(require(\"./signin\"));\nvar _signout = _interopRequireDefault(require(\"./signout\"));\nvar _verifyRequest = _interopRequireDefault(require(\"./verify-request\"));\nvar _error = _interopRequireDefault(require(\"./error\"));\nvar _css = _interopRequireDefault(require(\"../../css\"));\nfunction renderPage(params) {\n  const {\n    url,\n    theme,\n    query,\n    cookies\n  } = params;\n  function send({\n    html,\n    title,\n    status\n  }) {\n    var _theme$colorScheme;\n    return {\n      cookies,\n      status,\n      headers: [{\n        key: \"Content-Type\",\n        value: \"text/html\"\n      }],\n      body: `<!DOCTYPE html><html lang=\"en\"><head><meta charset=\"UTF-8\"><meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\"><meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\"><style>${(0, _css.default)()}</style><title>${title}</title></head><body class=\"__next-auth-theme-${(_theme$colorScheme = theme === null || theme === void 0 ? void 0 : theme.colorScheme) !== null && _theme$colorScheme !== void 0 ? _theme$colorScheme : \"auto\"}\"><div class=\"page\">${(0, _preactRenderToString.default)(html)}</div></body></html>`\n    };\n  }\n  return {\n    signin(props) {\n      return send({\n        html: (0, _signin.default)({\n          csrfToken: params.csrfToken,\n          providers: params.providers,\n          callbackUrl: params.callbackUrl,\n          theme,\n          ...query,\n          ...props\n        }),\n        title: \"Sign In\"\n      });\n    },\n    signout(props) {\n      return send({\n        html: (0, _signout.default)({\n          csrfToken: params.csrfToken,\n          url,\n          theme,\n          ...props\n        }),\n        title: \"Sign Out\"\n      });\n    },\n    verifyRequest(props) {\n      return send({\n        html: (0, _verifyRequest.default)({\n          url,\n          theme,\n          ...props\n        }),\n        title: \"Verify Request\"\n      });\n    },\n    error(props) {\n      return send({\n        ...(0, _error.default)({\n          url,\n          theme,\n          ...props\n        }),\n        title: \"Error\"\n      });\n    }\n  };\n}"], "names": [], "mappings": "AAAA;AAEA,IAAI;AACJ,OAAO,cAAc,CAAC,SAAS,cAAc;IAC3C,OAAO;AACT;AACA,QAAQ,OAAO,GAAG;AAClB,IAAI,wBAAwB;AAC5B,IAAI,UAAU;AACd,IAAI,WAAW;AACf,IAAI,iBAAiB;AACrB,IAAI,SAAS;AACb,IAAI,OAAO;AACX,SAAS,WAAW,MAAM;IACxB,MAAM,EACJ,GAAG,EACH,KAAK,EACL,KAAK,EACL,OAAO,EACR,GAAG;IACJ,SAAS,KAAK,EACZ,IAAI,EACJ,KAAK,EACL,MAAM,EACP;QACC,IAAI;QACJ,OAAO;YACL;YACA;YACA,SAAS;gBAAC;oBACR,KAAK;oBACL,OAAO;gBACT;aAAE;YACF,MAAM,CAAC,6LAA6L,EAAE,CAAC,GAAG,KAAK,OAAO,IAAI,eAAe,EAAE,MAAM,8CAA8C,EAAE,CAAC,qBAAqB,UAAU,QAAQ,UAAU,KAAK,IAAI,KAAK,IAAI,MAAM,WAAW,MAAM,QAAQ,uBAAuB,KAAK,IAAI,qBAAqB,OAAO,oBAAoB,EAAE,CAAC,GAAG,sBAAsB,OAAO,EAAE,MAAM,oBAAoB,CAAC;QACthB;IACF;IACA,OAAO;QACL,QAAO,KAAK;YACV,OAAO,KAAK;gBACV,MAAM,CAAC,GAAG,QAAQ,OAAO,EAAE;oBACzB,WAAW,OAAO,SAAS;oBAC3B,WAAW,OAAO,SAAS;oBAC3B,aAAa,OAAO,WAAW;oBAC/B;oBACA,GAAG,KAAK;oBACR,GAAG,KAAK;gBACV;gBACA,OAAO;YACT;QACF;QACA,SAAQ,KAAK;YACX,OAAO,KAAK;gBACV,MAAM,CAAC,GAAG,SAAS,OAAO,EAAE;oBAC1B,WAAW,OAAO,SAAS;oBAC3B;oBACA;oBACA,GAAG,KAAK;gBACV;gBACA,OAAO;YACT;QACF;QACA,eAAc,KAAK;YACjB,OAAO,KAAK;gBACV,MAAM,CAAC,GAAG,eAAe,OAAO,EAAE;oBAChC;oBACA;oBACA,GAAG,KAAK;gBACV;gBACA,OAAO;YACT;QACF;QACA,OAAM,KAAK;YACT,OAAO,KAAK;gBACV,GAAG,CAAC,GAAG,OAAO,OAAO,EAAE;oBACrB;oBACA;oBACA,GAAG,KAAK;gBACV,EAAE;gBACF,OAAO;YACT;QACF;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2650, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/node_modules/next-auth/utils/merge.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.merge = merge;\nfunction isObject(item) {\n  return item && typeof item === \"object\" && !Array.isArray(item);\n}\nfunction merge(target, ...sources) {\n  if (!sources.length) return target;\n  const source = sources.shift();\n  if (isObject(target) && isObject(source)) {\n    for (const key in source) {\n      if (isObject(source[key])) {\n        if (!target[key]) Object.assign(target, {\n          [key]: {}\n        });\n        merge(target[key], source[key]);\n      } else {\n        Object.assign(target, {\n          [key]: source[key]\n        });\n      }\n    }\n  }\n  return merge(target, ...sources);\n}"], "names": [], "mappings": "AAAA;AAEA,OAAO,cAAc,CAAC,SAAS,cAAc;IAC3C,OAAO;AACT;AACA,QAAQ,KAAK,GAAG;AAChB,SAAS,SAAS,IAAI;IACpB,OAAO,QAAQ,OAAO,SAAS,YAAY,CAAC,MAAM,OAAO,CAAC;AAC5D;AACA,SAAS,MAAM,MAAM,EAAE,GAAG,OAAO;IAC/B,IAAI,CAAC,QAAQ,MAAM,EAAE,OAAO;IAC5B,MAAM,SAAS,QAAQ,KAAK;IAC5B,IAAI,SAAS,WAAW,SAAS,SAAS;QACxC,IAAK,MAAM,OAAO,OAAQ;YACxB,IAAI,SAAS,MAAM,CAAC,IAAI,GAAG;gBACzB,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,OAAO,MAAM,CAAC,QAAQ;oBACtC,CAAC,IAAI,EAAE,CAAC;gBACV;gBACA,MAAM,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,IAAI;YAChC,OAAO;gBACL,OAAO,MAAM,CAAC,QAAQ;oBACpB,CAAC,IAAI,EAAE,MAAM,CAAC,IAAI;gBACpB;YACF;QACF;IACF;IACA,OAAO,MAAM,WAAW;AAC1B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2682, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/node_modules/next-auth/core/lib/providers.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = parseProviders;\nvar _merge = require(\"../../utils/merge\");\nfunction parseProviders(params) {\n  const {\n    url,\n    providerId\n  } = params;\n  const providers = params.providers.map(({\n    options: userOptions,\n    ...rest\n  }) => {\n    var _ref;\n    if (rest.type === \"oauth\") {\n      var _normalizedUserOption;\n      const normalizedOptions = normalizeOAuthOptions(rest);\n      const normalizedUserOptions = normalizeOAuthOptions(userOptions, true);\n      const id = (_normalizedUserOption = normalizedUserOptions === null || normalizedUserOptions === void 0 ? void 0 : normalizedUserOptions.id) !== null && _normalizedUserOption !== void 0 ? _normalizedUserOption : rest.id;\n      return (0, _merge.merge)(normalizedOptions, {\n        ...normalizedUserOptions,\n        signinUrl: `${url}/signin/${id}`,\n        callbackUrl: `${url}/callback/${id}`\n      });\n    }\n    const id = (_ref = userOptions === null || userOptions === void 0 ? void 0 : userOptions.id) !== null && _ref !== void 0 ? _ref : rest.id;\n    return (0, _merge.merge)(rest, {\n      ...userOptions,\n      signinUrl: `${url}/signin/${id}`,\n      callbackUrl: `${url}/callback/${id}`\n    });\n  });\n  return {\n    providers,\n    provider: providers.find(({\n      id\n    }) => id === providerId)\n  };\n}\nfunction normalizeOAuthOptions(oauthOptions, isUserOptions = false) {\n  var _normalized$version;\n  if (!oauthOptions) return;\n  const normalized = Object.entries(oauthOptions).reduce((acc, [key, value]) => {\n    if ([\"authorization\", \"token\", \"userinfo\"].includes(key) && typeof value === \"string\") {\n      var _url$searchParams;\n      const url = new URL(value);\n      acc[key] = {\n        url: `${url.origin}${url.pathname}`,\n        params: Object.fromEntries((_url$searchParams = url.searchParams) !== null && _url$searchParams !== void 0 ? _url$searchParams : [])\n      };\n    } else {\n      acc[key] = value;\n    }\n    return acc;\n  }, {});\n  if (!isUserOptions && !((_normalized$version = normalized.version) !== null && _normalized$version !== void 0 && _normalized$version.startsWith(\"1.\"))) {\n    var _ref2, _normalized$idToken, _normalized$wellKnown, _normalized$authoriza;\n    normalized.idToken = Boolean((_ref2 = (_normalized$idToken = normalized.idToken) !== null && _normalized$idToken !== void 0 ? _normalized$idToken : (_normalized$wellKnown = normalized.wellKnown) === null || _normalized$wellKnown === void 0 ? void 0 : _normalized$wellKnown.includes(\"openid-configuration\")) !== null && _ref2 !== void 0 ? _ref2 : (_normalized$authoriza = normalized.authorization) === null || _normalized$authoriza === void 0 || (_normalized$authoriza = _normalized$authoriza.params) === null || _normalized$authoriza === void 0 || (_normalized$authoriza = _normalized$authoriza.scope) === null || _normalized$authoriza === void 0 ? void 0 : _normalized$authoriza.includes(\"openid\"));\n    if (!normalized.checks) normalized.checks = [\"state\"];\n  }\n  return normalized;\n}"], "names": [], "mappings": "AAAA;AAEA,OAAO,cAAc,CAAC,SAAS,cAAc;IAC3C,OAAO;AACT;AACA,QAAQ,OAAO,GAAG;AAClB,IAAI;AACJ,SAAS,eAAe,MAAM;IAC5B,MAAM,EACJ,GAAG,EACH,UAAU,EACX,GAAG;IACJ,MAAM,YAAY,OAAO,SAAS,CAAC,GAAG,CAAC,CAAC,EACtC,SAAS,WAAW,EACpB,GAAG,MACJ;QACC,IAAI;QACJ,IAAI,KAAK,IAAI,KAAK,SAAS;YACzB,IAAI;YACJ,MAAM,oBAAoB,sBAAsB;YAChD,MAAM,wBAAwB,sBAAsB,aAAa;YACjE,MAAM,KAAK,CAAC,wBAAwB,0BAA0B,QAAQ,0BAA0B,KAAK,IAAI,KAAK,IAAI,sBAAsB,EAAE,MAAM,QAAQ,0BAA0B,KAAK,IAAI,wBAAwB,KAAK,EAAE;YAC1N,OAAO,CAAC,GAAG,OAAO,KAAK,EAAE,mBAAmB;gBAC1C,GAAG,qBAAqB;gBACxB,WAAW,GAAG,IAAI,QAAQ,EAAE,IAAI;gBAChC,aAAa,GAAG,IAAI,UAAU,EAAE,IAAI;YACtC;QACF;QACA,MAAM,KAAK,CAAC,OAAO,gBAAgB,QAAQ,gBAAgB,KAAK,IAAI,KAAK,IAAI,YAAY,EAAE,MAAM,QAAQ,SAAS,KAAK,IAAI,OAAO,KAAK,EAAE;QACzI,OAAO,CAAC,GAAG,OAAO,KAAK,EAAE,MAAM;YAC7B,GAAG,WAAW;YACd,WAAW,GAAG,IAAI,QAAQ,EAAE,IAAI;YAChC,aAAa,GAAG,IAAI,UAAU,EAAE,IAAI;QACtC;IACF;IACA,OAAO;QACL;QACA,UAAU,UAAU,IAAI,CAAC,CAAC,EACxB,EAAE,EACH,GAAK,OAAO;IACf;AACF;AACA,SAAS,sBAAsB,YAAY,EAAE,gBAAgB,KAAK;IAChE,IAAI;IACJ,IAAI,CAAC,cAAc;IACnB,MAAM,aAAa,OAAO,OAAO,CAAC,cAAc,MAAM,CAAC,CAAC,KAAK,CAAC,KAAK,MAAM;QACvE,IAAI;YAAC;YAAiB;YAAS;SAAW,CAAC,QAAQ,CAAC,QAAQ,OAAO,UAAU,UAAU;YACrF,IAAI;YACJ,MAAM,MAAM,IAAI,IAAI;YACpB,GAAG,CAAC,IAAI,GAAG;gBACT,KAAK,GAAG,IAAI,MAAM,GAAG,IAAI,QAAQ,EAAE;gBACnC,QAAQ,OAAO,WAAW,CAAC,CAAC,oBAAoB,IAAI,YAAY,MAAM,QAAQ,sBAAsB,KAAK,IAAI,oBAAoB,EAAE;YACrI;QACF,OAAO;YACL,GAAG,CAAC,IAAI,GAAG;QACb;QACA,OAAO;IACT,GAAG,CAAC;IACJ,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,sBAAsB,WAAW,OAAO,MAAM,QAAQ,wBAAwB,KAAK,KAAK,oBAAoB,UAAU,CAAC,KAAK,GAAG;QACtJ,IAAI,OAAO,qBAAqB,uBAAuB;QACvD,WAAW,OAAO,GAAG,QAAQ,CAAC,QAAQ,CAAC,sBAAsB,WAAW,OAAO,MAAM,QAAQ,wBAAwB,KAAK,IAAI,sBAAsB,CAAC,wBAAwB,WAAW,SAAS,MAAM,QAAQ,0BAA0B,KAAK,IAAI,KAAK,IAAI,sBAAsB,QAAQ,CAAC,uBAAuB,MAAM,QAAQ,UAAU,KAAK,IAAI,QAAQ,CAAC,wBAAwB,WAAW,aAAa,MAAM,QAAQ,0BAA0B,KAAK,KAAK,CAAC,wBAAwB,sBAAsB,MAAM,MAAM,QAAQ,0BAA0B,KAAK,KAAK,CAAC,wBAAwB,sBAAsB,KAAK,MAAM,QAAQ,0BAA0B,KAAK,IAAI,KAAK,IAAI,sBAAsB,QAAQ,CAAC;QACjrB,IAAI,CAAC,WAAW,MAAM,EAAE,WAAW,MAAM,GAAG;YAAC;SAAQ;IACvD;IACA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2749, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/node_modules/next-auth/core/lib/default-callbacks.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.defaultCallbacks = void 0;\nconst defaultCallbacks = exports.defaultCallbacks = {\n  signIn() {\n    return true;\n  },\n  redirect({\n    url,\n    baseUrl\n  }) {\n    if (url.startsWith(\"/\")) return `${baseUrl}${url}`;else if (new URL(url).origin === baseUrl) return url;\n    return baseUrl;\n  },\n  session({\n    session\n  }) {\n    return session;\n  },\n  jwt({\n    token\n  }) {\n    return token;\n  }\n};"], "names": [], "mappings": "AAAA;AAEA,OAAO,cAAc,CAAC,SAAS,cAAc;IAC3C,OAAO;AACT;AACA,QAAQ,gBAAgB,GAAG,KAAK;AAChC,MAAM,mBAAmB,QAAQ,gBAAgB,GAAG;IAClD;QACE,OAAO;IACT;IACA,UAAS,EACP,GAAG,EACH,OAAO,EACR;QACC,IAAI,IAAI,UAAU,CAAC,MAAM,OAAO,GAAG,UAAU,KAAK;aAAM,IAAI,IAAI,IAAI,KAAK,MAAM,KAAK,SAAS,OAAO;QACpG,OAAO;IACT;IACA,SAAQ,EACN,OAAO,EACR;QACC,OAAO;IACT;IACA,KAAI,EACF,KAAK,EACN;QACC,OAAO;IACT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2775, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/node_modules/next-auth/core/lib/csrf-token.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.createCSRFToken = createCSRFToken;\nvar _crypto = require(\"crypto\");\nfunction createCSRFToken({\n  options,\n  cookieValue,\n  isPost,\n  bodyValue\n}) {\n  if (cookieValue) {\n    const [csrfToken, csrfTokenHash] = cookieValue.split(\"|\");\n    const expectedCsrfTokenHash = (0, _crypto.createHash)(\"sha256\").update(`${csrfToken}${options.secret}`).digest(\"hex\");\n    if (csrfTokenHash === expectedCsrfTokenHash) {\n      const csrfTokenVerified = isPost && csrfToken === bodyValue;\n      return {\n        csrfTokenVerified,\n        csrfToken\n      };\n    }\n  }\n  const csrfToken = (0, _crypto.randomBytes)(32).toString(\"hex\");\n  const csrfTokenHash = (0, _crypto.createHash)(\"sha256\").update(`${csrfToken}${options.secret}`).digest(\"hex\");\n  const cookie = `${csrfToken}|${csrfTokenHash}`;\n  return {\n    cookie,\n    csrfToken\n  };\n}"], "names": [], "mappings": "AAAA;AAEA,OAAO,cAAc,CAAC,SAAS,cAAc;IAC3C,OAAO;AACT;AACA,QAAQ,eAAe,GAAG;AAC1B,IAAI;AACJ,SAAS,gBAAgB,EACvB,OAAO,EACP,WAAW,EACX,MAAM,EACN,SAAS,EACV;IACC,IAAI,aAAa;QACf,MAAM,CAAC,WAAW,cAAc,GAAG,YAAY,KAAK,CAAC;QACrD,MAAM,wBAAwB,CAAC,GAAG,QAAQ,UAAU,EAAE,UAAU,MAAM,CAAC,GAAG,YAAY,QAAQ,MAAM,EAAE,EAAE,MAAM,CAAC;QAC/G,IAAI,kBAAkB,uBAAuB;YAC3C,MAAM,oBAAoB,UAAU,cAAc;YAClD,OAAO;gBACL;gBACA;YACF;QACF;IACF;IACA,MAAM,YAAY,CAAC,GAAG,QAAQ,WAAW,EAAE,IAAI,QAAQ,CAAC;IACxD,MAAM,gBAAgB,CAAC,GAAG,QAAQ,UAAU,EAAE,UAAU,MAAM,CAAC,GAAG,YAAY,QAAQ,MAAM,EAAE,EAAE,MAAM,CAAC;IACvG,MAAM,SAAS,GAAG,UAAU,CAAC,EAAE,eAAe;IAC9C,OAAO;QACL;QACA;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2806, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/node_modules/next-auth/core/lib/callback-url.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.createCallbackUrl = createCallbackUrl;\nasync function createCallbackUrl({\n  options,\n  paramValue,\n  cookieValue\n}) {\n  const {\n    url,\n    callbacks\n  } = options;\n  let callbackUrl = url.origin;\n  if (paramValue) {\n    callbackUrl = await callbacks.redirect({\n      url: paramValue,\n      baseUrl: url.origin\n    });\n  } else if (cookieValue) {\n    callbackUrl = await callbacks.redirect({\n      url: cookieValue,\n      baseUrl: url.origin\n    });\n  }\n  return {\n    callbackUrl,\n    callbackUrlCookie: callbackUrl !== cookieValue ? callbackUrl : undefined\n  };\n}"], "names": [], "mappings": "AAAA;AAEA,OAAO,cAAc,CAAC,SAAS,cAAc;IAC3C,OAAO;AACT;AACA,QAAQ,iBAAiB,GAAG;AAC5B,eAAe,kBAAkB,EAC/B,OAAO,EACP,UAAU,EACV,WAAW,EACZ;IACC,MAAM,EACJ,GAAG,EACH,SAAS,EACV,GAAG;IACJ,IAAI,cAAc,IAAI,MAAM;IAC5B,IAAI,YAAY;QACd,cAAc,MAAM,UAAU,QAAQ,CAAC;YACrC,KAAK;YACL,SAAS,IAAI,MAAM;QACrB;IACF,OAAO,IAAI,aAAa;QACtB,cAAc,MAAM,UAAU,QAAQ,CAAC;YACrC,KAAK;YACL,SAAS,IAAI,MAAM;QACrB;IACF;IACA,OAAO;QACL;QACA,mBAAmB,gBAAgB,cAAc,cAAc;IACjE;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2835, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/node_modules/next-auth/utils/parse-url.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = parseUrl;\nfunction parseUrl(url) {\n  var _url2;\n  const defaultUrl = new URL(\"http://localhost:3000/api/auth\");\n  if (url && !url.startsWith(\"http\")) {\n    url = `https://${url}`;\n  }\n  const _url = new URL((_url2 = url) !== null && _url2 !== void 0 ? _url2 : defaultUrl);\n  const path = (_url.pathname === \"/\" ? defaultUrl.pathname : _url.pathname).replace(/\\/$/, \"\");\n  const base = `${_url.origin}${path}`;\n  return {\n    origin: _url.origin,\n    host: _url.host,\n    path,\n    base,\n    toString: () => base\n  };\n}"], "names": [], "mappings": "AAAA;AAEA,OAAO,cAAc,CAAC,SAAS,cAAc;IAC3C,OAAO;AACT;AACA,QAAQ,OAAO,GAAG;AAClB,SAAS,SAAS,GAAG;IACnB,IAAI;IACJ,MAAM,aAAa,IAAI,IAAI;IAC3B,IAAI,OAAO,CAAC,IAAI,UAAU,CAAC,SAAS;QAClC,MAAM,CAAC,QAAQ,EAAE,KAAK;IACxB;IACA,MAAM,OAAO,IAAI,IAAI,CAAC,QAAQ,GAAG,MAAM,QAAQ,UAAU,KAAK,IAAI,QAAQ;IAC1E,MAAM,OAAO,CAAC,KAAK,QAAQ,KAAK,MAAM,WAAW,QAAQ,GAAG,KAAK,QAAQ,EAAE,OAAO,CAAC,OAAO;IAC1F,MAAM,OAAO,GAAG,KAAK,MAAM,GAAG,MAAM;IACpC,OAAO;QACL,QAAQ,KAAK,MAAM;QACnB,MAAM,KAAK,IAAI;QACf;QACA;QACA,UAAU,IAAM;IAClB;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2862, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/node_modules/next-auth/core/init.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.init = init;\nvar _crypto = require(\"crypto\");\nvar _logger = _interopRequireDefault(require(\"../utils/logger\"));\nvar _errors = require(\"./errors\");\nvar _providers = _interopRequireDefault(require(\"./lib/providers\"));\nvar _utils = require(\"./lib/utils\");\nvar cookie = _interopRequireWildcard(require(\"./lib/cookie\"));\nvar jwt = _interopRequireWildcard(require(\"../jwt\"));\nvar _defaultCallbacks = require(\"./lib/default-callbacks\");\nvar _csrfToken = require(\"./lib/csrf-token\");\nvar _callbackUrl = require(\"./lib/callback-url\");\nvar _parseUrl = _interopRequireDefault(require(\"../utils/parse-url\"));\nfunction _getRequireWildcardCache(e) { if (\"function\" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function (e) { return e ? t : r; })(e); }\nfunction _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if (\"default\" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }\nasync function init({\n  authOptions,\n  providerId,\n  action,\n  origin,\n  cookies: reqCookies,\n  callbackUrl: reqCallbackUrl,\n  csrfToken: reqCsrfToken,\n  isPost\n}) {\n  var _authOptions$useSecur, _authOptions$events;\n  const url = (0, _parseUrl.default)(origin);\n  const secret = (0, _utils.createSecret)({\n    authOptions,\n    url\n  });\n  const {\n    providers,\n    provider\n  } = (0, _providers.default)({\n    providers: authOptions.providers,\n    url,\n    providerId\n  });\n  const maxAge = 30 * 24 * 60 * 60;\n  const options = {\n    debug: false,\n    pages: {},\n    theme: {\n      colorScheme: \"auto\",\n      logo: \"\",\n      brandColor: \"\",\n      buttonText: \"\"\n    },\n    ...authOptions,\n    url,\n    action,\n    provider,\n    cookies: {\n      ...cookie.defaultCookies((_authOptions$useSecur = authOptions.useSecureCookies) !== null && _authOptions$useSecur !== void 0 ? _authOptions$useSecur : url.base.startsWith(\"https://\")),\n      ...authOptions.cookies\n    },\n    secret,\n    providers,\n    session: {\n      strategy: authOptions.adapter ? \"database\" : \"jwt\",\n      maxAge,\n      updateAge: 24 * 60 * 60,\n      generateSessionToken: () => {\n        var _randomUUID;\n        return (_randomUUID = _crypto.randomUUID === null || _crypto.randomUUID === void 0 ? void 0 : (0, _crypto.randomUUID)()) !== null && _randomUUID !== void 0 ? _randomUUID : (0, _crypto.randomBytes)(32).toString(\"hex\");\n      },\n      ...authOptions.session\n    },\n    jwt: {\n      secret,\n      maxAge,\n      encode: jwt.encode,\n      decode: jwt.decode,\n      ...authOptions.jwt\n    },\n    events: (0, _errors.eventsErrorHandler)((_authOptions$events = authOptions.events) !== null && _authOptions$events !== void 0 ? _authOptions$events : {}, _logger.default),\n    adapter: (0, _errors.adapterErrorHandler)(authOptions.adapter, _logger.default),\n    callbacks: {\n      ..._defaultCallbacks.defaultCallbacks,\n      ...authOptions.callbacks\n    },\n    logger: _logger.default,\n    callbackUrl: url.origin\n  };\n  const cookies = [];\n  const {\n    csrfToken,\n    cookie: csrfCookie,\n    csrfTokenVerified\n  } = (0, _csrfToken.createCSRFToken)({\n    options,\n    cookieValue: reqCookies === null || reqCookies === void 0 ? void 0 : reqCookies[options.cookies.csrfToken.name],\n    isPost,\n    bodyValue: reqCsrfToken\n  });\n  options.csrfToken = csrfToken;\n  options.csrfTokenVerified = csrfTokenVerified;\n  if (csrfCookie) {\n    cookies.push({\n      name: options.cookies.csrfToken.name,\n      value: csrfCookie,\n      options: options.cookies.csrfToken.options\n    });\n  }\n  const {\n    callbackUrl,\n    callbackUrlCookie\n  } = await (0, _callbackUrl.createCallbackUrl)({\n    options,\n    cookieValue: reqCookies === null || reqCookies === void 0 ? void 0 : reqCookies[options.cookies.callbackUrl.name],\n    paramValue: reqCallbackUrl\n  });\n  options.callbackUrl = callbackUrl;\n  if (callbackUrlCookie) {\n    cookies.push({\n      name: options.cookies.callbackUrl.name,\n      value: callbackUrlCookie,\n      options: options.cookies.callbackUrl.options\n    });\n  }\n  return {\n    options,\n    cookies\n  };\n}"], "names": [], "mappings": "AAAA;AAEA,IAAI;AACJ,OAAO,cAAc,CAAC,SAAS,cAAc;IAC3C,OAAO;AACT;AACA,QAAQ,IAAI,GAAG;AACf,IAAI;AACJ,IAAI,UAAU;AACd,IAAI;AACJ,IAAI,aAAa;AACjB,IAAI;AACJ,IAAI,SAAS;AACb,IAAI,MAAM;AACV,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI,YAAY;AAChB,SAAS,yBAAyB,CAAC;IAAI,IAAI,cAAc,OAAO,SAAS,OAAO;IAAM,IAAI,IAAI,IAAI,WAAW,IAAI,IAAI;IAAW,OAAO,CAAC,2BAA2B,SAAU,CAAC;QAAI,OAAO,IAAI,IAAI;IAAG,CAAC,EAAE;AAAI;AAC3M,SAAS,wBAAwB,CAAC,EAAE,CAAC;IAAI,IAAI,CAAC,KAAK,KAAK,EAAE,UAAU,EAAE,OAAO;IAAG,IAAI,SAAS,KAAK,YAAY,OAAO,KAAK,cAAc,OAAO,GAAG,OAAO;QAAE,SAAS;IAAE;IAAG,IAAI,IAAI,yBAAyB;IAAI,IAAI,KAAK,EAAE,GAAG,CAAC,IAAI,OAAO,EAAE,GAAG,CAAC;IAAI,IAAI,IAAI;QAAE,WAAW;IAAK,GAAG,IAAI,OAAO,cAAc,IAAI,OAAO,wBAAwB;IAAE,IAAK,IAAI,KAAK,EAAG,IAAI,cAAc,KAAK,CAAA,CAAC,CAAA,EAAE,cAAc,CAAC,IAAI,CAAC,GAAG,IAAI;QAAE,IAAI,IAAI,IAAI,OAAO,wBAAwB,CAAC,GAAG,KAAK;QAAM,KAAK,CAAC,EAAE,GAAG,IAAI,EAAE,GAAG,IAAI,OAAO,cAAc,CAAC,GAAG,GAAG,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;IAAE;IAAE,OAAO,EAAE,OAAO,GAAG,GAAG,KAAK,EAAE,GAAG,CAAC,GAAG,IAAI;AAAG;AAClkB,eAAe,KAAK,EAClB,WAAW,EACX,UAAU,EACV,MAAM,EACN,MAAM,EACN,SAAS,UAAU,EACnB,aAAa,cAAc,EAC3B,WAAW,YAAY,EACvB,MAAM,EACP;IACC,IAAI,uBAAuB;IAC3B,MAAM,MAAM,CAAC,GAAG,UAAU,OAAO,EAAE;IACnC,MAAM,SAAS,CAAC,GAAG,OAAO,YAAY,EAAE;QACtC;QACA;IACF;IACA,MAAM,EACJ,SAAS,EACT,QAAQ,EACT,GAAG,CAAC,GAAG,WAAW,OAAO,EAAE;QAC1B,WAAW,YAAY,SAAS;QAChC;QACA;IACF;IACA,MAAM,SAAS,KAAK,KAAK,KAAK;IAC9B,MAAM,UAAU;QACd,OAAO;QACP,OAAO,CAAC;QACR,OAAO;YACL,aAAa;YACb,MAAM;YACN,YAAY;YACZ,YAAY;QACd;QACA,GAAG,WAAW;QACd;QACA;QACA;QACA,SAAS;YACP,GAAG,OAAO,cAAc,CAAC,CAAC,wBAAwB,YAAY,gBAAgB,MAAM,QAAQ,0BAA0B,KAAK,IAAI,wBAAwB,IAAI,IAAI,CAAC,UAAU,CAAC,YAAY;YACvL,GAAG,YAAY,OAAO;QACxB;QACA;QACA;QACA,SAAS;YACP,UAAU,YAAY,OAAO,GAAG,aAAa;YAC7C;YACA,WAAW,KAAK,KAAK;YACrB,sBAAsB;gBACpB,IAAI;gBACJ,OAAO,CAAC,cAAc,QAAQ,UAAU,KAAK,QAAQ,QAAQ,UAAU,KAAK,KAAK,IAAI,KAAK,IAAI,CAAC,GAAG,QAAQ,UAAU,GAAG,MAAM,QAAQ,gBAAgB,KAAK,IAAI,cAAc,CAAC,GAAG,QAAQ,WAAW,EAAE,IAAI,QAAQ,CAAC;YACpN;YACA,GAAG,YAAY,OAAO;QACxB;QACA,KAAK;YACH;YACA;YACA,QAAQ,IAAI,MAAM;YAClB,QAAQ,IAAI,MAAM;YAClB,GAAG,YAAY,GAAG;QACpB;QACA,QAAQ,CAAC,GAAG,QAAQ,kBAAkB,EAAE,CAAC,sBAAsB,YAAY,MAAM,MAAM,QAAQ,wBAAwB,KAAK,IAAI,sBAAsB,CAAC,GAAG,QAAQ,OAAO;QACzK,SAAS,CAAC,GAAG,QAAQ,mBAAmB,EAAE,YAAY,OAAO,EAAE,QAAQ,OAAO;QAC9E,WAAW;YACT,GAAG,kBAAkB,gBAAgB;YACrC,GAAG,YAAY,SAAS;QAC1B;QACA,QAAQ,QAAQ,OAAO;QACvB,aAAa,IAAI,MAAM;IACzB;IACA,MAAM,UAAU,EAAE;IAClB,MAAM,EACJ,SAAS,EACT,QAAQ,UAAU,EAClB,iBAAiB,EAClB,GAAG,CAAC,GAAG,WAAW,eAAe,EAAE;QAClC;QACA,aAAa,eAAe,QAAQ,eAAe,KAAK,IAAI,KAAK,IAAI,UAAU,CAAC,QAAQ,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC;QAC/G;QACA,WAAW;IACb;IACA,QAAQ,SAAS,GAAG;IACpB,QAAQ,iBAAiB,GAAG;IAC5B,IAAI,YAAY;QACd,QAAQ,IAAI,CAAC;YACX,MAAM,QAAQ,OAAO,CAAC,SAAS,CAAC,IAAI;YACpC,OAAO;YACP,SAAS,QAAQ,OAAO,CAAC,SAAS,CAAC,OAAO;QAC5C;IACF;IACA,MAAM,EACJ,WAAW,EACX,iBAAiB,EAClB,GAAG,MAAM,CAAC,GAAG,aAAa,iBAAiB,EAAE;QAC5C;QACA,aAAa,eAAe,QAAQ,eAAe,KAAK,IAAI,KAAK,IAAI,UAAU,CAAC,QAAQ,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC;QACjH,YAAY;IACd;IACA,QAAQ,WAAW,GAAG;IACtB,IAAI,mBAAmB;QACrB,QAAQ,IAAI,CAAC;YACX,MAAM,QAAQ,OAAO,CAAC,WAAW,CAAC,IAAI;YACtC,OAAO;YACP,SAAS,QAAQ,OAAO,CAAC,WAAW,CAAC,OAAO;QAC9C;IACF;IACA,OAAO;QACL;QACA;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2999, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/node_modules/next-auth/core/lib/assert.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.assertConfig = assertConfig;\nvar _errors = require(\"../errors\");\nvar _parseUrl = _interopRequireDefault(require(\"../../utils/parse-url\"));\nvar _cookie = require(\"./cookie\");\nlet warned = false;\nfunction isValidHttpUrl(url, baseUrl) {\n  try {\n    return /^https?:/.test(new URL(url, url.startsWith(\"/\") ? baseUrl : undefined).protocol);\n  } catch (_unused) {\n    return false;\n  }\n}\nfunction assertConfig(params) {\n  var _req$query, _req$query2, _options$useSecureCoo, _req$cookies, _options$cookies$call, _options$cookies;\n  const {\n    options,\n    req\n  } = params;\n  const warnings = [];\n  if (!warned) {\n    if (!req.origin) warnings.push(\"NEXTAUTH_URL\");\n    if (!options.secret && process.env.NODE_ENV !== \"production\") warnings.push(\"NO_SECRET\");\n    if (options.debug) warnings.push(\"DEBUG_ENABLED\");\n  }\n  if (!options.secret && process.env.NODE_ENV === \"production\") {\n    return new _errors.MissingSecret(\"Please define a `secret` in production.\");\n  }\n  if (!((_req$query = req.query) !== null && _req$query !== void 0 && _req$query.nextauth) && !req.action) {\n    return new _errors.MissingAPIRoute(\"Cannot find [...nextauth].{js,ts} in `/pages/api/auth`. Make sure the filename is written correctly.\");\n  }\n  const callbackUrlParam = (_req$query2 = req.query) === null || _req$query2 === void 0 ? void 0 : _req$query2.callbackUrl;\n  const url = (0, _parseUrl.default)(req.origin);\n  if (callbackUrlParam && !isValidHttpUrl(callbackUrlParam, url.base)) {\n    return new _errors.InvalidCallbackUrl(`Invalid callback URL. Received: ${callbackUrlParam}`);\n  }\n  const {\n    callbackUrl: defaultCallbackUrl\n  } = (0, _cookie.defaultCookies)((_options$useSecureCoo = options.useSecureCookies) !== null && _options$useSecureCoo !== void 0 ? _options$useSecureCoo : url.base.startsWith(\"https://\"));\n  const callbackUrlCookie = (_req$cookies = req.cookies) === null || _req$cookies === void 0 ? void 0 : _req$cookies[(_options$cookies$call = (_options$cookies = options.cookies) === null || _options$cookies === void 0 || (_options$cookies = _options$cookies.callbackUrl) === null || _options$cookies === void 0 ? void 0 : _options$cookies.name) !== null && _options$cookies$call !== void 0 ? _options$cookies$call : defaultCallbackUrl.name];\n  if (callbackUrlCookie && !isValidHttpUrl(callbackUrlCookie, url.base)) {\n    return new _errors.InvalidCallbackUrl(`Invalid callback URL. Received: ${callbackUrlCookie}`);\n  }\n  let hasCredentials, hasEmail;\n  let hasTwitterOAuth2;\n  for (const provider of options.providers) {\n    if (provider.type === \"credentials\") hasCredentials = true;else if (provider.type === \"email\") hasEmail = true;else if (provider.id === \"twitter\" && provider.version === \"2.0\") hasTwitterOAuth2 = true;\n  }\n  if (hasCredentials) {\n    var _options$session;\n    const dbStrategy = ((_options$session = options.session) === null || _options$session === void 0 ? void 0 : _options$session.strategy) === \"database\";\n    const onlyCredentials = !options.providers.some(p => p.type !== \"credentials\");\n    if (dbStrategy && onlyCredentials) {\n      return new _errors.UnsupportedStrategy(\"Signin in with credentials only supported if JWT strategy is enabled\");\n    }\n    const credentialsNoAuthorize = options.providers.some(p => p.type === \"credentials\" && !p.authorize);\n    if (credentialsNoAuthorize) {\n      return new _errors.MissingAuthorize(\"Must define an authorize() handler to use credentials authentication provider\");\n    }\n  }\n  if (hasEmail) {\n    const {\n      adapter\n    } = options;\n    if (!adapter) {\n      return new _errors.MissingAdapter(\"E-mail login requires an adapter.\");\n    }\n    const missingMethods = [\"createVerificationToken\", \"useVerificationToken\", \"getUserByEmail\"].filter(method => !adapter[method]);\n    if (missingMethods.length) {\n      return new _errors.MissingAdapterMethods(`Required adapter methods were missing: ${missingMethods.join(\", \")}`);\n    }\n  }\n  if (!warned) {\n    if (hasTwitterOAuth2) warnings.push(\"TWITTER_OAUTH_2_BETA\");\n    warned = true;\n  }\n  return warnings;\n}"], "names": [], "mappings": "AAAA;AAEA,IAAI;AACJ,OAAO,cAAc,CAAC,SAAS,cAAc;IAC3C,OAAO;AACT;AACA,QAAQ,YAAY,GAAG;AACvB,IAAI;AACJ,IAAI,YAAY;AAChB,IAAI;AACJ,IAAI,SAAS;AACb,SAAS,eAAe,GAAG,EAAE,OAAO;IAClC,IAAI;QACF,OAAO,WAAW,IAAI,CAAC,IAAI,IAAI,KAAK,IAAI,UAAU,CAAC,OAAO,UAAU,WAAW,QAAQ;IACzF,EAAE,OAAO,SAAS;QAChB,OAAO;IACT;AACF;AACA,SAAS,aAAa,MAAM;IAC1B,IAAI,YAAY,aAAa,uBAAuB,cAAc,uBAAuB;IACzF,MAAM,EACJ,OAAO,EACP,GAAG,EACJ,GAAG;IACJ,MAAM,WAAW,EAAE;IACnB,IAAI,CAAC,QAAQ;QACX,IAAI,CAAC,IAAI,MAAM,EAAE,SAAS,IAAI,CAAC;QAC/B,IAAI,CAAC,QAAQ,MAAM,IAAI,oDAAyB,cAAc,SAAS,IAAI,CAAC;QAC5E,IAAI,QAAQ,KAAK,EAAE,SAAS,IAAI,CAAC;IACnC;IACA,uCAA8D;;IAE9D;IACA,IAAI,CAAC,CAAC,CAAC,aAAa,IAAI,KAAK,MAAM,QAAQ,eAAe,KAAK,KAAK,WAAW,QAAQ,KAAK,CAAC,IAAI,MAAM,EAAE;QACvG,OAAO,IAAI,QAAQ,eAAe,CAAC;IACrC;IACA,MAAM,mBAAmB,CAAC,cAAc,IAAI,KAAK,MAAM,QAAQ,gBAAgB,KAAK,IAAI,KAAK,IAAI,YAAY,WAAW;IACxH,MAAM,MAAM,CAAC,GAAG,UAAU,OAAO,EAAE,IAAI,MAAM;IAC7C,IAAI,oBAAoB,CAAC,eAAe,kBAAkB,IAAI,IAAI,GAAG;QACnE,OAAO,IAAI,QAAQ,kBAAkB,CAAC,CAAC,gCAAgC,EAAE,kBAAkB;IAC7F;IACA,MAAM,EACJ,aAAa,kBAAkB,EAChC,GAAG,CAAC,GAAG,QAAQ,cAAc,EAAE,CAAC,wBAAwB,QAAQ,gBAAgB,MAAM,QAAQ,0BAA0B,KAAK,IAAI,wBAAwB,IAAI,IAAI,CAAC,UAAU,CAAC;IAC9K,MAAM,oBAAoB,CAAC,eAAe,IAAI,OAAO,MAAM,QAAQ,iBAAiB,KAAK,IAAI,KAAK,IAAI,YAAY,CAAC,CAAC,wBAAwB,CAAC,mBAAmB,QAAQ,OAAO,MAAM,QAAQ,qBAAqB,KAAK,KAAK,CAAC,mBAAmB,iBAAiB,WAAW,MAAM,QAAQ,qBAAqB,KAAK,IAAI,KAAK,IAAI,iBAAiB,IAAI,MAAM,QAAQ,0BAA0B,KAAK,IAAI,wBAAwB,mBAAmB,IAAI,CAAC;IACvb,IAAI,qBAAqB,CAAC,eAAe,mBAAmB,IAAI,IAAI,GAAG;QACrE,OAAO,IAAI,QAAQ,kBAAkB,CAAC,CAAC,gCAAgC,EAAE,mBAAmB;IAC9F;IACA,IAAI,gBAAgB;IACpB,IAAI;IACJ,KAAK,MAAM,YAAY,QAAQ,SAAS,CAAE;QACxC,IAAI,SAAS,IAAI,KAAK,eAAe,iBAAiB;aAAU,IAAI,SAAS,IAAI,KAAK,SAAS,WAAW;aAAU,IAAI,SAAS,EAAE,KAAK,aAAa,SAAS,OAAO,KAAK,OAAO,mBAAmB;IACtM;IACA,IAAI,gBAAgB;QAClB,IAAI;QACJ,MAAM,aAAa,CAAC,CAAC,mBAAmB,QAAQ,OAAO,MAAM,QAAQ,qBAAqB,KAAK,IAAI,KAAK,IAAI,iBAAiB,QAAQ,MAAM;QAC3I,MAAM,kBAAkB,CAAC,QAAQ,SAAS,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK;QAChE,IAAI,cAAc,iBAAiB;YACjC,OAAO,IAAI,QAAQ,mBAAmB,CAAC;QACzC;QACA,MAAM,yBAAyB,QAAQ,SAAS,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,iBAAiB,CAAC,EAAE,SAAS;QACnG,IAAI,wBAAwB;YAC1B,OAAO,IAAI,QAAQ,gBAAgB,CAAC;QACtC;IACF;IACA,IAAI,UAAU;QACZ,MAAM,EACJ,OAAO,EACR,GAAG;QACJ,IAAI,CAAC,SAAS;YACZ,OAAO,IAAI,QAAQ,cAAc,CAAC;QACpC;QACA,MAAM,iBAAiB;YAAC;YAA2B;YAAwB;SAAiB,CAAC,MAAM,CAAC,CAAA,SAAU,CAAC,OAAO,CAAC,OAAO;QAC9H,IAAI,eAAe,MAAM,EAAE;YACzB,OAAO,IAAI,QAAQ,qBAAqB,CAAC,CAAC,uCAAuC,EAAE,eAAe,IAAI,CAAC,OAAO;QAChH;IACF;IACA,IAAI,CAAC,QAAQ;QACX,IAAI,kBAAkB,SAAS,IAAI,CAAC;QACpC,SAAS;IACX;IACA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3085, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/node_modules/next-auth/core/index.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.AuthHandler = AuthHandler;\nvar _logger = _interopRequireWildcard(require(\"../utils/logger\"));\nvar _detectOrigin = require(\"../utils/detect-origin\");\nvar routes = _interopRequireWildcard(require(\"./routes\"));\nvar _pages = _interopRequireDefault(require(\"./pages\"));\nvar _init = require(\"./init\");\nvar _assert = require(\"./lib/assert\");\nvar _cookie = require(\"./lib/cookie\");\nvar _cookie2 = require(\"cookie\");\nfunction _getRequireWildcardCache(e) { if (\"function\" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function (e) { return e ? t : r; })(e); }\nfunction _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if (\"default\" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }\nasync function getBody(req) {\n  try {\n    return await req.json();\n  } catch (_unused) {}\n}\nasync function toInternalRequest(req) {\n  var _headers$xForwarded2;\n  if (req instanceof Request) {\n    var _req$headers$get, _url$searchParams$get, _headers$xForwarded;\n    const url = new URL(req.url);\n    const nextauth = url.pathname.split(\"/\").slice(3);\n    const headers = Object.fromEntries(req.headers);\n    const query = Object.fromEntries(url.searchParams);\n    query.nextauth = nextauth;\n    return {\n      action: nextauth[0],\n      method: req.method,\n      headers,\n      body: await getBody(req),\n      cookies: (0, _cookie2.parse)((_req$headers$get = req.headers.get(\"cookie\")) !== null && _req$headers$get !== void 0 ? _req$headers$get : \"\"),\n      providerId: nextauth[1],\n      error: (_url$searchParams$get = url.searchParams.get(\"error\")) !== null && _url$searchParams$get !== void 0 ? _url$searchParams$get : nextauth[1],\n      origin: (0, _detectOrigin.detectOrigin)((_headers$xForwarded = headers[\"x-forwarded-host\"]) !== null && _headers$xForwarded !== void 0 ? _headers$xForwarded : headers.host, headers[\"x-forwarded-proto\"]),\n      query\n    };\n  }\n  const {\n    headers\n  } = req;\n  const host = (_headers$xForwarded2 = headers === null || headers === void 0 ? void 0 : headers[\"x-forwarded-host\"]) !== null && _headers$xForwarded2 !== void 0 ? _headers$xForwarded2 : headers === null || headers === void 0 ? void 0 : headers.host;\n  req.origin = (0, _detectOrigin.detectOrigin)(host, headers === null || headers === void 0 ? void 0 : headers[\"x-forwarded-proto\"]);\n  return req;\n}\nasync function AuthHandler(params) {\n  var _req$body$callbackUrl, _req$body, _req$query2, _req$body2;\n  const {\n    options: authOptions,\n    req: incomingRequest\n  } = params;\n  const req = await toInternalRequest(incomingRequest);\n  (0, _logger.setLogger)(authOptions.logger, authOptions.debug);\n  const assertionResult = (0, _assert.assertConfig)({\n    options: authOptions,\n    req\n  });\n  if (Array.isArray(assertionResult)) {\n    assertionResult.forEach(_logger.default.warn);\n  } else if (assertionResult instanceof Error) {\n    var _req$query;\n    _logger.default.error(assertionResult.code, assertionResult);\n    const htmlPages = [\"signin\", \"signout\", \"error\", \"verify-request\"];\n    if (!htmlPages.includes(req.action) || req.method !== \"GET\") {\n      const message = `There is a problem with the server configuration. Check the server logs for more information.`;\n      return {\n        status: 500,\n        headers: [{\n          key: \"Content-Type\",\n          value: \"application/json\"\n        }],\n        body: {\n          message\n        }\n      };\n    }\n    const {\n      pages,\n      theme\n    } = authOptions;\n    const authOnErrorPage = (pages === null || pages === void 0 ? void 0 : pages.error) && ((_req$query = req.query) === null || _req$query === void 0 || (_req$query = _req$query.callbackUrl) === null || _req$query === void 0 ? void 0 : _req$query.startsWith(pages.error));\n    if (!(pages !== null && pages !== void 0 && pages.error) || authOnErrorPage) {\n      if (authOnErrorPage) {\n        _logger.default.error(\"AUTH_ON_ERROR_PAGE_ERROR\", new Error(`The error page ${pages === null || pages === void 0 ? void 0 : pages.error} should not require authentication`));\n      }\n      const render = (0, _pages.default)({\n        theme\n      });\n      return render.error({\n        error: \"configuration\"\n      });\n    }\n    return {\n      redirect: `${pages.error}?error=Configuration`\n    };\n  }\n  const {\n    action,\n    providerId,\n    error,\n    method = \"GET\"\n  } = req;\n  const {\n    options,\n    cookies\n  } = await (0, _init.init)({\n    authOptions,\n    action,\n    providerId,\n    origin: req.origin,\n    callbackUrl: (_req$body$callbackUrl = (_req$body = req.body) === null || _req$body === void 0 ? void 0 : _req$body.callbackUrl) !== null && _req$body$callbackUrl !== void 0 ? _req$body$callbackUrl : (_req$query2 = req.query) === null || _req$query2 === void 0 ? void 0 : _req$query2.callbackUrl,\n    csrfToken: (_req$body2 = req.body) === null || _req$body2 === void 0 ? void 0 : _req$body2.csrfToken,\n    cookies: req.cookies,\n    isPost: method === \"POST\"\n  });\n  const sessionStore = new _cookie.SessionStore(options.cookies.sessionToken, req, options.logger);\n  if (method === \"GET\") {\n    const render = (0, _pages.default)({\n      ...options,\n      query: req.query,\n      cookies\n    });\n    const {\n      pages\n    } = options;\n    switch (action) {\n      case \"providers\":\n        return await routes.providers(options.providers);\n      case \"session\":\n        {\n          const session = await routes.session({\n            options,\n            sessionStore\n          });\n          if (session.cookies) cookies.push(...session.cookies);\n          return {\n            ...session,\n            cookies\n          };\n        }\n      case \"csrf\":\n        return {\n          headers: [{\n            key: \"Content-Type\",\n            value: \"application/json\"\n          }],\n          body: {\n            csrfToken: options.csrfToken\n          },\n          cookies\n        };\n      case \"signin\":\n        if (pages.signIn) {\n          let signinUrl = `${pages.signIn}${pages.signIn.includes(\"?\") ? \"&\" : \"?\"}callbackUrl=${encodeURIComponent(options.callbackUrl)}`;\n          if (error) signinUrl = `${signinUrl}&error=${encodeURIComponent(error)}`;\n          return {\n            redirect: signinUrl,\n            cookies\n          };\n        }\n        return render.signin();\n      case \"signout\":\n        if (pages.signOut) return {\n          redirect: pages.signOut,\n          cookies\n        };\n        return render.signout();\n      case \"callback\":\n        if (options.provider) {\n          const callback = await routes.callback({\n            body: req.body,\n            query: req.query,\n            headers: req.headers,\n            cookies: req.cookies,\n            method,\n            options,\n            sessionStore\n          });\n          if (callback.cookies) cookies.push(...callback.cookies);\n          return {\n            ...callback,\n            cookies\n          };\n        }\n        break;\n      case \"verify-request\":\n        if (pages.verifyRequest) {\n          return {\n            redirect: pages.verifyRequest,\n            cookies\n          };\n        }\n        return render.verifyRequest();\n      case \"error\":\n        if ([\"Signin\", \"OAuthSignin\", \"OAuthCallback\", \"OAuthCreateAccount\", \"EmailCreateAccount\", \"Callback\", \"OAuthAccountNotLinked\", \"EmailSignin\", \"CredentialsSignin\", \"SessionRequired\"].includes(error)) {\n          return {\n            redirect: `${options.url}/signin?error=${error}`,\n            cookies\n          };\n        }\n        if (pages.error) {\n          return {\n            redirect: `${pages.error}${pages.error.includes(\"?\") ? \"&\" : \"?\"}error=${error}`,\n            cookies\n          };\n        }\n        return render.error({\n          error: error\n        });\n      default:\n    }\n  } else if (method === \"POST\") {\n    switch (action) {\n      case \"signin\":\n        if (options.csrfTokenVerified && options.provider) {\n          const signin = await routes.signin({\n            query: req.query,\n            body: req.body,\n            options\n          });\n          if (signin.cookies) cookies.push(...signin.cookies);\n          return {\n            ...signin,\n            cookies\n          };\n        }\n        return {\n          redirect: `${options.url}/signin?csrf=true`,\n          cookies\n        };\n      case \"signout\":\n        if (options.csrfTokenVerified) {\n          const signout = await routes.signout({\n            options,\n            sessionStore\n          });\n          if (signout.cookies) cookies.push(...signout.cookies);\n          return {\n            ...signout,\n            cookies\n          };\n        }\n        return {\n          redirect: `${options.url}/signout?csrf=true`,\n          cookies\n        };\n      case \"callback\":\n        if (options.provider) {\n          if (options.provider.type === \"credentials\" && !options.csrfTokenVerified) {\n            return {\n              redirect: `${options.url}/signin?csrf=true`,\n              cookies\n            };\n          }\n          const callback = await routes.callback({\n            body: req.body,\n            query: req.query,\n            headers: req.headers,\n            cookies: req.cookies,\n            method,\n            options,\n            sessionStore\n          });\n          if (callback.cookies) cookies.push(...callback.cookies);\n          return {\n            ...callback,\n            cookies\n          };\n        }\n        break;\n      case \"_log\":\n        {\n          if (authOptions.logger) {\n            try {\n              var _req$body3;\n              const {\n                code,\n                level,\n                ...metadata\n              } = (_req$body3 = req.body) !== null && _req$body3 !== void 0 ? _req$body3 : {};\n              _logger.default[level](code, metadata);\n            } catch (error) {\n              _logger.default.error(\"LOGGER_ERROR\", error);\n            }\n          }\n          return {};\n        }\n      case \"session\":\n        {\n          if (options.csrfTokenVerified) {\n            var _req$body4;\n            const session = await routes.session({\n              options,\n              sessionStore,\n              newSession: (_req$body4 = req.body) === null || _req$body4 === void 0 ? void 0 : _req$body4.data,\n              isUpdate: true\n            });\n            if (session.cookies) cookies.push(...session.cookies);\n            return {\n              ...session,\n              cookies\n            };\n          }\n          return {\n            status: 400,\n            body: {},\n            cookies\n          };\n        }\n      default:\n    }\n  }\n  return {\n    status: 400,\n    body: `Error: This action with HTTP ${method} is not supported by NextAuth.js`\n  };\n}"], "names": [], "mappings": "AAAA;AAEA,IAAI;AACJ,OAAO,cAAc,CAAC,SAAS,cAAc;IAC3C,OAAO;AACT;AACA,QAAQ,WAAW,GAAG;AACtB,IAAI,UAAU;AACd,IAAI;AACJ,IAAI,SAAS;AACb,IAAI,SAAS;AACb,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,SAAS,yBAAyB,CAAC;IAAI,IAAI,cAAc,OAAO,SAAS,OAAO;IAAM,IAAI,IAAI,IAAI,WAAW,IAAI,IAAI;IAAW,OAAO,CAAC,2BAA2B,SAAU,CAAC;QAAI,OAAO,IAAI,IAAI;IAAG,CAAC,EAAE;AAAI;AAC3M,SAAS,wBAAwB,CAAC,EAAE,CAAC;IAAI,IAAI,CAAC,KAAK,KAAK,EAAE,UAAU,EAAE,OAAO;IAAG,IAAI,SAAS,KAAK,YAAY,OAAO,KAAK,cAAc,OAAO,GAAG,OAAO;QAAE,SAAS;IAAE;IAAG,IAAI,IAAI,yBAAyB;IAAI,IAAI,KAAK,EAAE,GAAG,CAAC,IAAI,OAAO,EAAE,GAAG,CAAC;IAAI,IAAI,IAAI;QAAE,WAAW;IAAK,GAAG,IAAI,OAAO,cAAc,IAAI,OAAO,wBAAwB;IAAE,IAAK,IAAI,KAAK,EAAG,IAAI,cAAc,KAAK,CAAA,CAAC,CAAA,EAAE,cAAc,CAAC,IAAI,CAAC,GAAG,IAAI;QAAE,IAAI,IAAI,IAAI,OAAO,wBAAwB,CAAC,GAAG,KAAK;QAAM,KAAK,CAAC,EAAE,GAAG,IAAI,EAAE,GAAG,IAAI,OAAO,cAAc,CAAC,GAAG,GAAG,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;IAAE;IAAE,OAAO,EAAE,OAAO,GAAG,GAAG,KAAK,EAAE,GAAG,CAAC,GAAG,IAAI;AAAG;AAClkB,eAAe,QAAQ,GAAG;IACxB,IAAI;QACF,OAAO,MAAM,IAAI,IAAI;IACvB,EAAE,OAAO,SAAS,CAAC;AACrB;AACA,eAAe,kBAAkB,GAAG;IAClC,IAAI;IACJ,IAAI,eAAe,SAAS;QAC1B,IAAI,kBAAkB,uBAAuB;QAC7C,MAAM,MAAM,IAAI,IAAI,IAAI,GAAG;QAC3B,MAAM,WAAW,IAAI,QAAQ,CAAC,KAAK,CAAC,KAAK,KAAK,CAAC;QAC/C,MAAM,UAAU,OAAO,WAAW,CAAC,IAAI,OAAO;QAC9C,MAAM,QAAQ,OAAO,WAAW,CAAC,IAAI,YAAY;QACjD,MAAM,QAAQ,GAAG;QACjB,OAAO;YACL,QAAQ,QAAQ,CAAC,EAAE;YACnB,QAAQ,IAAI,MAAM;YAClB;YACA,MAAM,MAAM,QAAQ;YACpB,SAAS,CAAC,GAAG,SAAS,KAAK,EAAE,CAAC,mBAAmB,IAAI,OAAO,CAAC,GAAG,CAAC,SAAS,MAAM,QAAQ,qBAAqB,KAAK,IAAI,mBAAmB;YACzI,YAAY,QAAQ,CAAC,EAAE;YACvB,OAAO,CAAC,wBAAwB,IAAI,YAAY,CAAC,GAAG,CAAC,QAAQ,MAAM,QAAQ,0BAA0B,KAAK,IAAI,wBAAwB,QAAQ,CAAC,EAAE;YACjJ,QAAQ,CAAC,GAAG,cAAc,YAAY,EAAE,CAAC,sBAAsB,OAAO,CAAC,mBAAmB,MAAM,QAAQ,wBAAwB,KAAK,IAAI,sBAAsB,QAAQ,IAAI,EAAE,OAAO,CAAC,oBAAoB;YACzM;QACF;IACF;IACA,MAAM,EACJ,OAAO,EACR,GAAG;IACJ,MAAM,OAAO,CAAC,uBAAuB,YAAY,QAAQ,YAAY,KAAK,IAAI,KAAK,IAAI,OAAO,CAAC,mBAAmB,MAAM,QAAQ,yBAAyB,KAAK,IAAI,uBAAuB,YAAY,QAAQ,YAAY,KAAK,IAAI,KAAK,IAAI,QAAQ,IAAI;IACvP,IAAI,MAAM,GAAG,CAAC,GAAG,cAAc,YAAY,EAAE,MAAM,YAAY,QAAQ,YAAY,KAAK,IAAI,KAAK,IAAI,OAAO,CAAC,oBAAoB;IACjI,OAAO;AACT;AACA,eAAe,YAAY,MAAM;IAC/B,IAAI,uBAAuB,WAAW,aAAa;IACnD,MAAM,EACJ,SAAS,WAAW,EACpB,KAAK,eAAe,EACrB,GAAG;IACJ,MAAM,MAAM,MAAM,kBAAkB;IACpC,CAAC,GAAG,QAAQ,SAAS,EAAE,YAAY,MAAM,EAAE,YAAY,KAAK;IAC5D,MAAM,kBAAkB,CAAC,GAAG,QAAQ,YAAY,EAAE;QAChD,SAAS;QACT;IACF;IACA,IAAI,MAAM,OAAO,CAAC,kBAAkB;QAClC,gBAAgB,OAAO,CAAC,QAAQ,OAAO,CAAC,IAAI;IAC9C,OAAO,IAAI,2BAA2B,OAAO;QAC3C,IAAI;QACJ,QAAQ,OAAO,CAAC,KAAK,CAAC,gBAAgB,IAAI,EAAE;QAC5C,MAAM,YAAY;YAAC;YAAU;YAAW;YAAS;SAAiB;QAClE,IAAI,CAAC,UAAU,QAAQ,CAAC,IAAI,MAAM,KAAK,IAAI,MAAM,KAAK,OAAO;YAC3D,MAAM,UAAU,CAAC,6FAA6F,CAAC;YAC/G,OAAO;gBACL,QAAQ;gBACR,SAAS;oBAAC;wBACR,KAAK;wBACL,OAAO;oBACT;iBAAE;gBACF,MAAM;oBACJ;gBACF;YACF;QACF;QACA,MAAM,EACJ,KAAK,EACL,KAAK,EACN,GAAG;QACJ,MAAM,kBAAkB,CAAC,UAAU,QAAQ,UAAU,KAAK,IAAI,KAAK,IAAI,MAAM,KAAK,KAAK,CAAC,CAAC,aAAa,IAAI,KAAK,MAAM,QAAQ,eAAe,KAAK,KAAK,CAAC,aAAa,WAAW,WAAW,MAAM,QAAQ,eAAe,KAAK,IAAI,KAAK,IAAI,WAAW,UAAU,CAAC,MAAM,KAAK,CAAC;QAC3Q,IAAI,CAAC,CAAC,UAAU,QAAQ,UAAU,KAAK,KAAK,MAAM,KAAK,KAAK,iBAAiB;YAC3E,IAAI,iBAAiB;gBACnB,QAAQ,OAAO,CAAC,KAAK,CAAC,4BAA4B,IAAI,MAAM,CAAC,eAAe,EAAE,UAAU,QAAQ,UAAU,KAAK,IAAI,KAAK,IAAI,MAAM,KAAK,CAAC,kCAAkC,CAAC;YAC7K;YACA,MAAM,SAAS,CAAC,GAAG,OAAO,OAAO,EAAE;gBACjC;YACF;YACA,OAAO,OAAO,KAAK,CAAC;gBAClB,OAAO;YACT;QACF;QACA,OAAO;YACL,UAAU,GAAG,MAAM,KAAK,CAAC,oBAAoB,CAAC;QAChD;IACF;IACA,MAAM,EACJ,MAAM,EACN,UAAU,EACV,KAAK,EACL,SAAS,KAAK,EACf,GAAG;IACJ,MAAM,EACJ,OAAO,EACP,OAAO,EACR,GAAG,MAAM,CAAC,GAAG,MAAM,IAAI,EAAE;QACxB;QACA;QACA;QACA,QAAQ,IAAI,MAAM;QAClB,aAAa,CAAC,wBAAwB,CAAC,YAAY,IAAI,IAAI,MAAM,QAAQ,cAAc,KAAK,IAAI,KAAK,IAAI,UAAU,WAAW,MAAM,QAAQ,0BAA0B,KAAK,IAAI,wBAAwB,CAAC,cAAc,IAAI,KAAK,MAAM,QAAQ,gBAAgB,KAAK,IAAI,KAAK,IAAI,YAAY,WAAW;QACtS,WAAW,CAAC,aAAa,IAAI,IAAI,MAAM,QAAQ,eAAe,KAAK,IAAI,KAAK,IAAI,WAAW,SAAS;QACpG,SAAS,IAAI,OAAO;QACpB,QAAQ,WAAW;IACrB;IACA,MAAM,eAAe,IAAI,QAAQ,YAAY,CAAC,QAAQ,OAAO,CAAC,YAAY,EAAE,KAAK,QAAQ,MAAM;IAC/F,IAAI,WAAW,OAAO;QACpB,MAAM,SAAS,CAAC,GAAG,OAAO,OAAO,EAAE;YACjC,GAAG,OAAO;YACV,OAAO,IAAI,KAAK;YAChB;QACF;QACA,MAAM,EACJ,KAAK,EACN,GAAG;QACJ,OAAQ;YACN,KAAK;gBACH,OAAO,MAAM,OAAO,SAAS,CAAC,QAAQ,SAAS;YACjD,KAAK;gBACH;oBACE,MAAM,UAAU,MAAM,OAAO,OAAO,CAAC;wBACnC;wBACA;oBACF;oBACA,IAAI,QAAQ,OAAO,EAAE,QAAQ,IAAI,IAAI,QAAQ,OAAO;oBACpD,OAAO;wBACL,GAAG,OAAO;wBACV;oBACF;gBACF;YACF,KAAK;gBACH,OAAO;oBACL,SAAS;wBAAC;4BACR,KAAK;4BACL,OAAO;wBACT;qBAAE;oBACF,MAAM;wBACJ,WAAW,QAAQ,SAAS;oBAC9B;oBACA;gBACF;YACF,KAAK;gBACH,IAAI,MAAM,MAAM,EAAE;oBAChB,IAAI,YAAY,GAAG,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC,OAAO,MAAM,IAAI,YAAY,EAAE,mBAAmB,QAAQ,WAAW,GAAG;oBAChI,IAAI,OAAO,YAAY,GAAG,UAAU,OAAO,EAAE,mBAAmB,QAAQ;oBACxE,OAAO;wBACL,UAAU;wBACV;oBACF;gBACF;gBACA,OAAO,OAAO,MAAM;YACtB,KAAK;gBACH,IAAI,MAAM,OAAO,EAAE,OAAO;oBACxB,UAAU,MAAM,OAAO;oBACvB;gBACF;gBACA,OAAO,OAAO,OAAO;YACvB,KAAK;gBACH,IAAI,QAAQ,QAAQ,EAAE;oBACpB,MAAM,WAAW,MAAM,OAAO,QAAQ,CAAC;wBACrC,MAAM,IAAI,IAAI;wBACd,OAAO,IAAI,KAAK;wBAChB,SAAS,IAAI,OAAO;wBACpB,SAAS,IAAI,OAAO;wBACpB;wBACA;wBACA;oBACF;oBACA,IAAI,SAAS,OAAO,EAAE,QAAQ,IAAI,IAAI,SAAS,OAAO;oBACtD,OAAO;wBACL,GAAG,QAAQ;wBACX;oBACF;gBACF;gBACA;YACF,KAAK;gBACH,IAAI,MAAM,aAAa,EAAE;oBACvB,OAAO;wBACL,UAAU,MAAM,aAAa;wBAC7B;oBACF;gBACF;gBACA,OAAO,OAAO,aAAa;YAC7B,KAAK;gBACH,IAAI;oBAAC;oBAAU;oBAAe;oBAAiB;oBAAsB;oBAAsB;oBAAY;oBAAyB;oBAAe;oBAAqB;iBAAkB,CAAC,QAAQ,CAAC,QAAQ;oBACtM,OAAO;wBACL,UAAU,GAAG,QAAQ,GAAG,CAAC,cAAc,EAAE,OAAO;wBAChD;oBACF;gBACF;gBACA,IAAI,MAAM,KAAK,EAAE;oBACf,OAAO;wBACL,UAAU,GAAG,MAAM,KAAK,GAAG,MAAM,KAAK,CAAC,QAAQ,CAAC,OAAO,MAAM,IAAI,MAAM,EAAE,OAAO;wBAChF;oBACF;gBACF;gBACA,OAAO,OAAO,KAAK,CAAC;oBAClB,OAAO;gBACT;YACF;QACF;IACF,OAAO,IAAI,WAAW,QAAQ;QAC5B,OAAQ;YACN,KAAK;gBACH,IAAI,QAAQ,iBAAiB,IAAI,QAAQ,QAAQ,EAAE;oBACjD,MAAM,SAAS,MAAM,OAAO,MAAM,CAAC;wBACjC,OAAO,IAAI,KAAK;wBAChB,MAAM,IAAI,IAAI;wBACd;oBACF;oBACA,IAAI,OAAO,OAAO,EAAE,QAAQ,IAAI,IAAI,OAAO,OAAO;oBAClD,OAAO;wBACL,GAAG,MAAM;wBACT;oBACF;gBACF;gBACA,OAAO;oBACL,UAAU,GAAG,QAAQ,GAAG,CAAC,iBAAiB,CAAC;oBAC3C;gBACF;YACF,KAAK;gBACH,IAAI,QAAQ,iBAAiB,EAAE;oBAC7B,MAAM,UAAU,MAAM,OAAO,OAAO,CAAC;wBACnC;wBACA;oBACF;oBACA,IAAI,QAAQ,OAAO,EAAE,QAAQ,IAAI,IAAI,QAAQ,OAAO;oBACpD,OAAO;wBACL,GAAG,OAAO;wBACV;oBACF;gBACF;gBACA,OAAO;oBACL,UAAU,GAAG,QAAQ,GAAG,CAAC,kBAAkB,CAAC;oBAC5C;gBACF;YACF,KAAK;gBACH,IAAI,QAAQ,QAAQ,EAAE;oBACpB,IAAI,QAAQ,QAAQ,CAAC,IAAI,KAAK,iBAAiB,CAAC,QAAQ,iBAAiB,EAAE;wBACzE,OAAO;4BACL,UAAU,GAAG,QAAQ,GAAG,CAAC,iBAAiB,CAAC;4BAC3C;wBACF;oBACF;oBACA,MAAM,WAAW,MAAM,OAAO,QAAQ,CAAC;wBACrC,MAAM,IAAI,IAAI;wBACd,OAAO,IAAI,KAAK;wBAChB,SAAS,IAAI,OAAO;wBACpB,SAAS,IAAI,OAAO;wBACpB;wBACA;wBACA;oBACF;oBACA,IAAI,SAAS,OAAO,EAAE,QAAQ,IAAI,IAAI,SAAS,OAAO;oBACtD,OAAO;wBACL,GAAG,QAAQ;wBACX;oBACF;gBACF;gBACA;YACF,KAAK;gBACH;oBACE,IAAI,YAAY,MAAM,EAAE;wBACtB,IAAI;4BACF,IAAI;4BACJ,MAAM,EACJ,IAAI,EACJ,KAAK,EACL,GAAG,UACJ,GAAG,CAAC,aAAa,IAAI,IAAI,MAAM,QAAQ,eAAe,KAAK,IAAI,aAAa,CAAC;4BAC9E,QAAQ,OAAO,CAAC,MAAM,CAAC,MAAM;wBAC/B,EAAE,OAAO,OAAO;4BACd,QAAQ,OAAO,CAAC,KAAK,CAAC,gBAAgB;wBACxC;oBACF;oBACA,OAAO,CAAC;gBACV;YACF,KAAK;gBACH;oBACE,IAAI,QAAQ,iBAAiB,EAAE;wBAC7B,IAAI;wBACJ,MAAM,UAAU,MAAM,OAAO,OAAO,CAAC;4BACnC;4BACA;4BACA,YAAY,CAAC,aAAa,IAAI,IAAI,MAAM,QAAQ,eAAe,KAAK,IAAI,KAAK,IAAI,WAAW,IAAI;4BAChG,UAAU;wBACZ;wBACA,IAAI,QAAQ,OAAO,EAAE,QAAQ,IAAI,IAAI,QAAQ,OAAO;wBACpD,OAAO;4BACL,GAAG,OAAO;4BACV;wBACF;oBACF;oBACA,OAAO;wBACL,QAAQ;wBACR,MAAM,CAAC;wBACP;oBACF;gBACF;YACF;QACF;IACF;IACA,OAAO;QACL,QAAQ;QACR,MAAM,CAAC,6BAA6B,EAAE,OAAO,gCAAgC,CAAC;IAChF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3430, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/node_modules/next-auth/next/utils.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.getBody = getBody;\nexports.setCookie = setCookie;\nexports.toResponse = toResponse;\nvar _cookie = require(\"cookie\");\nfunction setCookie(res, cookie) {\n  var _res$getHeader;\n  let setCookieHeader = (_res$getHeader = res.getHeader(\"Set-Cookie\")) !== null && _res$getHeader !== void 0 ? _res$getHeader : [];\n  if (!Array.isArray(setCookieHeader)) {\n    setCookieHeader = [setCookieHeader];\n  }\n  const {\n    name,\n    value,\n    options\n  } = cookie;\n  const cookieHeader = (0, _cookie.serialize)(name, value, options);\n  setCookieHeader.push(cookieHeader);\n  res.setHeader(\"Set-Cookie\", setCookieHeader);\n}\nasync function getBody(req) {\n  if (!(\"body\" in req) || !req.body || req.method !== \"POST\") return;\n  const contentType = req.headers.get(\"content-type\");\n  if (contentType !== null && contentType !== void 0 && contentType.includes(\"application/json\")) {\n    return await req.json();\n  } else if (contentType !== null && contentType !== void 0 && contentType.includes(\"application/x-www-form-urlencoded\")) {\n    const params = new URLSearchParams(await req.text());\n    return Object.fromEntries(params);\n  }\n}\nfunction toResponse(res) {\n  var _res$headers, _res$cookies, _res$status;\n  const headers = new Headers((_res$headers = res.headers) === null || _res$headers === void 0 ? void 0 : _res$headers.reduce((acc, {\n    key,\n    value\n  }) => {\n    acc[key] = value;\n    return acc;\n  }, {}));\n  (_res$cookies = res.cookies) === null || _res$cookies === void 0 || _res$cookies.forEach(cookie => {\n    const {\n      name,\n      value,\n      options\n    } = cookie;\n    const cookieHeader = (0, _cookie.serialize)(name, value, options);\n    if (headers.has(\"Set-Cookie\")) headers.append(\"Set-Cookie\", cookieHeader);else headers.set(\"Set-Cookie\", cookieHeader);\n  });\n  let body = res.body;\n  if (headers.get(\"content-type\") === \"application/json\") body = JSON.stringify(res.body);else if (headers.get(\"content-type\") === \"application/x-www-form-urlencoded\") body = new URLSearchParams(res.body).toString();\n  const status = res.redirect ? 302 : (_res$status = res.status) !== null && _res$status !== void 0 ? _res$status : 200;\n  const response = new Response(body, {\n    headers,\n    status\n  });\n  if (res.redirect) response.headers.set(\"Location\", res.redirect);\n  return response;\n}"], "names": [], "mappings": "AAAA;AAEA,OAAO,cAAc,CAAC,SAAS,cAAc;IAC3C,OAAO;AACT;AACA,QAAQ,OAAO,GAAG;AAClB,QAAQ,SAAS,GAAG;AACpB,QAAQ,UAAU,GAAG;AACrB,IAAI;AACJ,SAAS,UAAU,GAAG,EAAE,MAAM;IAC5B,IAAI;IACJ,IAAI,kBAAkB,CAAC,iBAAiB,IAAI,SAAS,CAAC,aAAa,MAAM,QAAQ,mBAAmB,KAAK,IAAI,iBAAiB,EAAE;IAChI,IAAI,CAAC,MAAM,OAAO,CAAC,kBAAkB;QACnC,kBAAkB;YAAC;SAAgB;IACrC;IACA,MAAM,EACJ,IAAI,EACJ,KAAK,EACL,OAAO,EACR,GAAG;IACJ,MAAM,eAAe,CAAC,GAAG,QAAQ,SAAS,EAAE,MAAM,OAAO;IACzD,gBAAgB,IAAI,CAAC;IACrB,IAAI,SAAS,CAAC,cAAc;AAC9B;AACA,eAAe,QAAQ,GAAG;IACxB,IAAI,CAAC,CAAC,UAAU,GAAG,KAAK,CAAC,IAAI,IAAI,IAAI,IAAI,MAAM,KAAK,QAAQ;IAC5D,MAAM,cAAc,IAAI,OAAO,CAAC,GAAG,CAAC;IACpC,IAAI,gBAAgB,QAAQ,gBAAgB,KAAK,KAAK,YAAY,QAAQ,CAAC,qBAAqB;QAC9F,OAAO,MAAM,IAAI,IAAI;IACvB,OAAO,IAAI,gBAAgB,QAAQ,gBAAgB,KAAK,KAAK,YAAY,QAAQ,CAAC,sCAAsC;QACtH,MAAM,SAAS,IAAI,gBAAgB,MAAM,IAAI,IAAI;QACjD,OAAO,OAAO,WAAW,CAAC;IAC5B;AACF;AACA,SAAS,WAAW,GAAG;IACrB,IAAI,cAAc,cAAc;IAChC,MAAM,UAAU,IAAI,QAAQ,CAAC,eAAe,IAAI,OAAO,MAAM,QAAQ,iBAAiB,KAAK,IAAI,KAAK,IAAI,aAAa,MAAM,CAAC,CAAC,KAAK,EAChI,GAAG,EACH,KAAK,EACN;QACC,GAAG,CAAC,IAAI,GAAG;QACX,OAAO;IACT,GAAG,CAAC;IACJ,CAAC,eAAe,IAAI,OAAO,MAAM,QAAQ,iBAAiB,KAAK,KAAK,aAAa,OAAO,CAAC,CAAA;QACvF,MAAM,EACJ,IAAI,EACJ,KAAK,EACL,OAAO,EACR,GAAG;QACJ,MAAM,eAAe,CAAC,GAAG,QAAQ,SAAS,EAAE,MAAM,OAAO;QACzD,IAAI,QAAQ,GAAG,CAAC,eAAe,QAAQ,MAAM,CAAC,cAAc;aAAmB,QAAQ,GAAG,CAAC,cAAc;IAC3G;IACA,IAAI,OAAO,IAAI,IAAI;IACnB,IAAI,QAAQ,GAAG,CAAC,oBAAoB,oBAAoB,OAAO,KAAK,SAAS,CAAC,IAAI,IAAI;SAAO,IAAI,QAAQ,GAAG,CAAC,oBAAoB,qCAAqC,OAAO,IAAI,gBAAgB,IAAI,IAAI,EAAE,QAAQ;IACnN,MAAM,SAAS,IAAI,QAAQ,GAAG,MAAM,CAAC,cAAc,IAAI,MAAM,MAAM,QAAQ,gBAAgB,KAAK,IAAI,cAAc;IAClH,MAAM,WAAW,IAAI,SAAS,MAAM;QAClC;QACA;IACF;IACA,IAAI,IAAI,QAAQ,EAAE,SAAS,OAAO,CAAC,GAAG,CAAC,YAAY,IAAI,QAAQ;IAC/D,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3489, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/node_modules/next-auth/next/index.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nexports.getServerSession = getServerSession;\nexports.unstable_getServerSession = unstable_getServerSession;\nvar _core = require(\"../core\");\nvar _utils = require(\"./utils\");\nasync function NextAuthApiHandler(req, res, options) {\n  var _options$secret, _ref, _options$jwt$secret, _options$jwt, _ref2, _handler$status, _handler$cookies, _handler$headers;\n  const {\n    nextauth,\n    ...query\n  } = req.query;\n  (_options$secret = options.secret) !== null && _options$secret !== void 0 ? _options$secret : options.secret = (_ref = (_options$jwt$secret = (_options$jwt = options.jwt) === null || _options$jwt === void 0 ? void 0 : _options$jwt.secret) !== null && _options$jwt$secret !== void 0 ? _options$jwt$secret : process.env.NEXTAUTH_SECRET) !== null && _ref !== void 0 ? _ref : process.env.AUTH_SECRET;\n  const handler = await (0, _core.AuthHandler)({\n    req: {\n      body: req.body,\n      query,\n      cookies: req.cookies,\n      headers: req.headers,\n      method: req.method,\n      action: nextauth === null || nextauth === void 0 ? void 0 : nextauth[0],\n      providerId: nextauth === null || nextauth === void 0 ? void 0 : nextauth[1],\n      error: (_ref2 = req.query.error) !== null && _ref2 !== void 0 ? _ref2 : nextauth === null || nextauth === void 0 ? void 0 : nextauth[1]\n    },\n    options\n  });\n  res.status((_handler$status = handler.status) !== null && _handler$status !== void 0 ? _handler$status : 200);\n  (_handler$cookies = handler.cookies) === null || _handler$cookies === void 0 || _handler$cookies.forEach(cookie => (0, _utils.setCookie)(res, cookie));\n  (_handler$headers = handler.headers) === null || _handler$headers === void 0 || _handler$headers.forEach(h => res.setHeader(h.key, h.value));\n  if (handler.redirect) {\n    var _req$body;\n    if (((_req$body = req.body) === null || _req$body === void 0 ? void 0 : _req$body.json) !== \"true\") {\n      res.status(302).setHeader(\"Location\", handler.redirect);\n      res.end();\n      return;\n    }\n    return res.json({\n      url: handler.redirect\n    });\n  }\n  return res.send(handler.body);\n}\nasync function NextAuthRouteHandler(req, context, options) {\n  var _options$secret2, _process$env$NEXTAUTH, _await$context$params, _query$error;\n  (_options$secret2 = options.secret) !== null && _options$secret2 !== void 0 ? _options$secret2 : options.secret = (_process$env$NEXTAUTH = process.env.NEXTAUTH_SECRET) !== null && _process$env$NEXTAUTH !== void 0 ? _process$env$NEXTAUTH : process.env.AUTH_SECRET;\n  const {\n    headers,\n    cookies\n  } = require(\"next/headers\");\n  const nextauth = (_await$context$params = await context.params) === null || _await$context$params === void 0 ? void 0 : _await$context$params.nextauth;\n  const query = Object.fromEntries(req.nextUrl.searchParams);\n  const body = await (0, _utils.getBody)(req);\n  const internalResponse = await (0, _core.AuthHandler)({\n    req: {\n      body,\n      query,\n      cookies: Object.fromEntries((await cookies()).getAll().map(c => [c.name, c.value])),\n      headers: Object.fromEntries(await headers()),\n      method: req.method,\n      action: nextauth === null || nextauth === void 0 ? void 0 : nextauth[0],\n      providerId: nextauth === null || nextauth === void 0 ? void 0 : nextauth[1],\n      error: (_query$error = query.error) !== null && _query$error !== void 0 ? _query$error : nextauth === null || nextauth === void 0 ? void 0 : nextauth[1]\n    },\n    options\n  });\n  const response = (0, _utils.toResponse)(internalResponse);\n  const redirect = response.headers.get(\"Location\");\n  if ((body === null || body === void 0 ? void 0 : body.json) === \"true\" && redirect) {\n    response.headers.delete(\"Location\");\n    response.headers.set(\"Content-Type\", \"application/json\");\n    return new Response(JSON.stringify({\n      url: redirect\n    }), {\n      status: internalResponse.status,\n      headers: response.headers\n    });\n  }\n  return response;\n}\nfunction NextAuth(...args) {\n  var _args$;\n  if (args.length === 1) {\n    return async (req, res) => {\n      if (res !== null && res !== void 0 && res.params) {\n        return await NextAuthRouteHandler(req, res, args[0]);\n      }\n      return await NextAuthApiHandler(req, res, args[0]);\n    };\n  }\n  if ((_args$ = args[1]) !== null && _args$ !== void 0 && _args$.params) {\n    return NextAuthRouteHandler(...args);\n  }\n  return NextAuthApiHandler(...args);\n}\nvar _default = exports.default = NextAuth;\nasync function getServerSession(...args) {\n  var _options, _options$secret3, _process$env$NEXTAUTH2;\n  const isRSC = args.length === 0 || args.length === 1;\n  let req, res, options;\n  if (isRSC) {\n    options = Object.assign({}, args[0], {\n      providers: []\n    });\n    const {\n      headers,\n      cookies\n    } = require(\"next/headers\");\n    req = {\n      headers: Object.fromEntries(await headers()),\n      cookies: Object.fromEntries((await cookies()).getAll().map(c => [c.name, c.value]))\n    };\n    res = {\n      getHeader() {},\n      setCookie() {},\n      setHeader() {}\n    };\n  } else {\n    req = args[0];\n    res = args[1];\n    options = Object.assign({}, args[2], {\n      providers: []\n    });\n  }\n  (_options$secret3 = (_options = options).secret) !== null && _options$secret3 !== void 0 ? _options$secret3 : _options.secret = (_process$env$NEXTAUTH2 = process.env.NEXTAUTH_SECRET) !== null && _process$env$NEXTAUTH2 !== void 0 ? _process$env$NEXTAUTH2 : process.env.AUTH_SECRET;\n  const session = await (0, _core.AuthHandler)({\n    options,\n    req: {\n      action: \"session\",\n      method: \"GET\",\n      cookies: req.cookies,\n      headers: req.headers\n    }\n  });\n  const {\n    body,\n    cookies,\n    status = 200\n  } = session;\n  cookies === null || cookies === void 0 || cookies.forEach(cookie => (0, _utils.setCookie)(res, cookie));\n  if (body && typeof body !== \"string\" && Object.keys(body).length) {\n    if (status === 200) {\n      if (isRSC) delete body.expires;\n      return body;\n    }\n    throw new Error(body.message);\n  }\n  return null;\n}\nlet deprecatedWarningShown = false;\nasync function unstable_getServerSession(...args) {\n  if (!deprecatedWarningShown && process.env.NODE_ENV !== \"production\") {\n    console.warn(\"`unstable_getServerSession` has been renamed to `getServerSession`.\");\n    deprecatedWarningShown = true;\n  }\n  return await getServerSession(...args);\n}"], "names": [], "mappings": "AAAA;AAEA,OAAO,cAAc,CAAC,SAAS,cAAc;IAC3C,OAAO;AACT;AACA,QAAQ,OAAO,GAAG,KAAK;AACvB,QAAQ,gBAAgB,GAAG;AAC3B,QAAQ,yBAAyB,GAAG;AACpC,IAAI;AACJ,IAAI;AACJ,eAAe,mBAAmB,GAAG,EAAE,GAAG,EAAE,OAAO;IACjD,IAAI,iBAAiB,MAAM,qBAAqB,cAAc,OAAO,iBAAiB,kBAAkB;IACxG,MAAM,EACJ,QAAQ,EACR,GAAG,OACJ,GAAG,IAAI,KAAK;IACb,CAAC,kBAAkB,QAAQ,MAAM,MAAM,QAAQ,oBAAoB,KAAK,IAAI,kBAAkB,QAAQ,MAAM,GAAG,CAAC,OAAO,CAAC,sBAAsB,CAAC,eAAe,QAAQ,GAAG,MAAM,QAAQ,iBAAiB,KAAK,IAAI,KAAK,IAAI,aAAa,MAAM,MAAM,QAAQ,wBAAwB,KAAK,IAAI,sBAAsB,QAAQ,GAAG,CAAC,eAAe,MAAM,QAAQ,SAAS,KAAK,IAAI,OAAO,QAAQ,GAAG,CAAC,WAAW;IAC3Y,MAAM,UAAU,MAAM,CAAC,GAAG,MAAM,WAAW,EAAE;QAC3C,KAAK;YACH,MAAM,IAAI,IAAI;YACd;YACA,SAAS,IAAI,OAAO;YACpB,SAAS,IAAI,OAAO;YACpB,QAAQ,IAAI,MAAM;YAClB,QAAQ,aAAa,QAAQ,aAAa,KAAK,IAAI,KAAK,IAAI,QAAQ,CAAC,EAAE;YACvE,YAAY,aAAa,QAAQ,aAAa,KAAK,IAAI,KAAK,IAAI,QAAQ,CAAC,EAAE;YAC3E,OAAO,CAAC,QAAQ,IAAI,KAAK,CAAC,KAAK,MAAM,QAAQ,UAAU,KAAK,IAAI,QAAQ,aAAa,QAAQ,aAAa,KAAK,IAAI,KAAK,IAAI,QAAQ,CAAC,EAAE;QACzI;QACA;IACF;IACA,IAAI,MAAM,CAAC,CAAC,kBAAkB,QAAQ,MAAM,MAAM,QAAQ,oBAAoB,KAAK,IAAI,kBAAkB;IACzG,CAAC,mBAAmB,QAAQ,OAAO,MAAM,QAAQ,qBAAqB,KAAK,KAAK,iBAAiB,OAAO,CAAC,CAAA,SAAU,CAAC,GAAG,OAAO,SAAS,EAAE,KAAK;IAC9I,CAAC,mBAAmB,QAAQ,OAAO,MAAM,QAAQ,qBAAqB,KAAK,KAAK,iBAAiB,OAAO,CAAC,CAAA,IAAK,IAAI,SAAS,CAAC,EAAE,GAAG,EAAE,EAAE,KAAK;IAC1I,IAAI,QAAQ,QAAQ,EAAE;QACpB,IAAI;QACJ,IAAI,CAAC,CAAC,YAAY,IAAI,IAAI,MAAM,QAAQ,cAAc,KAAK,IAAI,KAAK,IAAI,UAAU,IAAI,MAAM,QAAQ;YAClG,IAAI,MAAM,CAAC,KAAK,SAAS,CAAC,YAAY,QAAQ,QAAQ;YACtD,IAAI,GAAG;YACP;QACF;QACA,OAAO,IAAI,IAAI,CAAC;YACd,KAAK,QAAQ,QAAQ;QACvB;IACF;IACA,OAAO,IAAI,IAAI,CAAC,QAAQ,IAAI;AAC9B;AACA,eAAe,qBAAqB,GAAG,EAAE,OAAO,EAAE,OAAO;IACvD,IAAI,kBAAkB,uBAAuB,uBAAuB;IACpE,CAAC,mBAAmB,QAAQ,MAAM,MAAM,QAAQ,qBAAqB,KAAK,IAAI,mBAAmB,QAAQ,MAAM,GAAG,CAAC,wBAAwB,QAAQ,GAAG,CAAC,eAAe,MAAM,QAAQ,0BAA0B,KAAK,IAAI,wBAAwB,QAAQ,GAAG,CAAC,WAAW;IACtQ,MAAM,EACJ,OAAO,EACP,OAAO,EACR;IACD,MAAM,WAAW,CAAC,wBAAwB,MAAM,QAAQ,MAAM,MAAM,QAAQ,0BAA0B,KAAK,IAAI,KAAK,IAAI,sBAAsB,QAAQ;IACtJ,MAAM,QAAQ,OAAO,WAAW,CAAC,IAAI,OAAO,CAAC,YAAY;IACzD,MAAM,OAAO,MAAM,CAAC,GAAG,OAAO,OAAO,EAAE;IACvC,MAAM,mBAAmB,MAAM,CAAC,GAAG,MAAM,WAAW,EAAE;QACpD,KAAK;YACH;YACA;YACA,SAAS,OAAO,WAAW,CAAC,CAAC,MAAM,SAAS,EAAE,MAAM,GAAG,GAAG,CAAC,CAAA,IAAK;oBAAC,EAAE,IAAI;oBAAE,EAAE,KAAK;iBAAC;YACjF,SAAS,OAAO,WAAW,CAAC,MAAM;YAClC,QAAQ,IAAI,MAAM;YAClB,QAAQ,aAAa,QAAQ,aAAa,KAAK,IAAI,KAAK,IAAI,QAAQ,CAAC,EAAE;YACvE,YAAY,aAAa,QAAQ,aAAa,KAAK,IAAI,KAAK,IAAI,QAAQ,CAAC,EAAE;YAC3E,OAAO,CAAC,eAAe,MAAM,KAAK,MAAM,QAAQ,iBAAiB,KAAK,IAAI,eAAe,aAAa,QAAQ,aAAa,KAAK,IAAI,KAAK,IAAI,QAAQ,CAAC,EAAE;QAC1J;QACA;IACF;IACA,MAAM,WAAW,CAAC,GAAG,OAAO,UAAU,EAAE;IACxC,MAAM,WAAW,SAAS,OAAO,CAAC,GAAG,CAAC;IACtC,IAAI,CAAC,SAAS,QAAQ,SAAS,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,MAAM,UAAU,UAAU;QAClF,SAAS,OAAO,CAAC,MAAM,CAAC;QACxB,SAAS,OAAO,CAAC,GAAG,CAAC,gBAAgB;QACrC,OAAO,IAAI,SAAS,KAAK,SAAS,CAAC;YACjC,KAAK;QACP,IAAI;YACF,QAAQ,iBAAiB,MAAM;YAC/B,SAAS,SAAS,OAAO;QAC3B;IACF;IACA,OAAO;AACT;AACA,SAAS,SAAS,GAAG,IAAI;IACvB,IAAI;IACJ,IAAI,KAAK,MAAM,KAAK,GAAG;QACrB,OAAO,OAAO,KAAK;YACjB,IAAI,QAAQ,QAAQ,QAAQ,KAAK,KAAK,IAAI,MAAM,EAAE;gBAChD,OAAO,MAAM,qBAAqB,KAAK,KAAK,IAAI,CAAC,EAAE;YACrD;YACA,OAAO,MAAM,mBAAmB,KAAK,KAAK,IAAI,CAAC,EAAE;QACnD;IACF;IACA,IAAI,CAAC,SAAS,IAAI,CAAC,EAAE,MAAM,QAAQ,WAAW,KAAK,KAAK,OAAO,MAAM,EAAE;QACrE,OAAO,wBAAwB;IACjC;IACA,OAAO,sBAAsB;AAC/B;AACA,IAAI,WAAW,QAAQ,OAAO,GAAG;AACjC,eAAe,iBAAiB,GAAG,IAAI;IACrC,IAAI,UAAU,kBAAkB;IAChC,MAAM,QAAQ,KAAK,MAAM,KAAK,KAAK,KAAK,MAAM,KAAK;IACnD,IAAI,KAAK,KAAK;IACd,IAAI,OAAO;QACT,UAAU,OAAO,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,EAAE;YACnC,WAAW,EAAE;QACf;QACA,MAAM,EACJ,OAAO,EACP,OAAO,EACR;QACD,MAAM;YACJ,SAAS,OAAO,WAAW,CAAC,MAAM;YAClC,SAAS,OAAO,WAAW,CAAC,CAAC,MAAM,SAAS,EAAE,MAAM,GAAG,GAAG,CAAC,CAAA,IAAK;oBAAC,EAAE,IAAI;oBAAE,EAAE,KAAK;iBAAC;QACnF;QACA,MAAM;YACJ,cAAa;YACb,cAAa;YACb,cAAa;QACf;IACF,OAAO;QACL,MAAM,IAAI,CAAC,EAAE;QACb,MAAM,IAAI,CAAC,EAAE;QACb,UAAU,OAAO,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,EAAE;YACnC,WAAW,EAAE;QACf;IACF;IACA,CAAC,mBAAmB,CAAC,WAAW,OAAO,EAAE,MAAM,MAAM,QAAQ,qBAAqB,KAAK,IAAI,mBAAmB,SAAS,MAAM,GAAG,CAAC,yBAAyB,QAAQ,GAAG,CAAC,eAAe,MAAM,QAAQ,2BAA2B,KAAK,IAAI,yBAAyB,QAAQ,GAAG,CAAC,WAAW;IACvR,MAAM,UAAU,MAAM,CAAC,GAAG,MAAM,WAAW,EAAE;QAC3C;QACA,KAAK;YACH,QAAQ;YACR,QAAQ;YACR,SAAS,IAAI,OAAO;YACpB,SAAS,IAAI,OAAO;QACtB;IACF;IACA,MAAM,EACJ,IAAI,EACJ,OAAO,EACP,SAAS,GAAG,EACb,GAAG;IACJ,YAAY,QAAQ,YAAY,KAAK,KAAK,QAAQ,OAAO,CAAC,CAAA,SAAU,CAAC,GAAG,OAAO,SAAS,EAAE,KAAK;IAC/F,IAAI,QAAQ,OAAO,SAAS,YAAY,OAAO,IAAI,CAAC,MAAM,MAAM,EAAE;QAChE,IAAI,WAAW,KAAK;YAClB,IAAI,OAAO,OAAO,KAAK,OAAO;YAC9B,OAAO;QACT;QACA,MAAM,IAAI,MAAM,KAAK,OAAO;IAC9B;IACA,OAAO;AACT;AACA,IAAI,yBAAyB;AAC7B,eAAe,0BAA0B,GAAG,IAAI;IAC9C,IAAI,CAAC,0BAA0B,oDAAyB,cAAc;QACpE,QAAQ,IAAI,CAAC;QACb,yBAAyB;IAC3B;IACA,OAAO,MAAM,oBAAoB;AACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3646, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/node_modules/next-auth/index.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nvar _exportNames = {};\nObject.defineProperty(exports, \"default\", {\n  enumerable: true,\n  get: function () {\n    return _next.default;\n  }\n});\nvar _types = require(\"./core/types\");\nObject.keys(_types).forEach(function (key) {\n  if (key === \"default\" || key === \"__esModule\") return;\n  if (Object.prototype.hasOwnProperty.call(_exportNames, key)) return;\n  if (key in exports && exports[key] === _types[key]) return;\n  Object.defineProperty(exports, key, {\n    enumerable: true,\n    get: function () {\n      return _types[key];\n    }\n  });\n});\nvar _next = _interopRequireWildcard(require(\"./next\"));\nObject.keys(_next).forEach(function (key) {\n  if (key === \"default\" || key === \"__esModule\") return;\n  if (Object.prototype.hasOwnProperty.call(_exportNames, key)) return;\n  if (key in exports && exports[key] === _next[key]) return;\n  Object.defineProperty(exports, key, {\n    enumerable: true,\n    get: function () {\n      return _next[key];\n    }\n  });\n});\nfunction _getRequireWildcardCache(e) { if (\"function\" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function (e) { return e ? t : r; })(e); }\nfunction _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if (\"default\" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }"], "names": [], "mappings": "AAAA;AAEA,OAAO,cAAc,CAAC,SAAS,cAAc;IAC3C,OAAO;AACT;AACA,IAAI,eAAe,CAAC;AACpB,OAAO,cAAc,CAAC,SAAS,WAAW;IACxC,YAAY;IACZ,KAAK;QACH,OAAO,MAAM,OAAO;IACtB;AACF;AACA,IAAI;AACJ,OAAO,IAAI,CAAC,QAAQ,OAAO,CAAC,SAAU,GAAG;IACvC,IAAI,QAAQ,aAAa,QAAQ,cAAc;IAC/C,IAAI,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,cAAc,MAAM;IAC7D,IAAI,OAAO,WAAW,OAAO,CAAC,IAAI,KAAK,MAAM,CAAC,IAAI,EAAE;IACpD,OAAO,cAAc,CAAC,SAAS,KAAK;QAClC,YAAY;QACZ,KAAK;YACH,OAAO,MAAM,CAAC,IAAI;QACpB;IACF;AACF;AACA,IAAI,QAAQ;AACZ,OAAO,IAAI,CAAC,OAAO,OAAO,CAAC,SAAU,GAAG;IACtC,IAAI,QAAQ,aAAa,QAAQ,cAAc;IAC/C,IAAI,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,cAAc,MAAM;IAC7D,IAAI,OAAO,WAAW,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,EAAE;IACnD,OAAO,cAAc,CAAC,SAAS,KAAK;QAClC,YAAY;QACZ,KAAK;YACH,OAAO,KAAK,CAAC,IAAI;QACnB;IACF;AACF;AACA,SAAS,yBAAyB,CAAC;IAAI,IAAI,cAAc,OAAO,SAAS,OAAO;IAAM,IAAI,IAAI,IAAI,WAAW,IAAI,IAAI;IAAW,OAAO,CAAC,2BAA2B,SAAU,CAAC;QAAI,OAAO,IAAI,IAAI;IAAG,CAAC,EAAE;AAAI;AAC3M,SAAS,wBAAwB,CAAC,EAAE,CAAC;IAAI,IAAI,CAAC,KAAK,KAAK,EAAE,UAAU,EAAE,OAAO;IAAG,IAAI,SAAS,KAAK,YAAY,OAAO,KAAK,cAAc,OAAO,GAAG,OAAO;QAAE,SAAS;IAAE;IAAG,IAAI,IAAI,yBAAyB;IAAI,IAAI,KAAK,EAAE,GAAG,CAAC,IAAI,OAAO,EAAE,GAAG,CAAC;IAAI,IAAI,IAAI;QAAE,WAAW;IAAK,GAAG,IAAI,OAAO,cAAc,IAAI,OAAO,wBAAwB;IAAE,IAAK,IAAI,KAAK,EAAG,IAAI,cAAc,KAAK,CAAA,CAAC,CAAA,EAAE,cAAc,CAAC,IAAI,CAAC,GAAG,IAAI;QAAE,IAAI,IAAI,IAAI,OAAO,wBAAwB,CAAC,GAAG,KAAK;QAAM,KAAK,CAAC,EAAE,GAAG,IAAI,EAAE,GAAG,IAAI,OAAO,cAAc,CAAC,GAAG,GAAG,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;IAAE;IAAE,OAAO,EAAE,OAAO,GAAG,GAAG,KAAK,EAAE,GAAG,CAAC,GAAG,IAAI;AAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3709, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/node_modules/next-auth/providers/google.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = Google;\nfunction Google(options) {\n  return {\n    id: \"google\",\n    name: \"<PERSON>\",\n    type: \"oauth\",\n    wellKnown: \"https://accounts.google.com/.well-known/openid-configuration\",\n    authorization: {\n      params: {\n        scope: \"openid email profile\"\n      }\n    },\n    idToken: true,\n    checks: [\"pkce\", \"state\"],\n    profile(profile) {\n      return {\n        id: profile.sub,\n        name: profile.name,\n        email: profile.email,\n        image: profile.picture\n      };\n    },\n    style: {\n      logo: \"/google.svg\",\n      bg: \"#fff\",\n      text: \"#000\"\n    },\n    options\n  };\n}"], "names": [], "mappings": "AAAA;AAEA,OAAO,cAAc,CAAC,SAAS,cAAc;IAC3C,OAAO;AACT;AACA,QAAQ,OAAO,GAAG;AAClB,SAAS,OAAO,OAAO;IACrB,OAAO;QACL,IAAI;QACJ,MAAM;QACN,MAAM;QACN,WAAW;QACX,eAAe;YACb,QAAQ;gBACN,OAAO;YACT;QACF;QACA,SAAS;QACT,QAAQ;YAAC;YAAQ;SAAQ;QACzB,SAAQ,OAAO;YACb,OAAO;gBACL,IAAI,QAAQ,GAAG;gBACf,MAAM,QAAQ,IAAI;gBAClB,OAAO,QAAQ,KAAK;gBACpB,OAAO,QAAQ,OAAO;YACxB;QACF;QACA,OAAO;YACL,MAAM;YACN,IAAI;YACJ,MAAM;QACR;QACA;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3751, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/node_modules/next-auth/providers/github.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = Github;\nfunction Github(options) {\n  return {\n    id: \"github\",\n    name: \"Git<PERSON><PERSON>\",\n    type: \"oauth\",\n    authorization: {\n      url: \"https://github.com/login/oauth/authorize\",\n      params: {\n        scope: \"read:user user:email\"\n      }\n    },\n    token: \"https://github.com/login/oauth/access_token\",\n    userinfo: {\n      url: \"https://api.github.com/user\",\n      async request({\n        client,\n        tokens\n      }) {\n        const profile = await client.userinfo(tokens.access_token);\n        if (!profile.email) {\n          const res = await fetch(\"https://api.github.com/user/emails\", {\n            headers: {\n              Authorization: `token ${tokens.access_token}`\n            }\n          });\n          if (res.ok) {\n            var _emails$find;\n            const emails = await res.json();\n            profile.email = ((_emails$find = emails.find(e => e.primary)) !== null && _emails$find !== void 0 ? _emails$find : emails[0]).email;\n          }\n        }\n        return profile;\n      }\n    },\n    profile(profile) {\n      var _profile$name;\n      return {\n        id: profile.id.toString(),\n        name: (_profile$name = profile.name) !== null && _profile$name !== void 0 ? _profile$name : profile.login,\n        email: profile.email,\n        image: profile.avatar_url\n      };\n    },\n    style: {\n      logo: \"/github.svg\",\n      bg: \"#24292f\",\n      text: \"#fff\"\n    },\n    options\n  };\n}"], "names": [], "mappings": "AAAA;AAEA,OAAO,cAAc,CAAC,SAAS,cAAc;IAC3C,OAAO;AACT;AACA,QAAQ,OAAO,GAAG;AAClB,SAAS,OAAO,OAAO;IACrB,OAAO;QACL,IAAI;QACJ,MAAM;QACN,MAAM;QACN,eAAe;YACb,KAAK;YACL,QAAQ;gBACN,OAAO;YACT;QACF;QACA,OAAO;QACP,UAAU;YACR,KAAK;YACL,MAAM,SAAQ,EACZ,MAAM,EACN,MAAM,EACP;gBACC,MAAM,UAAU,MAAM,OAAO,QAAQ,CAAC,OAAO,YAAY;gBACzD,IAAI,CAAC,QAAQ,KAAK,EAAE;oBAClB,MAAM,MAAM,MAAM,MAAM,sCAAsC;wBAC5D,SAAS;4BACP,eAAe,CAAC,MAAM,EAAE,OAAO,YAAY,EAAE;wBAC/C;oBACF;oBACA,IAAI,IAAI,EAAE,EAAE;wBACV,IAAI;wBACJ,MAAM,SAAS,MAAM,IAAI,IAAI;wBAC7B,QAAQ,KAAK,GAAG,CAAC,CAAC,eAAe,OAAO,IAAI,CAAC,CAAA,IAAK,EAAE,OAAO,CAAC,MAAM,QAAQ,iBAAiB,KAAK,IAAI,eAAe,MAAM,CAAC,EAAE,EAAE,KAAK;oBACrI;gBACF;gBACA,OAAO;YACT;QACF;QACA,SAAQ,OAAO;YACb,IAAI;YACJ,OAAO;gBACL,IAAI,QAAQ,EAAE,CAAC,QAAQ;gBACvB,MAAM,CAAC,gBAAgB,QAAQ,IAAI,MAAM,QAAQ,kBAAkB,KAAK,IAAI,gBAAgB,QAAQ,KAAK;gBACzG,OAAO,QAAQ,KAAK;gBACpB,OAAO,QAAQ,UAAU;YAC3B;QACF;QACA,OAAO;YACL,MAAM;YACN,IAAI;YACJ,MAAM;QACR;QACA;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3809, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/node_modules/next-auth/providers/email.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = Email;\nvar _nodemailer = require(\"nodemailer\");\nfunction Email(options) {\n  return {\n    id: \"email\",\n    type: \"email\",\n    name: \"Email\",\n    server: {\n      host: \"localhost\",\n      port: 25,\n      auth: {\n        user: \"\",\n        pass: \"\"\n      }\n    },\n    from: \"NextAuth <<EMAIL>>\",\n    maxAge: 24 * 60 * 60,\n    async sendVerificationRequest(params) {\n      const {\n        identifier,\n        url,\n        provider,\n        theme\n      } = params;\n      const {\n        host\n      } = new URL(url);\n      const transport = (0, _nodemailer.createTransport)(provider.server);\n      const result = await transport.sendMail({\n        to: identifier,\n        from: provider.from,\n        subject: `Sign in to ${host}`,\n        text: text({\n          url,\n          host\n        }),\n        html: html({\n          url,\n          host,\n          theme\n        })\n      });\n      const failed = result.rejected.concat(result.pending).filter(Boolean);\n      if (failed.length) {\n        throw new Error(`Email (${failed.join(\", \")}) could not be sent`);\n      }\n    },\n    options\n  };\n}\nfunction html(params) {\n  const {\n    url,\n    host,\n    theme\n  } = params;\n  const escapedHost = host.replace(/\\./g, \"&#8203;.\");\n  const brandColor = theme.brandColor || \"#346df1\";\n  const buttonText = theme.buttonText || \"#fff\";\n  const color = {\n    background: \"#f9f9f9\",\n    text: \"#444\",\n    mainBackground: \"#fff\",\n    buttonBackground: brandColor,\n    buttonBorder: brandColor,\n    buttonText\n  };\n  return `\n<body style=\"background: ${color.background};\">\n  <table width=\"100%\" border=\"0\" cellspacing=\"20\" cellpadding=\"0\"\n    style=\"background: ${color.mainBackground}; max-width: 600px; margin: auto; border-radius: 10px;\">\n    <tr>\n      <td align=\"center\"\n        style=\"padding: 10px 0px; font-size: 22px; font-family: Helvetica, Arial, sans-serif; color: ${color.text};\">\n        Sign in to <strong>${escapedHost}</strong>\n      </td>\n    </tr>\n    <tr>\n      <td align=\"center\" style=\"padding: 20px 0;\">\n        <table border=\"0\" cellspacing=\"0\" cellpadding=\"0\">\n          <tr>\n            <td align=\"center\" style=\"border-radius: 5px;\" bgcolor=\"${color.buttonBackground}\"><a href=\"${url}\"\n                target=\"_blank\"\n                style=\"font-size: 18px; font-family: Helvetica, Arial, sans-serif; color: ${color.buttonText}; text-decoration: none; border-radius: 5px; padding: 10px 20px; border: 1px solid ${color.buttonBorder}; display: inline-block; font-weight: bold;\">Sign\n                in</a></td>\n          </tr>\n        </table>\n      </td>\n    </tr>\n    <tr>\n      <td align=\"center\"\n        style=\"padding: 0px 0px 10px 0px; font-size: 16px; line-height: 22px; font-family: Helvetica, Arial, sans-serif; color: ${color.text};\">\n        If you did not request this email you can safely ignore it.\n      </td>\n    </tr>\n  </table>\n</body>\n`;\n}\nfunction text({\n  url,\n  host\n}) {\n  return `Sign in to ${host}\\n${url}\\n\\n`;\n}"], "names": [], "mappings": "AAAA;AAEA,OAAO,cAAc,CAAC,SAAS,cAAc;IAC3C,OAAO;AACT;AACA,QAAQ,OAAO,GAAG;AAClB,IAAI;AACJ,SAAS,MAAM,OAAO;IACpB,OAAO;QACL,IAAI;QACJ,MAAM;QACN,MAAM;QACN,QAAQ;YACN,MAAM;YACN,MAAM;YACN,MAAM;gBACJ,MAAM;gBACN,MAAM;YACR;QACF;QACA,MAAM;QACN,QAAQ,KAAK,KAAK;QAClB,MAAM,yBAAwB,MAAM;YAClC,MAAM,EACJ,UAAU,EACV,GAAG,EACH,QAAQ,EACR,KAAK,EACN,GAAG;YACJ,MAAM,EACJ,IAAI,EACL,GAAG,IAAI,IAAI;YACZ,MAAM,YAAY,CAAC,GAAG,YAAY,eAAe,EAAE,SAAS,MAAM;YAClE,MAAM,SAAS,MAAM,UAAU,QAAQ,CAAC;gBACtC,IAAI;gBACJ,MAAM,SAAS,IAAI;gBACnB,SAAS,CAAC,WAAW,EAAE,MAAM;gBAC7B,MAAM,KAAK;oBACT;oBACA;gBACF;gBACA,MAAM,KAAK;oBACT;oBACA;oBACA;gBACF;YACF;YACA,MAAM,SAAS,OAAO,QAAQ,CAAC,MAAM,CAAC,OAAO,OAAO,EAAE,MAAM,CAAC;YAC7D,IAAI,OAAO,MAAM,EAAE;gBACjB,MAAM,IAAI,MAAM,CAAC,OAAO,EAAE,OAAO,IAAI,CAAC,MAAM,mBAAmB,CAAC;YAClE;QACF;QACA;IACF;AACF;AACA,SAAS,KAAK,MAAM;IAClB,MAAM,EACJ,GAAG,EACH,IAAI,EACJ,KAAK,EACN,GAAG;IACJ,MAAM,cAAc,KAAK,OAAO,CAAC,OAAO;IACxC,MAAM,aAAa,MAAM,UAAU,IAAI;IACvC,MAAM,aAAa,MAAM,UAAU,IAAI;IACvC,MAAM,QAAQ;QACZ,YAAY;QACZ,MAAM;QACN,gBAAgB;QAChB,kBAAkB;QAClB,cAAc;QACd;IACF;IACA,OAAO,CAAC;yBACe,EAAE,MAAM,UAAU,CAAC;;uBAErB,EAAE,MAAM,cAAc,CAAC;;;qGAGuD,EAAE,MAAM,IAAI,CAAC;2BACvF,EAAE,YAAY;;;;;;;oEAO2B,EAAE,MAAM,gBAAgB,CAAC,WAAW,EAAE,IAAI;;0FAEpB,EAAE,MAAM,UAAU,CAAC,mFAAmF,EAAE,MAAM,YAAY,CAAC;;;;;;;;gIAQrF,EAAE,MAAM,IAAI,CAAC;;;;;;AAM7I,CAAC;AACD;AACA,SAAS,KAAK,EACZ,GAAG,EACH,IAAI,EACL;IACC,OAAO,CAAC,WAAW,EAAE,KAAK,EAAE,EAAE,IAAI,IAAI,CAAC;AACzC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3909, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/node_modules/next-auth/providers/credentials.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = Credentials;\nfunction Credentials(options) {\n  return {\n    id: \"credentials\",\n    name: \"Credentials\",\n    type: \"credentials\",\n    credentials: {},\n    authorize: () => null,\n    options\n  };\n}"], "names": [], "mappings": "AAAA;AAEA,OAAO,cAAc,CAAC,SAAS,cAAc;IAC3C,OAAO;AACT;AACA,QAAQ,OAAO,GAAG;AAClB,SAAS,YAAY,OAAO;IAC1B,OAAO;QACL,IAAI;QACJ,MAAM;QACN,MAAM;QACN,aAAa,CAAC;QACd,WAAW,IAAM;QACjB;IACF;AACF", "ignoreList": [0], "debugId": null}}]}
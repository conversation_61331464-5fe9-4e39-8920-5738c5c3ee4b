{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 100, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/lib/mongodb.ts"], "sourcesContent": ["import mongoose from 'mongoose';\n\nconst MONGODB_URI = process.env.MONGODB_URI;\n\nif (!MONGODB_URI) {\n  throw new Error(\n    'Please define the MONGODB_URI environment variable inside .env.local'\n  );\n}\n\n/**\n * Global is used here to maintain a cached connection across hot reloads\n * in development. This prevents connections growing exponentially\n * during API Route usage.\n */\nlet cached = global.mongoose;\n\nif (!cached) {\n  cached = global.mongoose = { conn: null, promise: null };\n}\n\nasync function dbConnect() {\n  if (cached.conn) {\n    return cached.conn;\n  }\n\n  if (!cached.promise) {\n    const opts = {\n      bufferCommands: false,\n    };\n\n    cached.promise = mongoose.connect(MONGODB_URI, opts).then((mongoose) => {\n      return mongoose;\n    });\n  }\n\n  try {\n    cached.conn = await cached.promise;\n  } catch (e) {\n    cached.promise = null;\n    throw e;\n  }\n\n  return cached.conn;\n}\n\nexport default dbConnect;\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,cAAc,QAAQ,GAAG,CAAC,WAAW;AAE3C,IAAI,CAAC,aAAa;IAChB,MAAM,IAAI,MACR;AAEJ;AAEA;;;;CAIC,GACD,IAAI,SAAS,OAAO,QAAQ;AAE5B,IAAI,CAAC,QAAQ;IACX,SAAS,OAAO,QAAQ,GAAG;QAAE,MAAM;QAAM,SAAS;IAAK;AACzD;AAEA,eAAe;IACb,IAAI,OAAO,IAAI,EAAE;QACf,OAAO,OAAO,IAAI;IACpB;IAEA,IAAI,CAAC,OAAO,OAAO,EAAE;QACnB,MAAM,OAAO;YACX,gBAAgB;QAClB;QAEA,OAAO,OAAO,GAAG,yGAAA,CAAA,UAAQ,CAAC,OAAO,CAAC,aAAa,MAAM,IAAI,CAAC,CAAC;YACzD,OAAO;QACT;IACF;IAEA,IAAI;QACF,OAAO,IAAI,GAAG,MAAM,OAAO,OAAO;IACpC,EAAE,OAAO,GAAG;QACV,OAAO,OAAO,GAAG;QACjB,MAAM;IACR;IAEA,OAAO,OAAO,IAAI;AACpB;uCAEe", "debugId": null}}, {"offset": {"line": 147, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/models/User.ts"], "sourcesContent": ["import mongoose, { Document, Schema } from 'mongoose';\n\nexport interface IAccount {\n  provider: 'google' | 'github' | 'email';\n  providerId: string;\n  providerAccountId: string;\n  accessToken?: string;\n  refreshToken?: string;\n  expiresAt?: Date;\n}\n\nexport interface IUser extends Document {\n  email: string;\n  name: string;\n  avatar?: string;\n  role: 'user' | 'admin';\n  isActive: boolean;\n\n  // 认证相关\n  emailVerified: boolean;\n  emailVerificationToken?: string;\n  emailVerificationExpires?: Date;\n\n  // OAuth账户关联\n  accounts: IAccount[];\n\n  // 用户行为\n  submittedTools: string[]; // Tool IDs\n  likedTools: string[]; // Tool IDs\n  comments: string[]; // Comment IDs\n\n  // 时间戳\n  createdAt: Date;\n  updatedAt: Date;\n  lastLoginAt?: Date;\n}\n\nconst AccountSchema = new Schema({\n  provider: {\n    type: String,\n    required: true,\n    enum: ['google', 'github', 'email']\n  },\n  providerId: {\n    type: String,\n    required: true\n  },\n  providerAccountId: {\n    type: String,\n    required: true\n  },\n  accessToken: String,\n  refreshToken: String,\n  expiresAt: Date\n}, { _id: false });\n\nconst UserSchema: Schema = new Schema({\n  email: {\n    type: String,\n    required: [true, 'Email is required'],\n    unique: true,\n    trim: true,\n    lowercase: true,\n    validate: {\n      validator: function(v: string) {\n        return /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(v);\n      },\n      message: 'Please enter a valid email address'\n    }\n  },\n  name: {\n    type: String,\n    required: [true, 'Name is required'],\n    trim: true,\n    maxlength: [100, 'Name cannot exceed 100 characters']\n  },\n  avatar: {\n    type: String,\n    trim: true\n  },\n  role: {\n    type: String,\n    required: true,\n    enum: ['user', 'admin'],\n    default: 'user'\n  },\n  isActive: {\n    type: Boolean,\n    default: true\n  },\n\n  // 认证相关\n  emailVerified: {\n    type: Boolean,\n    default: false\n  },\n  emailVerificationToken: {\n    type: String,\n    trim: true\n  },\n  emailVerificationExpires: {\n    type: Date\n  },\n\n  // OAuth账户关联\n  accounts: [AccountSchema],\n\n  // 用户行为\n  submittedTools: [{\n    type: Schema.Types.ObjectId,\n    ref: 'Tool'\n  }],\n  likedTools: [{\n    type: Schema.Types.ObjectId,\n    ref: 'Tool'\n  }],\n  comments: [{\n    type: Schema.Types.ObjectId,\n    ref: 'Comment'\n  }],\n\n  // 时间戳\n  lastLoginAt: {\n    type: Date\n  }\n}, {\n  timestamps: true,\n  toJSON: { virtuals: true },\n  toObject: { virtuals: true }\n});\n\n// Indexes\nUserSchema.index({ email: 1 });\nUserSchema.index({ role: 1 });\nUserSchema.index({ emailVerificationToken: 1 });\nUserSchema.index({ 'accounts.provider': 1, 'accounts.providerAccountId': 1 });\n\n// 实例方法\nUserSchema.methods.addAccount = function(account: IAccount) {\n  // 检查是否已存在相同的账户\n  const existingAccount = this.accounts.find(\n    (acc: IAccount) => acc.provider === account.provider && acc.providerAccountId === account.providerAccountId\n  );\n\n  if (!existingAccount) {\n    this.accounts.push(account);\n  } else {\n    // 更新现有账户信息\n    Object.assign(existingAccount, account);\n  }\n};\n\nUserSchema.methods.removeAccount = function(provider: string, providerAccountId: string) {\n  this.accounts = this.accounts.filter(\n    (acc: IAccount) => !(acc.provider === provider && acc.providerAccountId === providerAccountId)\n  );\n};\n\nexport default mongoose.models.User || mongoose.model<IUser>('User', UserSchema);\n"], "names": [], "mappings": ";;;AAAA;;AAqCA,MAAM,gBAAgB,IAAI,yGAAA,CAAA,SAAM,CAAC;IAC/B,UAAU;QACR,MAAM;QACN,UAAU;QACV,MAAM;YAAC;YAAU;YAAU;SAAQ;IACrC;IACA,YAAY;QACV,MAAM;QACN,UAAU;IACZ;IACA,mBAAmB;QACjB,MAAM;QACN,UAAU;IACZ;IACA,aAAa;IACb,cAAc;IACd,WAAW;AACb,GAAG;IAAE,KAAK;AAAM;AAEhB,MAAM,aAAqB,IAAI,yGAAA,CAAA,SAAM,CAAC;IACpC,OAAO;QACL,MAAM;QACN,UAAU;YAAC;YAAM;SAAoB;QACrC,QAAQ;QACR,MAAM;QACN,WAAW;QACX,UAAU;YACR,WAAW,SAAS,CAAS;gBAC3B,OAAO,6BAA6B,IAAI,CAAC;YAC3C;YACA,SAAS;QACX;IACF;IACA,MAAM;QACJ,MAAM;QACN,UAAU;YAAC;YAAM;SAAmB;QACpC,MAAM;QACN,WAAW;YAAC;YAAK;SAAoC;IACvD;IACA,QAAQ;QACN,MAAM;QACN,MAAM;IACR;IACA,MAAM;QACJ,MAAM;QACN,UAAU;QACV,MAAM;YAAC;YAAQ;SAAQ;QACvB,SAAS;IACX;IACA,UAAU;QACR,MAAM;QACN,SAAS;IACX;IAEA,OAAO;IACP,eAAe;QACb,MAAM;QACN,SAAS;IACX;IACA,wBAAwB;QACtB,MAAM;QACN,MAAM;IACR;IACA,0BAA0B;QACxB,MAAM;IACR;IAEA,YAAY;IACZ,UAAU;QAAC;KAAc;IAEzB,OAAO;IACP,gBAAgB;QAAC;YACf,MAAM,yGAAA,CAAA,SAAM,CAAC,KAAK,CAAC,QAAQ;YAC3B,KAAK;QACP;KAAE;IACF,YAAY;QAAC;YACX,MAAM,yGAAA,CAAA,SAAM,CAAC,KAAK,CAAC,QAAQ;YAC3B,KAAK;QACP;KAAE;IACF,UAAU;QAAC;YACT,MAAM,yGAAA,CAAA,SAAM,CAAC,KAAK,CAAC,QAAQ;YAC3B,KAAK;QACP;KAAE;IAEF,MAAM;IACN,aAAa;QACX,MAAM;IACR;AACF,GAAG;IACD,YAAY;IACZ,QAAQ;QAAE,UAAU;IAAK;IACzB,UAAU;QAAE,UAAU;IAAK;AAC7B;AAEA,UAAU;AACV,WAAW,KAAK,CAAC;IAAE,OAAO;AAAE;AAC5B,WAAW,KAAK,CAAC;IAAE,MAAM;AAAE;AAC3B,WAAW,KAAK,CAAC;IAAE,wBAAwB;AAAE;AAC7C,WAAW,KAAK,CAAC;IAAE,qBAAqB;IAAG,8BAA8B;AAAE;AAE3E,OAAO;AACP,WAAW,OAAO,CAAC,UAAU,GAAG,SAAS,OAAiB;IACxD,eAAe;IACf,MAAM,kBAAkB,IAAI,CAAC,QAAQ,CAAC,IAAI,CACxC,CAAC,MAAkB,IAAI,QAAQ,KAAK,QAAQ,QAAQ,IAAI,IAAI,iBAAiB,KAAK,QAAQ,iBAAiB;IAG7G,IAAI,CAAC,iBAAiB;QACpB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;IACrB,OAAO;QACL,WAAW;QACX,OAAO,MAAM,CAAC,iBAAiB;IACjC;AACF;AAEA,WAAW,OAAO,CAAC,aAAa,GAAG,SAAS,QAAgB,EAAE,iBAAyB;IACrF,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAClC,CAAC,MAAkB,CAAC,CAAC,IAAI,QAAQ,KAAK,YAAY,IAAI,iBAAiB,KAAK,iBAAiB;AAEjG;uCAEe,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,IAAI,IAAI,yGAAA,CAAA,UAAQ,CAAC,KAAK,CAAQ,QAAQ", "debugId": null}}, {"offset": {"line": 305, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/app/api/auth/custom-login/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport jwt from 'jsonwebtoken';\nimport dbConnect from '../../../../lib/mongodb';\nimport User from '../../../../models/User';\n\nexport async function POST(request: NextRequest) {\n  try {\n    const { email, code, token } = await request.json();\n    console.log('Custom login request:', { email, code, token: token ? 'present' : 'missing' });\n\n    if (!email || !code || !token) {\n      console.log('Missing parameters:', { email: !!email, code: !!code, token: !!token });\n      return NextResponse.json({\n        success: false,\n        error: '缺少必要参数'\n      }, { status: 400 });\n    }\n\n    // 验证token\n    let decoded;\n    try {\n      decoded = jwt.verify(token, process.env.NEXTAUTH_SECRET!) as any;\n      console.log('Token decoded successfully:', decoded);\n    } catch (error) {\n      console.log('Token verification failed:', error);\n      return NextResponse.json({\n        success: false,\n        error: '验证token无效或已过期'\n      }, { status: 400 });\n    }\n\n    if (decoded.email !== email) {\n      console.log('Email mismatch:', { decoded: decoded.email, provided: email });\n      return NextResponse.json({\n        success: false,\n        error: '邮箱不匹配'\n      }, { status: 400 });\n    }\n\n    // 验证验证码\n    if (decoded.code !== code) {\n      console.log('Code mismatch:', { decoded: decoded.code, provided: code });\n      return NextResponse.json({\n        success: false,\n        error: '验证码错误'\n      }, { status: 400 });\n    }\n\n    // 检查验证码是否过期（5分钟）\n    if (Date.now() - decoded.timestamp > 5 * 60 * 1000) {\n      console.log('Code expired:', { timestamp: decoded.timestamp, now: Date.now(), diff: Date.now() - decoded.timestamp });\n      return NextResponse.json({\n        success: false,\n        error: '验证码已过期'\n      }, { status: 400 });\n    }\n\n    // 连接数据库\n    await dbConnect();\n\n    // 查找或创建用户\n    let user = await User.findOne({ email });\n    if (!user) {\n      user = new User({\n        email,\n        name: email.split('@')[0],\n        emailVerified: true,\n        lastLoginAt: new Date(),\n      });\n      await user.save();\n    } else {\n      user.lastLoginAt = new Date();\n      await user.save();\n    }\n\n    // 创建NextAuth兼容的JWT token\n    const nextAuthToken = jwt.sign(\n      {\n        sub: user._id.toString(),\n        email: user.email,\n        name: user.name,\n        picture: user.avatar,\n        iat: Math.floor(Date.now() / 1000),\n        exp: Math.floor(Date.now() / 1000) + (30 * 24 * 60 * 60), // 30天\n        jti: Math.random().toString(36).substring(2),\n      },\n      process.env.NEXTAUTH_SECRET!\n    );\n\n    // 设置NextAuth session cookie\n    const response = NextResponse.json({\n      success: true,\n      message: '登录成功',\n      user: {\n        id: user._id.toString(),\n        email: user.email,\n        name: user.name,\n        image: user.avatar,\n        role: user.role,\n      }\n    });\n\n    // 设置NextAuth session cookie\n    response.cookies.set('next-auth.session-token', nextAuthToken, {\n      httpOnly: true,\n      secure: process.env.NODE_ENV === 'production',\n      sameSite: 'lax',\n      maxAge: 30 * 24 * 60 * 60, // 30天\n      path: '/',\n    });\n\n    return response;\n\n  } catch (error) {\n    console.error('Custom login error:', error);\n    return NextResponse.json({\n      success: false,\n      error: '服务器错误'\n    }, { status: 500 });\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;;;;;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,QAAQ,IAAI;QACjD,QAAQ,GAAG,CAAC,yBAAyB;YAAE;YAAO;YAAM,OAAO,QAAQ,YAAY;QAAU;QAEzF,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,OAAO;YAC7B,QAAQ,GAAG,CAAC,uBAAuB;gBAAE,OAAO,CAAC,CAAC;gBAAO,MAAM,CAAC,CAAC;gBAAM,OAAO,CAAC,CAAC;YAAM;YAClF,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,OAAO;YACT,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,UAAU;QACV,IAAI;QACJ,IAAI;YACF,UAAU,uIAAA,CAAA,UAAG,CAAC,MAAM,CAAC,OAAO,QAAQ,GAAG,CAAC,eAAe;YACvD,QAAQ,GAAG,CAAC,+BAA+B;QAC7C,EAAE,OAAO,OAAO;YACd,QAAQ,GAAG,CAAC,8BAA8B;YAC1C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,OAAO;YACT,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,IAAI,QAAQ,KAAK,KAAK,OAAO;YAC3B,QAAQ,GAAG,CAAC,mBAAmB;gBAAE,SAAS,QAAQ,KAAK;gBAAE,UAAU;YAAM;YACzE,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,OAAO;YACT,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,QAAQ;QACR,IAAI,QAAQ,IAAI,KAAK,MAAM;YACzB,QAAQ,GAAG,CAAC,kBAAkB;gBAAE,SAAS,QAAQ,IAAI;gBAAE,UAAU;YAAK;YACtE,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,OAAO;YACT,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,iBAAiB;QACjB,IAAI,KAAK,GAAG,KAAK,QAAQ,SAAS,GAAG,IAAI,KAAK,MAAM;YAClD,QAAQ,GAAG,CAAC,iBAAiB;gBAAE,WAAW,QAAQ,SAAS;gBAAE,KAAK,KAAK,GAAG;gBAAI,MAAM,KAAK,GAAG,KAAK,QAAQ,SAAS;YAAC;YACnH,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,OAAO;YACT,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,QAAQ;QACR,MAAM,CAAA,GAAA,uHAAA,CAAA,UAAS,AAAD;QAEd,UAAU;QACV,IAAI,OAAO,MAAM,uHAAA,CAAA,UAAI,CAAC,OAAO,CAAC;YAAE;QAAM;QACtC,IAAI,CAAC,MAAM;YACT,OAAO,IAAI,uHAAA,CAAA,UAAI,CAAC;gBACd;gBACA,MAAM,MAAM,KAAK,CAAC,IAAI,CAAC,EAAE;gBACzB,eAAe;gBACf,aAAa,IAAI;YACnB;YACA,MAAM,KAAK,IAAI;QACjB,OAAO;YACL,KAAK,WAAW,GAAG,IAAI;YACvB,MAAM,KAAK,IAAI;QACjB;QAEA,yBAAyB;QACzB,MAAM,gBAAgB,uIAAA,CAAA,UAAG,CAAC,IAAI,CAC5B;YACE,KAAK,KAAK,GAAG,CAAC,QAAQ;YACtB,OAAO,KAAK,KAAK;YACjB,MAAM,KAAK,IAAI;YACf,SAAS,KAAK,MAAM;YACpB,KAAK,KAAK,KAAK,CAAC,KAAK,GAAG,KAAK;YAC7B,KAAK,KAAK,KAAK,CAAC,KAAK,GAAG,KAAK,QAAS,KAAK,KAAK,KAAK;YACrD,KAAK,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC;QAC5C,GACA,QAAQ,GAAG,CAAC,eAAe;QAG7B,4BAA4B;QAC5B,MAAM,WAAW,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACjC,SAAS;YACT,SAAS;YACT,MAAM;gBACJ,IAAI,KAAK,GAAG,CAAC,QAAQ;gBACrB,OAAO,KAAK,KAAK;gBACjB,MAAM,KAAK,IAAI;gBACf,OAAO,KAAK,MAAM;gBAClB,MAAM,KAAK,IAAI;YACjB;QACF;QAEA,4BAA4B;QAC5B,SAAS,OAAO,CAAC,GAAG,CAAC,2BAA2B,eAAe;YAC7D,UAAU;YACV,QAAQ,oDAAyB;YACjC,UAAU;YACV,QAAQ,KAAK,KAAK,KAAK;YACvB,MAAM;QACR;QAEA,OAAO;IAET,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uBAAuB;QACrC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,OAAO;QACT,GAAG;YAAE,QAAQ;QAAI;IACnB;AACF", "debugId": null}}]}
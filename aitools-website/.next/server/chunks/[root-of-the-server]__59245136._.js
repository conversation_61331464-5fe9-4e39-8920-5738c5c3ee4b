module.exports = {

"[project]/.next-internal/server/app/api/admin/stats/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/mongoose [external] (mongoose, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("mongoose", () => require("mongoose"));

module.exports = mod;
}}),
"[project]/src/lib/mongodb.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/mongoose [external] (mongoose, cjs)");
;
const MONGODB_URI = process.env.MONGODB_URI;
if (!MONGODB_URI) {
    throw new Error('Please define the MONGODB_URI environment variable inside .env.local');
}
/**
 * Global is used here to maintain a cached connection across hot reloads
 * in development. This prevents connections growing exponentially
 * during API Route usage.
 */ let cached = global.mongoose;
if (!cached) {
    cached = global.mongoose = {
        conn: null,
        promise: null
    };
}
async function dbConnect() {
    if (cached.conn) {
        return cached.conn;
    }
    if (!cached.promise) {
        const opts = {
            bufferCommands: false
        };
        cached.promise = __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].connect(MONGODB_URI, opts).then((mongoose)=>{
            return mongoose;
        });
    }
    try {
        cached.conn = await cached.promise;
    } catch (e) {
        cached.promise = null;
        throw e;
    }
    return cached.conn;
}
const __TURBOPACK__default__export__ = dbConnect;
}}),
"[project]/src/models/Tool.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/mongoose [external] (mongoose, cjs)");
;
const ToolSchema = new __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["Schema"]({
    name: {
        type: String,
        required: [
            true,
            'Tool name is required'
        ],
        trim: true,
        maxlength: [
            100,
            'Tool name cannot exceed 100 characters'
        ]
    },
    description: {
        type: String,
        required: [
            true,
            'Tool description is required'
        ],
        trim: true,
        maxlength: [
            500,
            'Description cannot exceed 500 characters'
        ]
    },
    longDescription: {
        type: String,
        trim: true,
        maxlength: [
            2000,
            'Long description cannot exceed 2000 characters'
        ]
    },
    website: {
        type: String,
        required: [
            true,
            'Website URL is required'
        ],
        trim: true,
        validate: {
            validator: function(v) {
                return /^https?:\/\/.+/.test(v);
            },
            message: 'Please enter a valid URL'
        }
    },
    logo: {
        type: String,
        trim: true
    },
    category: {
        type: String,
        required: [
            true,
            'Category is required'
        ],
        enum: [
            'text-generation',
            'image-generation',
            'video-generation',
            'audio-generation',
            'code-generation',
            'data-analysis',
            'productivity',
            'design',
            'marketing',
            'education',
            'research',
            'other'
        ]
    },
    tags: [
        {
            type: String,
            trim: true,
            lowercase: true
        }
    ],
    pricing: {
        type: String,
        required: [
            true,
            'Pricing model is required'
        ],
        enum: [
            'free',
            'freemium',
            'paid'
        ]
    },
    pricingDetails: {
        type: String,
        trim: true,
        maxlength: [
            500,
            'Pricing details cannot exceed 500 characters'
        ]
    },
    features: [
        {
            type: String,
            trim: true,
            maxlength: [
                200,
                'Feature description cannot exceed 200 characters'
            ]
        }
    ],
    screenshots: [
        {
            type: String,
            trim: true
        }
    ],
    submittedBy: {
        type: String,
        required: [
            true,
            'Submitter ID is required'
        ],
        trim: true
    },
    submittedAt: {
        type: Date,
        default: Date.now
    },
    publishedAt: {
        type: Date
    },
    status: {
        type: String,
        required: true,
        enum: [
            'pending',
            'approved',
            'rejected'
        ],
        default: 'pending'
    },
    reviewNotes: {
        type: String,
        trim: true,
        maxlength: [
            1000,
            'Review notes cannot exceed 1000 characters'
        ]
    },
    reviewedBy: {
        type: String,
        trim: true
    },
    reviewedAt: {
        type: Date
    },
    views: {
        type: Number,
        default: 0,
        min: 0
    },
    likes: {
        type: Number,
        default: 0,
        min: 0
    },
    isActive: {
        type: Boolean,
        default: true
    }
}, {
    timestamps: true,
    toJSON: {
        virtuals: true
    },
    toObject: {
        virtuals: true
    }
});
// Indexes for better query performance
ToolSchema.index({
    status: 1,
    isActive: 1
});
ToolSchema.index({
    category: 1,
    status: 1
});
ToolSchema.index({
    tags: 1,
    status: 1
});
ToolSchema.index({
    submittedBy: 1
});
ToolSchema.index({
    publishedAt: -1
});
ToolSchema.index({
    views: -1
});
ToolSchema.index({
    likes: -1
});
// Text search index
ToolSchema.index({
    name: 'text',
    description: 'text',
    longDescription: 'text',
    tags: 'text'
});
const __TURBOPACK__default__export__ = __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].models.Tool || __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].model('Tool', ToolSchema);
}}),
"[project]/src/app/api/admin/stats/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "GET": (()=>GET)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mongodb$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/mongodb.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Tool$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/models/Tool.ts [app-route] (ecmascript)");
;
;
;
async function GET(request) {
    try {
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mongodb$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])();
        const { searchParams } = new URL(request.url);
        const timeRange = searchParams.get('timeRange') || '7d';
        // 计算时间范围
        const now = new Date();
        let startDate;
        switch(timeRange){
            case '1d':
                startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000);
                break;
            case '7d':
                startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
                break;
            case '30d':
                startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
                break;
            case '90d':
                startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
                break;
            default:
                startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        }
        // 基础统计
        const [totalTools, pendingTools, approvedTools, rejectedTools, totalViews, totalLikes, recentSubmissions, recentApprovals, recentRejections] = await Promise.all([
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Tool$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].countDocuments(),
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Tool$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].countDocuments({
                status: 'pending'
            }),
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Tool$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].countDocuments({
                status: 'approved'
            }),
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Tool$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].countDocuments({
                status: 'rejected'
            }),
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Tool$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].aggregate([
                {
                    $group: {
                        _id: null,
                        total: {
                            $sum: '$views'
                        }
                    }
                }
            ]),
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Tool$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].aggregate([
                {
                    $group: {
                        _id: null,
                        total: {
                            $sum: '$likes'
                        }
                    }
                }
            ]),
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Tool$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].countDocuments({
                submittedAt: {
                    $gte: startDate
                }
            }),
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Tool$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].countDocuments({
                status: 'approved',
                reviewedAt: {
                    $gte: startDate
                }
            }),
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Tool$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].countDocuments({
                status: 'rejected',
                reviewedAt: {
                    $gte: startDate
                }
            })
        ]);
        // 分类统计
        const categoryStats = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Tool$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].aggregate([
            {
                $match: {
                    status: 'approved'
                }
            },
            {
                $group: {
                    _id: '$category',
                    count: {
                        $sum: 1
                    },
                    totalViews: {
                        $sum: '$views'
                    },
                    totalLikes: {
                        $sum: '$likes'
                    }
                }
            },
            {
                $sort: {
                    count: -1
                }
            }
        ]);
        // 热门工具
        const topTools = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Tool$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].find({
            status: 'approved'
        }).sort({
            views: -1
        }).limit(10).select('name category views likes').lean();
        // 最近活动
        const recentActivity = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Tool$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].find({
            $or: [
                {
                    submittedAt: {
                        $gte: startDate
                    }
                },
                {
                    reviewedAt: {
                        $gte: startDate
                    }
                }
            ]
        }).sort({
            updatedAt: -1
        }).limit(20).select('name status submittedAt reviewedAt submittedBy reviewedBy').lean();
        // 每日统计（最近7天）
        const dailyStats = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Tool$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].aggregate([
            {
                $match: {
                    submittedAt: {
                        $gte: new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
                    }
                }
            },
            {
                $group: {
                    _id: {
                        date: {
                            $dateToString: {
                                format: '%Y-%m-%d',
                                date: '$submittedAt'
                            }
                        },
                        status: '$status'
                    },
                    count: {
                        $sum: 1
                    }
                }
            },
            {
                $sort: {
                    '_id.date': 1
                }
            }
        ]);
        // 处理每日统计数据
        const last7Days = [];
        for(let i = 6; i >= 0; i--){
            const date = new Date(now.getTime() - i * 24 * 60 * 60 * 1000);
            const dateStr = date.toISOString().split('T')[0];
            const dayName = date.toLocaleDateString('zh-CN', {
                weekday: 'short'
            });
            const dayStats = dailyStats.filter((stat)=>stat._id.date === dateStr);
            const submissions = dayStats.find((s)=>s._id.status === 'pending')?.count || 0;
            const approvals = dayStats.find((s)=>s._id.status === 'approved')?.count || 0;
            const rejections = dayStats.find((s)=>s._id.status === 'rejected')?.count || 0;
            last7Days.push({
                date: dateStr,
                day: dayName,
                submissions,
                approvals,
                rejections
            });
        }
        // 审核效率统计
        const avgReviewTime = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Tool$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].aggregate([
            {
                $match: {
                    status: {
                        $in: [
                            'approved',
                            'rejected'
                        ]
                    },
                    reviewedAt: {
                        $exists: true
                    },
                    submittedAt: {
                        $exists: true
                    }
                }
            },
            {
                $project: {
                    reviewTime: {
                        $divide: [
                            {
                                $subtract: [
                                    '$reviewedAt',
                                    '$submittedAt'
                                ]
                            },
                            1000 * 60 * 60 // 转换为小时
                        ]
                    }
                }
            },
            {
                $group: {
                    _id: null,
                    avgReviewTime: {
                        $avg: '$reviewTime'
                    }
                }
            }
        ]);
        const overview = {
            totalTools,
            pendingTools,
            approvedTools,
            rejectedTools,
            totalViews: totalViews[0]?.total || 0,
            totalLikes: totalLikes[0]?.total || 0,
            recentSubmissions,
            recentApprovals,
            recentRejections,
            avgReviewTime: avgReviewTime[0]?.avgReviewTime || 0
        };
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true,
            data: {
                overview,
                categoryStats,
                topTools,
                recentActivity,
                dailyStats: last7Days,
                timeRange
            }
        });
    } catch (error) {
        console.error('Error fetching admin stats:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: false,
            error: '获取统计数据失败'
        }, {
            status: 500
        });
    }
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__59245136._.js.map
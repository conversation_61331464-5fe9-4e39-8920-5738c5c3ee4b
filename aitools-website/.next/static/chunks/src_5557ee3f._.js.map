{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/Layout.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Link from 'next/link';\nimport { Search, Menu, X } from 'lucide-react';\n\ninterface LayoutProps {\n  children: React.ReactNode;\n}\n\nconst Layout: React.FC<LayoutProps> = ({ children }) => {\n  const [isMenuOpen, setIsMenuOpen] = React.useState(false);\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header */}\n      <header className=\"bg-white shadow-sm border-b border-gray-200\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center h-16\">\n            {/* Logo */}\n            <div className=\"flex items-center\">\n              <Link href=\"/\" className=\"flex items-center space-x-2\">\n                <div className=\"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center\">\n                  <span className=\"text-white font-bold text-sm\">AI</span>\n                </div>\n                <span className=\"text-xl font-bold text-gray-900\">AI Tools</span>\n              </Link>\n            </div>\n\n            {/* Desktop Navigation */}\n            <nav className=\"hidden md:flex items-center space-x-8\">\n              <Link href=\"/\" className=\"text-gray-700 hover:text-blue-600 transition-colors\">\n                首页\n              </Link>\n              <Link href=\"/tools\" className=\"text-gray-700 hover:text-blue-600 transition-colors\">\n                工具目录\n              </Link>\n              <Link href=\"/categories\" className=\"text-gray-700 hover:text-blue-600 transition-colors\">\n                分类\n              </Link>\n              <Link href=\"/submit\" className=\"text-gray-700 hover:text-blue-600 transition-colors\">\n                提交工具\n              </Link>\n              <Link href=\"/dashboard\" className=\"text-gray-700 hover:text-blue-600 transition-colors\">\n                仪表板\n              </Link>\n              {/* Admin Navigation */}\n              <div className=\"relative group\">\n                <button className=\"text-gray-700 hover:text-blue-600 transition-colors flex items-center\">\n                  管理员\n                  <svg className=\"w-4 h-4 ml-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 9l-7 7-7-7\" />\n                  </svg>\n                </button>\n                <div className=\"absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg border border-gray-200 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50\">\n                  <div className=\"py-1\">\n                    <Link href=\"/admin\" className=\"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors\">\n                      审核中心\n                    </Link>\n                    <Link href=\"/admin/dashboard\" className=\"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors\">\n                      统计面板\n                    </Link>\n                  </div>\n                </div>\n              </div>\n            </nav>\n\n            {/* Search Bar */}\n            <div className=\"hidden md:flex items-center flex-1 max-w-md mx-8\">\n              <div className=\"relative w-full\">\n                <input\n                  type=\"text\"\n                  placeholder=\"搜索 AI 工具...\"\n                  className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                />\n                <Search className=\"absolute left-3 top-2.5 h-5 w-5 text-gray-400\" />\n              </div>\n            </div>\n\n            {/* Mobile menu button */}\n            <div className=\"md:hidden\">\n              <button\n                onClick={() => setIsMenuOpen(!isMenuOpen)}\n                className=\"text-gray-700 hover:text-blue-600\"\n              >\n                {isMenuOpen ? <X className=\"h-6 w-6\" /> : <Menu className=\"h-6 w-6\" />}\n              </button>\n            </div>\n          </div>\n        </div>\n\n        {/* Mobile Navigation */}\n        {isMenuOpen && (\n          <div className=\"md:hidden bg-white border-t border-gray-200\">\n            <div className=\"px-4 py-2 space-y-1\">\n              <Link\n                href=\"/\"\n                className=\"block px-3 py-2 text-gray-700 hover:text-blue-600 hover:bg-gray-50 rounded-md\"\n              >\n                首页\n              </Link>\n              <Link\n                href=\"/tools\"\n                className=\"block px-3 py-2 text-gray-700 hover:text-blue-600 hover:bg-gray-50 rounded-md\"\n              >\n                工具目录\n              </Link>\n              <Link\n                href=\"/categories\"\n                className=\"block px-3 py-2 text-gray-700 hover:text-blue-600 hover:bg-gray-50 rounded-md\"\n              >\n                分类\n              </Link>\n              <Link\n                href=\"/submit\"\n                className=\"block px-3 py-2 text-gray-700 hover:text-blue-600 hover:bg-gray-50 rounded-md\"\n              >\n                提交工具\n              </Link>\n              <Link\n                href=\"/dashboard\"\n                className=\"block px-3 py-2 text-gray-700 hover:text-blue-600 hover:bg-gray-50 rounded-md\"\n              >\n                仪表板\n              </Link>\n              {/* Admin Links */}\n              <div className=\"border-t border-gray-200 pt-4 mt-4\">\n                <div className=\"px-3 py-2 text-sm font-medium text-gray-500\">管理员</div>\n                <Link\n                  href=\"/admin\"\n                  className=\"block px-3 py-2 text-gray-700 hover:text-blue-600 hover:bg-gray-50 rounded-md\"\n                >\n                  审核中心\n                </Link>\n                <Link\n                  href=\"/admin/dashboard\"\n                  className=\"block px-3 py-2 text-gray-700 hover:text-blue-600 hover:bg-gray-50 rounded-md\"\n                >\n                  统计面板\n                </Link>\n              </div>\n              {/* Mobile Search */}\n              <div className=\"px-3 py-2\">\n                <div className=\"relative\">\n                  <input\n                    type=\"text\"\n                    placeholder=\"搜索 AI 工具...\"\n                    className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                  />\n                  <Search className=\"absolute left-3 top-2.5 h-5 w-5 text-gray-400\" />\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n      </header>\n\n      {/* Main Content */}\n      <main className=\"flex-1\">\n        {children}\n      </main>\n\n      {/* Footer */}\n      <footer className=\"bg-white border-t border-gray-200 mt-16\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n          <div className=\"grid grid-cols-1 md:grid-cols-4 gap-8\">\n            <div className=\"col-span-1 md:col-span-2\">\n              <div className=\"flex items-center space-x-2 mb-4\">\n                <div className=\"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center\">\n                  <span className=\"text-white font-bold text-sm\">AI</span>\n                </div>\n                <span className=\"text-xl font-bold text-gray-900\">AI Tools</span>\n              </div>\n              <p className=\"text-gray-600 mb-4\">\n                发现最新最好的 AI 工具，提升您的工作效率和创造力。\n              </p>\n            </div>\n            \n            <div>\n              <h3 className=\"text-sm font-semibold text-gray-900 tracking-wider uppercase mb-4\">\n                快速链接\n              </h3>\n              <ul className=\"space-y-2\">\n                <li>\n                  <Link href=\"/tools\" className=\"text-gray-600 hover:text-blue-600\">\n                    工具目录\n                  </Link>\n                </li>\n                <li>\n                  <Link href=\"/categories\" className=\"text-gray-600 hover:text-blue-600\">\n                    分类浏览\n                  </Link>\n                </li>\n                <li>\n                  <Link href=\"/submit\" className=\"text-gray-600 hover:text-blue-600\">\n                    提交工具\n                  </Link>\n                </li>\n              </ul>\n            </div>\n            \n            <div>\n              <h3 className=\"text-sm font-semibold text-gray-900 tracking-wider uppercase mb-4\">\n                支持\n              </h3>\n              <ul className=\"space-y-2\">\n                <li>\n                  <a href=\"#\" className=\"text-gray-600 hover:text-blue-600\">\n                    帮助中心\n                  </a>\n                </li>\n                <li>\n                  <a href=\"#\" className=\"text-gray-600 hover:text-blue-600\">\n                    联系我们\n                  </a>\n                </li>\n                <li>\n                  <a href=\"#\" className=\"text-gray-600 hover:text-blue-600\">\n                    隐私政策\n                  </a>\n                </li>\n              </ul>\n            </div>\n          </div>\n          \n          <div className=\"border-t border-gray-200 mt-8 pt-8\">\n            <p className=\"text-center text-gray-600\">\n              © 2024 AI Tools Directory. All rights reserved.\n            </p>\n          </div>\n        </div>\n      </footer>\n    </div>\n  );\n};\n\nexport default Layout;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;;;AAJA;;;;AAUA,MAAM,SAAgC,CAAC,EAAE,QAAQ,EAAE;;IACjD,MAAM,CAAC,YAAY,cAAc,GAAG,6JAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IAEnD,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAO,WAAU;;kCAChB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAI,WAAU;;0DACvB,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAK,WAAU;8DAA+B;;;;;;;;;;;0DAEjD,6LAAC;gDAAK,WAAU;0DAAkC;;;;;;;;;;;;;;;;;8CAKtD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAI,WAAU;sDAAsD;;;;;;sDAG/E,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAS,WAAU;sDAAsD;;;;;;sDAGpF,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAc,WAAU;sDAAsD;;;;;;sDAGzF,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAU,WAAU;sDAAsD;;;;;;sDAGrF,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAa,WAAU;sDAAsD;;;;;;sDAIxF,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAO,WAAU;;wDAAwE;sEAExF,6LAAC;4DAAI,WAAU;4DAAe,MAAK;4DAAO,QAAO;4DAAe,SAAQ;sEACtE,cAAA,6LAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAG,GAAE;;;;;;;;;;;;;;;;;8DAGzE,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,+JAAA,CAAA,UAAI;gEAAC,MAAK;gEAAS,WAAU;0EAA4E;;;;;;0EAG1G,6LAAC,+JAAA,CAAA,UAAI;gEAAC,MAAK;gEAAmB,WAAU;0EAA4E;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAS5H,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,MAAK;gDACL,aAAY;gDACZ,WAAU;;;;;;0DAEZ,6LAAC,yMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;;;;;;;;;;;;8CAKtB,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCACC,SAAS,IAAM,cAAc,CAAC;wCAC9B,WAAU;kDAET,2BAAa,6LAAC,+LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;iEAAe,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAOjE,4BACC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAID,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAA8C;;;;;;sDAC7D,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;sDAGD,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;;;;;;;8CAKH,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,MAAK;gDACL,aAAY;gDACZ,WAAU;;;;;;0DAEZ,6LAAC,yMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS9B,6LAAC;gBAAK,WAAU;0BACb;;;;;;0BAIH,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAK,WAAU;kEAA+B;;;;;;;;;;;8DAEjD,6LAAC;oDAAK,WAAU;8DAAkC;;;;;;;;;;;;sDAEpD,6LAAC;4CAAE,WAAU;sDAAqB;;;;;;;;;;;;8CAKpC,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAoE;;;;;;sDAGlF,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;8DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAS,WAAU;kEAAoC;;;;;;;;;;;8DAIpE,6LAAC;8DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAc,WAAU;kEAAoC;;;;;;;;;;;8DAIzE,6LAAC;8DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAU,WAAU;kEAAoC;;;;;;;;;;;;;;;;;;;;;;;8CAOzE,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAoE;;;;;;sDAGlF,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;8DACC,cAAA,6LAAC;wDAAE,MAAK;wDAAI,WAAU;kEAAoC;;;;;;;;;;;8DAI5D,6LAAC;8DACC,cAAA,6LAAC;wDAAE,MAAK;wDAAI,WAAU;kEAAoC;;;;;;;;;;;8DAI5D,6LAAC;8DACC,cAAA,6LAAC;wDAAE,MAAK;wDAAI,WAAU;kEAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQlE,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAE,WAAU;0CAA4B;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQrD;GAhOM;KAAA;uCAkOS", "debugId": null}}, {"offset": {"line": 666, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/LoadingSpinner.tsx"], "sourcesContent": ["'use client';\n\ninterface LoadingSpinnerProps {\n  size?: 'sm' | 'md' | 'lg';\n  className?: string;\n}\n\nexport default function LoadingSpinner({ size = 'md', className = '' }: LoadingSpinnerProps) {\n  const sizeClasses = {\n    sm: 'w-4 h-4',\n    md: 'w-8 h-8',\n    lg: 'w-12 h-12'\n  };\n\n  return (\n    <div className={`flex justify-center items-center ${className}`}>\n      <div className={`${sizeClasses[size]} border-4 border-gray-200 border-t-blue-600 rounded-full animate-spin`}></div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAOe,SAAS,eAAe,EAAE,OAAO,IAAI,EAAE,YAAY,EAAE,EAAuB;IACzF,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAC,iCAAiC,EAAE,WAAW;kBAC7D,cAAA,6LAAC;YAAI,WAAW,GAAG,WAAW,CAAC,KAAK,CAAC,qEAAqE,CAAC;;;;;;;;;;;AAGjH;KAZwB", "debugId": null}}, {"offset": {"line": 705, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/ErrorMessage.tsx"], "sourcesContent": ["'use client';\n\nimport { AlertCircle, X } from 'lucide-react';\n\ninterface ErrorMessageProps {\n  message: string;\n  onClose?: () => void;\n  className?: string;\n}\n\nexport default function ErrorMessage({ message, onClose, className = '' }: ErrorMessageProps) {\n  return (\n    <div className={`bg-red-50 border border-red-200 rounded-lg p-4 ${className}`}>\n      <div className=\"flex items-start\">\n        <AlertCircle className=\"w-5 h-5 text-red-500 mt-0.5 mr-3 flex-shrink-0\" />\n        <div className=\"flex-1\">\n          <p className=\"text-red-800 text-sm\">{message}</p>\n        </div>\n        {onClose && (\n          <button\n            onClick={onClose}\n            className=\"ml-3 text-red-400 hover:text-red-600 transition-colors\"\n          >\n            <X className=\"w-4 h-4\" />\n          </button>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAFA;;;AAUe,SAAS,aAAa,EAAE,OAAO,EAAE,OAAO,EAAE,YAAY,EAAE,EAAqB;IAC1F,qBACE,6LAAC;QAAI,WAAW,CAAC,+CAA+C,EAAE,WAAW;kBAC3E,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC,uNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;8BACvB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;gBAEtC,yBACC,6LAAC;oBACC,SAAS;oBACT,WAAU;8BAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;wBAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;AAMzB;KAnBwB", "debugId": null}}, {"offset": {"line": 781, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/lib/api.ts"], "sourcesContent": ["// API客户端工具类\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3001/api';\n\nexport interface ApiResponse<T = any> {\n  success: boolean;\n  data?: T;\n  error?: string;\n  message?: string;\n  details?: string[];\n}\n\nexport interface PaginationInfo {\n  currentPage: number;\n  totalPages: number;\n  totalItems: number;\n  itemsPerPage: number;\n  hasNextPage: boolean;\n  hasPrevPage: boolean;\n}\n\nexport interface Tool {\n  _id: string;\n  name: string;\n  description: string;\n  longDescription?: string;\n  website: string;\n  logo?: string;\n  category: string;\n  tags: string[];\n  pricing: 'free' | 'freemium' | 'paid';\n  pricingDetails?: string;\n  features: string[];\n  screenshots?: string[];\n  submittedBy: string;\n  submittedAt: string;\n  publishedAt?: string;\n  status: 'pending' | 'approved' | 'rejected';\n  reviewNotes?: string;\n  reviewedBy?: string;\n  reviewedAt?: string;\n  views: number;\n  likes: number;\n  isActive: boolean;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface ToolsResponse {\n  tools: Tool[];\n  pagination: PaginationInfo;\n  stats?: {\n    total: number;\n    pending: number;\n    approved: number;\n    rejected: number;\n  };\n}\n\nexport interface AdminStats {\n  overview: {\n    totalTools: number;\n    pendingTools: number;\n    approvedTools: number;\n    rejectedTools: number;\n    totalViews: number;\n    totalLikes: number;\n    recentSubmissions: number;\n    recentApprovals: number;\n    recentRejections: number;\n    avgReviewTime: number;\n  };\n  categoryStats: Array<{\n    _id: string;\n    count: number;\n    totalViews: number;\n    totalLikes: number;\n  }>;\n  topTools: Array<{\n    _id: string;\n    name: string;\n    category: string;\n    views: number;\n    likes: number;\n  }>;\n  recentActivity: Array<{\n    _id: string;\n    name: string;\n    submittedBy: string;\n    submittedAt: string;\n    status: string;\n    reviewedAt?: string;\n    reviewedBy?: string;\n  }>;\n  dailyStats: Array<{\n    date: string;\n    day: string;\n    submissions: number;\n    approvals: number;\n    rejections: number;\n  }>;\n  timeRange: string;\n}\n\nexport interface Category {\n  id: string;\n  name: string;\n  count: number;\n  totalViews: number;\n  totalLikes: number;\n}\n\nexport interface CategoriesResponse {\n  categories: Category[];\n  overview: {\n    totalTools: number;\n    totalViews: number;\n    totalLikes: number;\n  };\n}\n\nclass ApiClient {\n  private baseURL: string;\n\n  constructor(baseURL: string = API_BASE_URL) {\n    this.baseURL = baseURL;\n  }\n\n  private async request<T>(\n    endpoint: string,\n    options: RequestInit = {}\n  ): Promise<ApiResponse<T>> {\n    try {\n      const url = `${this.baseURL}${endpoint}`;\n      const config: RequestInit = {\n        headers: {\n          'Content-Type': 'application/json',\n          ...options.headers,\n        },\n        ...options,\n      };\n\n      const response = await fetch(url, config);\n      const data = await response.json();\n\n      if (!response.ok) {\n        throw new Error(data.error || `HTTP error! status: ${response.status}`);\n      }\n\n      return data;\n    } catch (error) {\n      console.error('API request failed:', error);\n      return {\n        success: false,\n        error: error instanceof Error ? error.message : '请求失败',\n      };\n    }\n  }\n\n  // 工具相关API\n  async getTools(params?: {\n    page?: number;\n    limit?: number;\n    category?: string;\n    status?: string;\n    search?: string;\n    sort?: string;\n    order?: string;\n  }): Promise<ApiResponse<ToolsResponse>> {\n    const searchParams = new URLSearchParams();\n    if (params) {\n      Object.entries(params).forEach(([key, value]) => {\n        if (value !== undefined) {\n          searchParams.append(key, value.toString());\n        }\n      });\n    }\n    const query = searchParams.toString();\n    return this.request<ToolsResponse>(`/tools${query ? `?${query}` : ''}`);\n  }\n\n  async getTool(id: string): Promise<ApiResponse<Tool>> {\n    return this.request<Tool>(`/tools/${id}`);\n  }\n\n  async createTool(toolData: Partial<Tool>): Promise<ApiResponse<Tool>> {\n    return this.request<Tool>('/tools', {\n      method: 'POST',\n      body: JSON.stringify(toolData),\n    });\n  }\n\n  async updateTool(id: string, toolData: Partial<Tool>): Promise<ApiResponse<Tool>> {\n    return this.request<Tool>(`/tools/${id}`, {\n      method: 'PUT',\n      body: JSON.stringify(toolData),\n    });\n  }\n\n  async deleteTool(id: string): Promise<ApiResponse<void>> {\n    return this.request<void>(`/tools/${id}`, {\n      method: 'DELETE',\n    });\n  }\n\n  // 管理员API\n  async getAdminTools(params?: {\n    page?: number;\n    limit?: number;\n    status?: string;\n    search?: string;\n    sort?: string;\n    order?: string;\n  }): Promise<ApiResponse<ToolsResponse>> {\n    const searchParams = new URLSearchParams();\n    if (params) {\n      Object.entries(params).forEach(([key, value]) => {\n        if (value !== undefined) {\n          searchParams.append(key, value.toString());\n        }\n      });\n    }\n    const query = searchParams.toString();\n    return this.request<ToolsResponse>(`/admin/tools${query ? `?${query}` : ''}`);\n  }\n\n  async approveTool(id: string, data: {\n    reviewedBy?: string;\n    reviewNotes?: string;\n    publishedAt?: string;\n  }): Promise<ApiResponse<Tool>> {\n    return this.request<Tool>(`/admin/tools/${id}/approve`, {\n      method: 'POST',\n      body: JSON.stringify(data),\n    });\n  }\n\n  async rejectTool(id: string, data: {\n    reviewedBy?: string;\n    rejectReason: string;\n  }): Promise<ApiResponse<Tool>> {\n    return this.request<Tool>(`/admin/tools/${id}/reject`, {\n      method: 'POST',\n      body: JSON.stringify(data),\n    });\n  }\n\n  async getAdminStats(timeRange?: string): Promise<ApiResponse<AdminStats>> {\n    const query = timeRange ? `?timeRange=${timeRange}` : '';\n    return this.request<AdminStats>(`/admin/stats${query}`);\n  }\n\n  // 分类API\n  async getCategories(): Promise<ApiResponse<CategoriesResponse>> {\n    return this.request<CategoriesResponse>('/categories');\n  }\n}\n\n// 创建默认的API客户端实例\nexport const apiClient = new ApiClient();\n\n// 导出API客户端类以便在需要时创建新实例\nexport { ApiClient };\n"], "names": [], "mappings": "AAAA,YAAY;;;;;AACS;AAArB,MAAM,eAAe,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,wBAAwB,IAAI;AAuH7D,MAAM;IACI,QAAgB;IAExB,YAAY,UAAkB,YAAY,CAAE;QAC1C,IAAI,CAAC,OAAO,GAAG;IACjB;IAEA,MAAc,QACZ,QAAgB,EAChB,UAAuB,CAAC,CAAC,EACA;QACzB,IAAI;YACF,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,GAAG,UAAU;YACxC,MAAM,SAAsB;gBAC1B,SAAS;oBACP,gBAAgB;oBAChB,GAAG,QAAQ,OAAO;gBACpB;gBACA,GAAG,OAAO;YACZ;YAEA,MAAM,WAAW,MAAM,MAAM,KAAK;YAClC,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,KAAK,KAAK,IAAI,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YACxE;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uBAAuB;YACrC,OAAO;gBACL,SAAS;gBACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAClD;QACF;IACF;IAEA,UAAU;IACV,MAAM,SAAS,MAQd,EAAuC;QACtC,MAAM,eAAe,IAAI;QACzB,IAAI,QAAQ;YACV,OAAO,OAAO,CAAC,QAAQ,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;gBAC1C,IAAI,UAAU,WAAW;oBACvB,aAAa,MAAM,CAAC,KAAK,MAAM,QAAQ;gBACzC;YACF;QACF;QACA,MAAM,QAAQ,aAAa,QAAQ;QACnC,OAAO,IAAI,CAAC,OAAO,CAAgB,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC,EAAE,OAAO,GAAG,IAAI;IACxE;IAEA,MAAM,QAAQ,EAAU,EAA8B;QACpD,OAAO,IAAI,CAAC,OAAO,CAAO,CAAC,OAAO,EAAE,IAAI;IAC1C;IAEA,MAAM,WAAW,QAAuB,EAA8B;QACpE,OAAO,IAAI,CAAC,OAAO,CAAO,UAAU;YAClC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,WAAW,EAAU,EAAE,QAAuB,EAA8B;QAChF,OAAO,IAAI,CAAC,OAAO,CAAO,CAAC,OAAO,EAAE,IAAI,EAAE;YACxC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,WAAW,EAAU,EAA8B;QACvD,OAAO,IAAI,CAAC,OAAO,CAAO,CAAC,OAAO,EAAE,IAAI,EAAE;YACxC,QAAQ;QACV;IACF;IAEA,SAAS;IACT,MAAM,cAAc,MAOnB,EAAuC;QACtC,MAAM,eAAe,IAAI;QACzB,IAAI,QAAQ;YACV,OAAO,OAAO,CAAC,QAAQ,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;gBAC1C,IAAI,UAAU,WAAW;oBACvB,aAAa,MAAM,CAAC,KAAK,MAAM,QAAQ;gBACzC;YACF;QACF;QACA,MAAM,QAAQ,aAAa,QAAQ;QACnC,OAAO,IAAI,CAAC,OAAO,CAAgB,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC,EAAE,OAAO,GAAG,IAAI;IAC9E;IAEA,MAAM,YAAY,EAAU,EAAE,IAI7B,EAA8B;QAC7B,OAAO,IAAI,CAAC,OAAO,CAAO,CAAC,aAAa,EAAE,GAAG,QAAQ,CAAC,EAAE;YACtD,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,WAAW,EAAU,EAAE,IAG5B,EAA8B;QAC7B,OAAO,IAAI,CAAC,OAAO,CAAO,CAAC,aAAa,EAAE,GAAG,OAAO,CAAC,EAAE;YACrD,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,cAAc,SAAkB,EAAoC;QACxE,MAAM,QAAQ,YAAY,CAAC,WAAW,EAAE,WAAW,GAAG;QACtD,OAAO,IAAI,CAAC,OAAO,CAAa,CAAC,YAAY,EAAE,OAAO;IACxD;IAEA,QAAQ;IACR,MAAM,gBAA0D;QAC9D,OAAO,IAAI,CAAC,OAAO,CAAqB;IAC1C;AACF;AAGO,MAAM,YAAY,IAAI", "debugId": null}}, {"offset": {"line": 895, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/app/dashboard/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport Link from 'next/link';\nimport Layout from '@/components/Layout';\nimport LoadingSpinner from '@/components/LoadingSpinner';\nimport ErrorMessage from '@/components/ErrorMessage';\nimport { apiClient, Tool } from '@/lib/api';\nimport {\n  Plus,\n  Edit,\n  Eye,\n  Clock,\n  CheckCircle,\n  XCircle,\n  Calendar,\n  ExternalLink,\n  BarChart3\n} from 'lucide-react';\n\n// 分类标签映射\nconst categoryLabels: Record<string, string> = {\n  'text-generation': '文本生成',\n  'image-generation': '图像生成',\n  'code-generation': '代码生成',\n  'data-analysis': '数据分析',\n  'audio-processing': '音频处理',\n  'video-editing': '视频编辑',\n  'design-tools': '设计工具',\n  'productivity': '生产力工具',\n  'customer-service': '客户服务'\n};\n\nconst getStatusColor = (status: string) => {\n  switch (status) {\n    case 'approved':\n      return 'bg-green-100 text-green-800';\n    case 'pending':\n      return 'bg-yellow-100 text-yellow-800';\n    case 'rejected':\n      return 'bg-red-100 text-red-800';\n    default:\n      return 'bg-gray-100 text-gray-800';\n  }\n};\n\nconst getStatusText = (status: string) => {\n  switch (status) {\n    case 'approved':\n      return '已通过';\n    case 'pending':\n      return '审核中';\n    case 'rejected':\n      return '已拒绝';\n    default:\n      return status;\n  }\n};\n\nconst getStatusIcon = (status: string) => {\n  switch (status) {\n    case 'approved':\n      return <CheckCircle className=\"h-4 w-4\" />;\n    case 'pending':\n      return <Clock className=\"h-4 w-4\" />;\n    case 'rejected':\n      return <XCircle className=\"h-4 w-4\" />;\n    default:\n      return null;\n  }\n};\n\nexport default function DashboardPage() {\n  const [selectedStatus, setSelectedStatus] = useState('all');\n  const [tools, setTools] = useState<Tool[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n\n  useEffect(() => {\n    fetchTools();\n  }, []);\n\n  const fetchTools = async () => {\n    try {\n      setLoading(true);\n      setError('');\n\n      // 获取所有工具（包括所有状态）- 这里应该根据用户身份获取用户自己的工具\n      // 暂时获取所有工具作为演示\n      const response = await apiClient.getAdminTools();\n\n      if (response.success && response.data) {\n        setTools(response.data.tools);\n      } else {\n        setError(response.error || '获取工具列表失败');\n      }\n    } catch (err) {\n      setError('网络错误，请重试');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const filteredTools = tools.filter(tool =>\n    selectedStatus === 'all' || tool.status === selectedStatus\n  );\n\n  const stats = {\n    total: tools.length,\n    approved: tools.filter(t => t.status === 'approved').length,\n    pending: tools.filter(t => t.status === 'pending').length,\n    rejected: tools.filter(t => t.status === 'rejected').length,\n    totalViews: tools.reduce((sum, t) => sum + t.views, 0),\n    totalLikes: tools.reduce((sum, t) => sum + t.likes, 0)\n  };\n\n  if (loading) {\n    return (\n      <Layout>\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n          <LoadingSpinner size=\"lg\" className=\"py-20\" />\n        </div>\n      </Layout>\n    );\n  }\n\n  return (\n    <Layout>\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {/* Header */}\n        <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between mb-8\">\n          <div>\n            <h1 className=\"text-3xl font-bold text-gray-900 mb-2\">开发者仪表板</h1>\n            <p className=\"text-lg text-gray-600\">管理您提交的 AI 工具</p>\n          </div>\n          <Link\n            href=\"/submit\"\n            className=\"inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 transition-colors\"\n          >\n            <Plus className=\"mr-2 h-5 w-5\" />\n            提交新工具\n          </Link>\n        </div>\n\n        {/* Stats */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\">\n          <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <BarChart3 className=\"h-8 w-8 text-blue-600\" />\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-500\">总提交数</p>\n                <p className=\"text-2xl font-bold text-gray-900\">{stats.total}</p>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <CheckCircle className=\"h-8 w-8 text-green-600\" />\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-500\">已通过</p>\n                <p className=\"text-2xl font-bold text-gray-900\">{stats.approved}</p>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <Eye className=\"h-8 w-8 text-purple-600\" />\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-500\">总浏览量</p>\n                <p className=\"text-2xl font-bold text-gray-900\">{stats.totalViews}</p>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <div className=\"h-8 w-8 text-red-600\">❤️</div>\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-500\">总点赞数</p>\n                <p className=\"text-2xl font-bold text-gray-900\">{stats.totalLikes}</p>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Filters */}\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6\">\n          <div className=\"flex flex-wrap gap-2\">\n            <button\n              onClick={() => setSelectedStatus('all')}\n              className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${\n                selectedStatus === 'all'\n                  ? 'bg-blue-600 text-white'\n                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'\n              }`}\n            >\n              全部 ({stats.total})\n            </button>\n            <button\n              onClick={() => setSelectedStatus('approved')}\n              className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${\n                selectedStatus === 'approved'\n                  ? 'bg-green-600 text-white'\n                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'\n              }`}\n            >\n              已通过 ({stats.approved})\n            </button>\n            <button\n              onClick={() => setSelectedStatus('pending')}\n              className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${\n                selectedStatus === 'pending'\n                  ? 'bg-yellow-600 text-white'\n                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'\n              }`}\n            >\n              审核中 ({stats.pending})\n            </button>\n            <button\n              onClick={() => setSelectedStatus('rejected')}\n              className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${\n                selectedStatus === 'rejected'\n                  ? 'bg-red-600 text-white'\n                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'\n              }`}\n            >\n              已拒绝 ({stats.rejected})\n            </button>\n          </div>\n        </div>\n\n        {/* Error Message */}\n        {error && (\n          <ErrorMessage\n            message={error}\n            onClose={() => setError('')}\n            className=\"mb-6\"\n          />\n        )}\n\n        {/* Tools List */}}\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200\">\n          {filteredTools.length > 0 ? (\n            <div className=\"divide-y divide-gray-200\">\n              {filteredTools.map((tool) => (\n                <div key={tool._id} className=\"p-6\">\n                  <div className=\"flex items-start justify-between\">\n                    <div className=\"flex-1\">\n                      <div className=\"flex items-center space-x-3 mb-2\">\n                        <h3 className=\"text-lg font-semibold text-gray-900\">\n                          {tool.name}\n                        </h3>\n                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(tool.status)}`}>\n                          {getStatusIcon(tool.status)}\n                          <span className=\"ml-1\">{getStatusText(tool.status)}</span>\n                        </span>\n                      </div>\n                      \n                      <p className=\"text-gray-600 mb-3 line-clamp-2\">\n                        {tool.description}\n                      </p>\n                      \n                      <div className=\"flex items-center space-x-6 text-sm text-gray-500\">\n                        <div className=\"flex items-center space-x-1\">\n                          <Calendar className=\"h-4 w-4\" />\n                          <span>提交于 {new Date(tool.submittedAt).toLocaleDateString('zh-CN')}</span>\n                        </div>\n                        {tool.publishedAt && (\n                          <div className=\"flex items-center space-x-1\">\n                            <CheckCircle className=\"h-4 w-4\" />\n                            <span>发布于 {new Date(tool.publishedAt).toLocaleDateString('zh-CN')}</span>\n                          </div>\n                        )}\n                        {tool.status === 'approved' && (\n                          <>\n                            <div className=\"flex items-center space-x-1\">\n                              <Eye className=\"h-4 w-4\" />\n                              <span>{tool.views} 浏览</span>\n                            </div>\n                            <div className=\"flex items-center space-x-1\">\n                              <span>❤️</span>\n                              <span>{tool.likes} 点赞</span>\n                            </div>\n                          </>\n                        )}\n                      </div>\n\n                      {tool.status === 'rejected' && tool.reviewNotes && (\n                        <div className=\"mt-3 p-3 bg-red-50 border border-red-200 rounded-lg\">\n                          <p className=\"text-sm text-red-800\">\n                            <strong>拒绝原因：</strong> {tool.reviewNotes}\n                          </p>\n                        </div>\n                      )}\n                    </div>\n                    \n                    <div className=\"flex items-center space-x-2 ml-4\">\n                      {tool.status === 'approved' && (\n                        <Link\n                          href={`/tools/${tool._id}`}\n                          className=\"p-2 text-gray-400 hover:text-blue-600 transition-colors\"\n                          title=\"查看详情\"\n                        >\n                          <Eye className=\"h-5 w-5\" />\n                        </Link>\n                      )}\n                      <a\n                        href={tool.website}\n                        target=\"_blank\"\n                        rel=\"noopener noreferrer\"\n                        className=\"p-2 text-gray-400 hover:text-green-600 transition-colors\"\n                        title=\"访问网站\"\n                      >\n                        <ExternalLink className=\"h-5 w-5\" />\n                      </a>\n                      {(tool.status === 'rejected' || tool.status === 'pending') && (\n                        <button\n                          className=\"p-2 text-gray-400 hover:text-blue-600 transition-colors\"\n                          title=\"编辑\"\n                        >\n                          <Edit className=\"h-5 w-5\" />\n                        </button>\n                      )}\n                    </div>\n                  </div>\n                </div>\n              ))}\n            </div>\n          ) : (\n            <div className=\"text-center py-12\">\n              <div className=\"text-gray-400 mb-4\">\n                <BarChart3 className=\"h-12 w-12 mx-auto\" />\n              </div>\n              <h3 className=\"text-lg font-medium text-gray-900 mb-2\">\n                {selectedStatus === 'all' ? '还没有提交任何工具' : `没有${getStatusText(selectedStatus)}的工具`}\n              </h3>\n              <p className=\"text-gray-600 mb-4\">\n                {selectedStatus === 'all' \n                  ? '开始提交您的第一个 AI 工具吧！'\n                  : '尝试选择其他状态查看工具'\n                }\n              </p>\n              {selectedStatus === 'all' && (\n                <Link\n                  href=\"/submit\"\n                  className=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 transition-colors\"\n                >\n                  <Plus className=\"mr-2 h-4 w-4\" />\n                  提交工具\n                </Link>\n              )}\n            </div>\n          )}\n        </div>\n      </div>\n    </Layout>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AARA;;;;;;;;AAoBA,SAAS;AACT,MAAM,iBAAyC;IAC7C,mBAAmB;IACnB,oBAAoB;IACpB,mBAAmB;IACnB,iBAAiB;IACjB,oBAAoB;IACpB,iBAAiB;IACjB,gBAAgB;IAChB,gBAAgB;IAChB,oBAAoB;AACtB;AAEA,MAAM,iBAAiB,CAAC;IACtB,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAEA,MAAM,gBAAgB,CAAC;IACrB,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAEA,MAAM,gBAAgB,CAAC;IACrB,OAAQ;QACN,KAAK;YACH,qBAAO,6LAAC,8NAAA,CAAA,cAAW;gBAAC,WAAU;;;;;;QAChC,KAAK;YACH,qBAAO,6LAAC,uMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;QAC1B,KAAK;YACH,qBAAO,6LAAC,+MAAA,CAAA,UAAO;gBAAC,WAAU;;;;;;QAC5B;YACE,OAAO;IACX;AACF;AAEe,SAAS;;IACtB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR;QACF;kCAAG,EAAE;IAEL,MAAM,aAAa;QACjB,IAAI;YACF,WAAW;YACX,SAAS;YAET,sCAAsC;YACtC,eAAe;YACf,MAAM,WAAW,MAAM,oHAAA,CAAA,YAAS,CAAC,aAAa;YAE9C,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;gBACrC,SAAS,SAAS,IAAI,CAAC,KAAK;YAC9B,OAAO;gBACL,SAAS,SAAS,KAAK,IAAI;YAC7B;QACF,EAAE,OAAO,KAAK;YACZ,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,gBAAgB,MAAM,MAAM,CAAC,CAAA,OACjC,mBAAmB,SAAS,KAAK,MAAM,KAAK;IAG9C,MAAM,QAAQ;QACZ,OAAO,MAAM,MAAM;QACnB,UAAU,MAAM,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,YAAY,MAAM;QAC3D,SAAS,MAAM,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,WAAW,MAAM;QACzD,UAAU,MAAM,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,YAAY,MAAM;QAC3D,YAAY,MAAM,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,KAAK,EAAE;QACpD,YAAY,MAAM,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,KAAK,EAAE;IACtD;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC,+HAAA,CAAA,UAAM;sBACL,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,uIAAA,CAAA,UAAc;oBAAC,MAAK;oBAAK,WAAU;;;;;;;;;;;;;;;;IAI5C;IAEA,qBACE,6LAAC,+HAAA,CAAA,UAAM;kBACL,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAAwC;;;;;;8CACtD,6LAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;sCAEvC,6LAAC,+JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;;8CAEV,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;8BAMrC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,qNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;;;;;;kDAEvB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,6LAAC;gDAAE,WAAU;0DAAoC,MAAM,KAAK;;;;;;;;;;;;;;;;;;;;;;;sCAKlE,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,8NAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;;;;;;kDAEzB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,6LAAC;gDAAE,WAAU;0DAAoC,MAAM,QAAQ;;;;;;;;;;;;;;;;;;;;;;;sCAKrE,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,mMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;;;;;;kDAEjB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,6LAAC;gDAAE,WAAU;0DAAoC,MAAM,UAAU;;;;;;;;;;;;;;;;;;;;;;;sCAKvE,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;sDAAuB;;;;;;;;;;;kDAExC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,6LAAC;gDAAE,WAAU;0DAAoC,MAAM,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAOzE,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,SAAS,IAAM,kBAAkB;gCACjC,WAAW,CAAC,2DAA2D,EACrE,mBAAmB,QACf,2BACA,+CACJ;;oCACH;oCACM,MAAM,KAAK;oCAAC;;;;;;;0CAEnB,6LAAC;gCACC,SAAS,IAAM,kBAAkB;gCACjC,WAAW,CAAC,2DAA2D,EACrE,mBAAmB,aACf,4BACA,+CACJ;;oCACH;oCACO,MAAM,QAAQ;oCAAC;;;;;;;0CAEvB,6LAAC;gCACC,SAAS,IAAM,kBAAkB;gCACjC,WAAW,CAAC,2DAA2D,EACrE,mBAAmB,YACf,6BACA,+CACJ;;oCACH;oCACO,MAAM,OAAO;oCAAC;;;;;;;0CAEtB,6LAAC;gCACC,SAAS,IAAM,kBAAkB;gCACjC,WAAW,CAAC,2DAA2D,EACrE,mBAAmB,aACf,0BACA,+CACJ;;oCACH;oCACO,MAAM,QAAQ;oCAAC;;;;;;;;;;;;;;;;;;gBAM1B,uBACC,6LAAC,qIAAA,CAAA,UAAY;oBACX,SAAS;oBACT,SAAS,IAAM,SAAS;oBACxB,WAAU;;;;;;gBAII;8BAClB,6LAAC;oBAAI,WAAU;8BACZ,cAAc,MAAM,GAAG,kBACtB,6LAAC;wBAAI,WAAU;kCACZ,cAAc,GAAG,CAAC,CAAC,qBAClB,6LAAC;gCAAmB,WAAU;0CAC5B,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEACX,KAAK,IAAI;;;;;;sEAEZ,6LAAC;4DAAK,WAAW,CAAC,wEAAwE,EAAE,eAAe,KAAK,MAAM,GAAG;;gEACtH,cAAc,KAAK,MAAM;8EAC1B,6LAAC;oEAAK,WAAU;8EAAQ,cAAc,KAAK,MAAM;;;;;;;;;;;;;;;;;;8DAIrD,6LAAC;oDAAE,WAAU;8DACV,KAAK,WAAW;;;;;;8DAGnB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,6MAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;8EACpB,6LAAC;;wEAAK;wEAAK,IAAI,KAAK,KAAK,WAAW,EAAE,kBAAkB,CAAC;;;;;;;;;;;;;wDAE1D,KAAK,WAAW,kBACf,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,8NAAA,CAAA,cAAW;oEAAC,WAAU;;;;;;8EACvB,6LAAC;;wEAAK;wEAAK,IAAI,KAAK,KAAK,WAAW,EAAE,kBAAkB,CAAC;;;;;;;;;;;;;wDAG5D,KAAK,MAAM,KAAK,4BACf;;8EACE,6LAAC;oEAAI,WAAU;;sFACb,6LAAC,mMAAA,CAAA,MAAG;4EAAC,WAAU;;;;;;sFACf,6LAAC;;gFAAM,KAAK,KAAK;gFAAC;;;;;;;;;;;;;8EAEpB,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;sFAAK;;;;;;sFACN,6LAAC;;gFAAM,KAAK,KAAK;gFAAC;;;;;;;;;;;;;;;;;;;;;gDAMzB,KAAK,MAAM,KAAK,cAAc,KAAK,WAAW,kBAC7C,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAE,WAAU;;0EACX,6LAAC;0EAAO;;;;;;4DAAc;4DAAE,KAAK,WAAW;;;;;;;;;;;;;;;;;;sDAMhD,6LAAC;4CAAI,WAAU;;gDACZ,KAAK,MAAM,KAAK,4BACf,6LAAC,+JAAA,CAAA,UAAI;oDACH,MAAM,CAAC,OAAO,EAAE,KAAK,GAAG,EAAE;oDAC1B,WAAU;oDACV,OAAM;8DAEN,cAAA,6LAAC,mMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;;;;;;8DAGnB,6LAAC;oDACC,MAAM,KAAK,OAAO;oDAClB,QAAO;oDACP,KAAI;oDACJ,WAAU;oDACV,OAAM;8DAEN,cAAA,6LAAC,yNAAA,CAAA,eAAY;wDAAC,WAAU;;;;;;;;;;;gDAEzB,CAAC,KAAK,MAAM,KAAK,cAAc,KAAK,MAAM,KAAK,SAAS,mBACvD,6LAAC;oDACC,WAAU;oDACV,OAAM;8DAEN,cAAA,6LAAC,8MAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;+BA3EhB,KAAK,GAAG;;;;;;;;;6CAoFtB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,qNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;;;;;;0CAEvB,6LAAC;gCAAG,WAAU;0CACX,mBAAmB,QAAQ,cAAc,CAAC,EAAE,EAAE,cAAc,gBAAgB,GAAG,CAAC;;;;;;0CAEnF,6LAAC;gCAAE,WAAU;0CACV,mBAAmB,QAChB,sBACA;;;;;;4BAGL,mBAAmB,uBAClB,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;;kDAEV,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUnD;GAvSwB;KAAA", "debugId": null}}]}
{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/Layout.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Link from 'next/link';\nimport { Search, Menu, X } from 'lucide-react';\n\ninterface LayoutProps {\n  children: React.ReactNode;\n}\n\nconst Layout: React.FC<LayoutProps> = ({ children }) => {\n  const [isMenuOpen, setIsMenuOpen] = React.useState(false);\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header */}\n      <header className=\"bg-white shadow-sm border-b border-gray-200\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center h-16\">\n            {/* Logo */}\n            <div className=\"flex items-center\">\n              <Link href=\"/\" className=\"flex items-center space-x-2\">\n                <div className=\"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center\">\n                  <span className=\"text-white font-bold text-sm\">AI</span>\n                </div>\n                <span className=\"text-xl font-bold text-gray-900\">AI Tools</span>\n              </Link>\n            </div>\n\n            {/* Desktop Navigation */}\n            <nav className=\"hidden md:flex items-center space-x-8\">\n              <Link href=\"/\" className=\"text-gray-700 hover:text-blue-600 transition-colors\">\n                首页\n              </Link>\n              <Link href=\"/tools\" className=\"text-gray-700 hover:text-blue-600 transition-colors\">\n                工具目录\n              </Link>\n              <Link href=\"/categories\" className=\"text-gray-700 hover:text-blue-600 transition-colors\">\n                分类\n              </Link>\n              <Link href=\"/submit\" className=\"text-gray-700 hover:text-blue-600 transition-colors\">\n                提交工具\n              </Link>\n              <Link href=\"/dashboard\" className=\"text-gray-700 hover:text-blue-600 transition-colors\">\n                仪表板\n              </Link>\n              {/* Admin Navigation */}\n              <div className=\"relative group\">\n                <button className=\"text-gray-700 hover:text-blue-600 transition-colors flex items-center\">\n                  管理员\n                  <svg className=\"w-4 h-4 ml-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 9l-7 7-7-7\" />\n                  </svg>\n                </button>\n                <div className=\"absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg border border-gray-200 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50\">\n                  <div className=\"py-1\">\n                    <Link href=\"/admin\" className=\"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors\">\n                      审核中心\n                    </Link>\n                    <Link href=\"/admin/dashboard\" className=\"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors\">\n                      统计面板\n                    </Link>\n                  </div>\n                </div>\n              </div>\n            </nav>\n\n            {/* Search Bar */}\n            <div className=\"hidden md:flex items-center flex-1 max-w-md mx-8\">\n              <div className=\"relative w-full\">\n                <input\n                  type=\"text\"\n                  placeholder=\"搜索 AI 工具...\"\n                  className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                />\n                <Search className=\"absolute left-3 top-2.5 h-5 w-5 text-gray-400\" />\n              </div>\n            </div>\n\n            {/* Mobile menu button */}\n            <div className=\"md:hidden\">\n              <button\n                onClick={() => setIsMenuOpen(!isMenuOpen)}\n                className=\"text-gray-700 hover:text-blue-600\"\n              >\n                {isMenuOpen ? <X className=\"h-6 w-6\" /> : <Menu className=\"h-6 w-6\" />}\n              </button>\n            </div>\n          </div>\n        </div>\n\n        {/* Mobile Navigation */}\n        {isMenuOpen && (\n          <div className=\"md:hidden bg-white border-t border-gray-200\">\n            <div className=\"px-4 py-2 space-y-1\">\n              <Link\n                href=\"/\"\n                className=\"block px-3 py-2 text-gray-700 hover:text-blue-600 hover:bg-gray-50 rounded-md\"\n              >\n                首页\n              </Link>\n              <Link\n                href=\"/tools\"\n                className=\"block px-3 py-2 text-gray-700 hover:text-blue-600 hover:bg-gray-50 rounded-md\"\n              >\n                工具目录\n              </Link>\n              <Link\n                href=\"/categories\"\n                className=\"block px-3 py-2 text-gray-700 hover:text-blue-600 hover:bg-gray-50 rounded-md\"\n              >\n                分类\n              </Link>\n              <Link\n                href=\"/submit\"\n                className=\"block px-3 py-2 text-gray-700 hover:text-blue-600 hover:bg-gray-50 rounded-md\"\n              >\n                提交工具\n              </Link>\n              <Link\n                href=\"/dashboard\"\n                className=\"block px-3 py-2 text-gray-700 hover:text-blue-600 hover:bg-gray-50 rounded-md\"\n              >\n                仪表板\n              </Link>\n              {/* Admin Links */}\n              <div className=\"border-t border-gray-200 pt-4 mt-4\">\n                <div className=\"px-3 py-2 text-sm font-medium text-gray-500\">管理员</div>\n                <Link\n                  href=\"/admin\"\n                  className=\"block px-3 py-2 text-gray-700 hover:text-blue-600 hover:bg-gray-50 rounded-md\"\n                >\n                  审核中心\n                </Link>\n                <Link\n                  href=\"/admin/dashboard\"\n                  className=\"block px-3 py-2 text-gray-700 hover:text-blue-600 hover:bg-gray-50 rounded-md\"\n                >\n                  统计面板\n                </Link>\n              </div>\n              {/* Mobile Search */}\n              <div className=\"px-3 py-2\">\n                <div className=\"relative\">\n                  <input\n                    type=\"text\"\n                    placeholder=\"搜索 AI 工具...\"\n                    className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                  />\n                  <Search className=\"absolute left-3 top-2.5 h-5 w-5 text-gray-400\" />\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n      </header>\n\n      {/* Main Content */}\n      <main className=\"flex-1\">\n        {children}\n      </main>\n\n      {/* Footer */}\n      <footer className=\"bg-white border-t border-gray-200 mt-16\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n          <div className=\"grid grid-cols-1 md:grid-cols-4 gap-8\">\n            <div className=\"col-span-1 md:col-span-2\">\n              <div className=\"flex items-center space-x-2 mb-4\">\n                <div className=\"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center\">\n                  <span className=\"text-white font-bold text-sm\">AI</span>\n                </div>\n                <span className=\"text-xl font-bold text-gray-900\">AI Tools</span>\n              </div>\n              <p className=\"text-gray-600 mb-4\">\n                发现最新最好的 AI 工具，提升您的工作效率和创造力。\n              </p>\n            </div>\n            \n            <div>\n              <h3 className=\"text-sm font-semibold text-gray-900 tracking-wider uppercase mb-4\">\n                快速链接\n              </h3>\n              <ul className=\"space-y-2\">\n                <li>\n                  <Link href=\"/tools\" className=\"text-gray-600 hover:text-blue-600\">\n                    工具目录\n                  </Link>\n                </li>\n                <li>\n                  <Link href=\"/categories\" className=\"text-gray-600 hover:text-blue-600\">\n                    分类浏览\n                  </Link>\n                </li>\n                <li>\n                  <Link href=\"/submit\" className=\"text-gray-600 hover:text-blue-600\">\n                    提交工具\n                  </Link>\n                </li>\n              </ul>\n            </div>\n            \n            <div>\n              <h3 className=\"text-sm font-semibold text-gray-900 tracking-wider uppercase mb-4\">\n                支持\n              </h3>\n              <ul className=\"space-y-2\">\n                <li>\n                  <a href=\"#\" className=\"text-gray-600 hover:text-blue-600\">\n                    帮助中心\n                  </a>\n                </li>\n                <li>\n                  <a href=\"#\" className=\"text-gray-600 hover:text-blue-600\">\n                    联系我们\n                  </a>\n                </li>\n                <li>\n                  <a href=\"#\" className=\"text-gray-600 hover:text-blue-600\">\n                    隐私政策\n                  </a>\n                </li>\n              </ul>\n            </div>\n          </div>\n          \n          <div className=\"border-t border-gray-200 mt-8 pt-8\">\n            <p className=\"text-center text-gray-600\">\n              © 2024 AI Tools Directory. All rights reserved.\n            </p>\n          </div>\n        </div>\n      </footer>\n    </div>\n  );\n};\n\nexport default Layout;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;;;AAJA;;;;AAUA,MAAM,SAAgC,CAAC,EAAE,QAAQ,EAAE;;IACjD,MAAM,CAAC,YAAY,cAAc,GAAG,6JAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IAEnD,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAO,WAAU;;kCAChB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAI,WAAU;;0DACvB,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAK,WAAU;8DAA+B;;;;;;;;;;;0DAEjD,6LAAC;gDAAK,WAAU;0DAAkC;;;;;;;;;;;;;;;;;8CAKtD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAI,WAAU;sDAAsD;;;;;;sDAG/E,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAS,WAAU;sDAAsD;;;;;;sDAGpF,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAc,WAAU;sDAAsD;;;;;;sDAGzF,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAU,WAAU;sDAAsD;;;;;;sDAGrF,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAa,WAAU;sDAAsD;;;;;;sDAIxF,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAO,WAAU;;wDAAwE;sEAExF,6LAAC;4DAAI,WAAU;4DAAe,MAAK;4DAAO,QAAO;4DAAe,SAAQ;sEACtE,cAAA,6LAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAG,GAAE;;;;;;;;;;;;;;;;;8DAGzE,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,+JAAA,CAAA,UAAI;gEAAC,MAAK;gEAAS,WAAU;0EAA4E;;;;;;0EAG1G,6LAAC,+JAAA,CAAA,UAAI;gEAAC,MAAK;gEAAmB,WAAU;0EAA4E;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAS5H,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,MAAK;gDACL,aAAY;gDACZ,WAAU;;;;;;0DAEZ,6LAAC,yMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;;;;;;;;;;;;8CAKtB,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCACC,SAAS,IAAM,cAAc,CAAC;wCAC9B,WAAU;kDAET,2BAAa,6LAAC,+LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;iEAAe,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAOjE,4BACC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAID,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAA8C;;;;;;sDAC7D,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;sDAGD,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;;;;;;;8CAKH,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,MAAK;gDACL,aAAY;gDACZ,WAAU;;;;;;0DAEZ,6LAAC,yMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS9B,6LAAC;gBAAK,WAAU;0BACb;;;;;;0BAIH,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAK,WAAU;kEAA+B;;;;;;;;;;;8DAEjD,6LAAC;oDAAK,WAAU;8DAAkC;;;;;;;;;;;;sDAEpD,6LAAC;4CAAE,WAAU;sDAAqB;;;;;;;;;;;;8CAKpC,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAoE;;;;;;sDAGlF,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;8DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAS,WAAU;kEAAoC;;;;;;;;;;;8DAIpE,6LAAC;8DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAc,WAAU;kEAAoC;;;;;;;;;;;8DAIzE,6LAAC;8DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAU,WAAU;kEAAoC;;;;;;;;;;;;;;;;;;;;;;;8CAOzE,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAoE;;;;;;sDAGlF,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;8DACC,cAAA,6LAAC;wDAAE,MAAK;wDAAI,WAAU;kEAAoC;;;;;;;;;;;8DAI5D,6LAAC;8DACC,cAAA,6LAAC;wDAAE,MAAK;wDAAI,WAAU;kEAAoC;;;;;;;;;;;8DAI5D,6LAAC;8DACC,cAAA,6LAAC;wDAAE,MAAK;wDAAI,WAAU;kEAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQlE,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAE,WAAU;0CAA4B;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQrD;GAhOM;KAAA;uCAkOS", "debugId": null}}, {"offset": {"line": 666, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/tools/LikeButton.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { useSession } from 'next-auth/react';\nimport { FaHeart, FaRegHeart } from 'react-icons/fa';\n\ninterface LikeButtonProps {\n  toolId: string;\n  initialLikes?: number;\n  initialLiked?: boolean;\n  onLoginRequired?: () => void;\n}\n\nexport default function LikeButton({ \n  toolId, \n  initialLikes = 0, \n  initialLiked = false,\n  onLoginRequired \n}: LikeButtonProps) {\n  const { data: session } = useSession();\n  const [liked, setLiked] = useState(initialLiked);\n  const [likes, setLikes] = useState(initialLikes);\n  const [isLoading, setIsLoading] = useState(false);\n\n  // 获取点赞状态\n  useEffect(() => {\n    const fetchLikeStatus = async () => {\n      try {\n        const response = await fetch(`/api/tools/${toolId}/like`);\n        if (response.ok) {\n          const data = await response.json();\n          if (data.success) {\n            setLiked(data.data.liked);\n            setLikes(data.data.likes);\n          }\n        }\n      } catch (error) {\n        console.error('Failed to fetch like status:', error);\n      }\n    };\n\n    if (session) {\n      fetchLikeStatus();\n    }\n  }, [toolId, session]);\n\n  const handleLike = async () => {\n    if (!session) {\n      onLoginRequired?.();\n      return;\n    }\n\n    if (isLoading) return;\n\n    setIsLoading(true);\n    \n    try {\n      const response = await fetch(`/api/tools/${toolId}/like`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n        if (data.success) {\n          setLiked(data.data.liked);\n          setLikes(data.data.likes);\n        }\n      } else {\n        const errorData = await response.json();\n        console.error('Like failed:', errorData.message);\n      }\n    } catch (error) {\n      console.error('Like request failed:', error);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  return (\n    <button\n      onClick={handleLike}\n      disabled={isLoading}\n      className={`\n        flex items-center gap-2 px-3 py-2 rounded-lg transition-all duration-200\n        ${liked \n          ? 'bg-red-50 text-red-600 hover:bg-red-100' \n          : 'bg-gray-50 text-gray-600 hover:bg-gray-100'\n        }\n        ${isLoading ? 'opacity-50 cursor-not-allowed' : 'hover:scale-105'}\n        border border-gray-200 hover:border-gray-300\n      `}\n    >\n      {liked ? (\n        <FaHeart className=\"w-4 h-4 text-red-500\" />\n      ) : (\n        <FaRegHeart className=\"w-4 h-4\" />\n      )}\n      <span className=\"text-sm font-medium\">\n        {likes > 0 ? likes : ''}\n      </span>\n    </button>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAae,SAAS,WAAW,EACjC,MAAM,EACN,eAAe,CAAC,EAChB,eAAe,KAAK,EACpB,eAAe,EACC;;IAChB,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD;IACnC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,SAAS;IACT,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,MAAM;wDAAkB;oBACtB,IAAI;wBACF,MAAM,WAAW,MAAM,MAAM,CAAC,WAAW,EAAE,OAAO,KAAK,CAAC;wBACxD,IAAI,SAAS,EAAE,EAAE;4BACf,MAAM,OAAO,MAAM,SAAS,IAAI;4BAChC,IAAI,KAAK,OAAO,EAAE;gCAChB,SAAS,KAAK,IAAI,CAAC,KAAK;gCACxB,SAAS,KAAK,IAAI,CAAC,KAAK;4BAC1B;wBACF;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,gCAAgC;oBAChD;gBACF;;YAEA,IAAI,SAAS;gBACX;YACF;QACF;+BAAG;QAAC;QAAQ;KAAQ;IAEpB,MAAM,aAAa;QACjB,IAAI,CAAC,SAAS;YACZ;YACA;QACF;QAEA,IAAI,WAAW;QAEf,aAAa;QAEb,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,WAAW,EAAE,OAAO,KAAK,CAAC,EAAE;gBACxD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,IAAI,KAAK,OAAO,EAAE;oBAChB,SAAS,KAAK,IAAI,CAAC,KAAK;oBACxB,SAAS,KAAK,IAAI,CAAC,KAAK;gBAC1B;YACF,OAAO;gBACL,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,QAAQ,KAAK,CAAC,gBAAgB,UAAU,OAAO;YACjD;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;QACxC,SAAU;YACR,aAAa;QACf;IACF;IAEA,qBACE,6LAAC;QACC,SAAS;QACT,UAAU;QACV,WAAW,CAAC;;QAEV,EAAE,QACE,4CACA,6CACH;QACD,EAAE,YAAY,kCAAkC,kBAAkB;;MAEpE,CAAC;;YAEA,sBACC,6LAAC,iJAAA,CAAA,UAAO;gBAAC,WAAU;;;;;qCAEnB,6LAAC,iJAAA,CAAA,aAAU;gBAAC,WAAU;;;;;;0BAExB,6LAAC;gBAAK,WAAU;0BACb,QAAQ,IAAI,QAAQ;;;;;;;;;;;;AAI7B;GA5FwB;;QAMI,iJAAA,CAAA,aAAU;;;KANd", "debugId": null}}, {"offset": {"line": 797, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/ToolCard.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Link from 'next/link';\nimport { Eye, Heart, ExternalLink } from 'lucide-react';\nimport LikeButton from './tools/LikeButton';\n\ninterface ToolCardProps {\n  tool: {\n    _id: string;\n    name: string;\n    description: string;\n    website: string;\n    logo?: string;\n    category: string;\n    tags: string[];\n    pricing: 'free' | 'freemium' | 'paid';\n    views: number;\n    likes: number;\n  };\n  onLoginRequired?: () => void;\n}\n\nconst ToolCard: React.FC<ToolCardProps> = ({ tool, onLoginRequired }) => {\n  const getPricingColor = (pricing: string) => {\n    switch (pricing) {\n      case 'free':\n        return 'bg-green-100 text-green-800';\n      case 'freemium':\n        return 'bg-blue-100 text-blue-800';\n      case 'paid':\n        return 'bg-orange-100 text-orange-800';\n      default:\n        return 'bg-gray-100 text-gray-800';\n    }\n  };\n\n  const getPricingText = (pricing: string) => {\n    switch (pricing) {\n      case 'free':\n        return '免费';\n      case 'freemium':\n        return '免费增值';\n      case 'paid':\n        return '付费';\n      default:\n        return pricing;\n    }\n  };\n\n  return (\n    <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow duration-200\">\n      <div className=\"p-6\">\n        {/* Header */}\n        <div className=\"flex items-start justify-between mb-4\">\n          <div className=\"flex items-center space-x-3\">\n            {tool.logo ? (\n              <img\n                src={tool.logo}\n                alt={tool.name}\n                className=\"w-12 h-12 rounded-lg object-cover\"\n              />\n            ) : (\n              <div className=\"w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center\">\n                <span className=\"text-white font-bold text-lg\">\n                  {tool.name.charAt(0).toUpperCase()}\n                </span>\n              </div>\n            )}\n            <div>\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-1\">\n                {tool.name}\n              </h3>\n              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getPricingColor(tool.pricing)}`}>\n                {getPricingText(tool.pricing)}\n              </span>\n            </div>\n          </div>\n          \n          <a\n            href={tool.website}\n            target=\"_blank\"\n            rel=\"noopener noreferrer\"\n            className=\"text-gray-400 hover:text-blue-600 transition-colors\"\n          >\n            <ExternalLink className=\"h-5 w-5\" />\n          </a>\n        </div>\n\n        {/* Description */}\n        <p className=\"text-gray-600 text-sm mb-4 line-clamp-2\">\n          {tool.description}\n        </p>\n\n        {/* Tags */}\n        <div className=\"flex flex-wrap gap-2 mb-4\">\n          {tool.tags.slice(0, 3).map((tag, index) => (\n            <span\n              key={index}\n              className=\"inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-700\"\n            >\n              {tag}\n            </span>\n          ))}\n          {tool.tags.length > 3 && (\n            <span className=\"inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-700\">\n              +{tool.tags.length - 3}\n            </span>\n          )}\n        </div>\n\n        {/* Stats and Actions */}\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center space-x-4 text-sm text-gray-500\">\n            <div className=\"flex items-center space-x-1\">\n              <Eye className=\"h-4 w-4\" />\n              <span>{tool.views}</span>\n            </div>\n            <div className=\"flex items-center space-x-1\">\n              <Heart className=\"h-4 w-4\" />\n              <span>{tool.likes}</span>\n            </div>\n          </div>\n          \n          <div className=\"flex items-center space-x-2\">\n            <LikeButton\n              toolId={tool._id}\n              initialLikes={tool.likes}\n              onLoginRequired={onLoginRequired}\n            />\n            <Link\n              href={`/tools/${tool._id}`}\n              className=\"inline-flex items-center px-3 py-1.5 border border-transparent text-sm font-medium rounded-md text-blue-600 bg-blue-50 hover:bg-blue-100 transition-colors\"\n            >\n              查看详情\n            </Link>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ToolCard;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAAA;AAAA;AACA;AALA;;;;;AAuBA,MAAM,WAAoC,CAAC,EAAE,IAAI,EAAE,eAAe,EAAE;IAClE,MAAM,kBAAkB,CAAC;QACvB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;gCACZ,KAAK,IAAI,iBACR,6LAAC;oCACC,KAAK,KAAK,IAAI;oCACd,KAAK,KAAK,IAAI;oCACd,WAAU;;;;;yDAGZ,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAK,WAAU;kDACb,KAAK,IAAI,CAAC,MAAM,CAAC,GAAG,WAAW;;;;;;;;;;;8CAItC,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDACX,KAAK,IAAI;;;;;;sDAEZ,6LAAC;4CAAK,WAAW,CAAC,wEAAwE,EAAE,gBAAgB,KAAK,OAAO,GAAG;sDACxH,eAAe,KAAK,OAAO;;;;;;;;;;;;;;;;;;sCAKlC,6LAAC;4BACC,MAAM,KAAK,OAAO;4BAClB,QAAO;4BACP,KAAI;4BACJ,WAAU;sCAEV,cAAA,6LAAC,yNAAA,CAAA,eAAY;gCAAC,WAAU;;;;;;;;;;;;;;;;;8BAK5B,6LAAC;oBAAE,WAAU;8BACV,KAAK,WAAW;;;;;;8BAInB,6LAAC;oBAAI,WAAU;;wBACZ,KAAK,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,KAAK,sBAC/B,6LAAC;gCAEC,WAAU;0CAET;+BAHI;;;;;wBAMR,KAAK,IAAI,CAAC,MAAM,GAAG,mBAClB,6LAAC;4BAAK,WAAU;;gCAA8F;gCAC1G,KAAK,IAAI,CAAC,MAAM,GAAG;;;;;;;;;;;;;8BAM3B,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,mMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;sDACf,6LAAC;sDAAM,KAAK,KAAK;;;;;;;;;;;;8CAEnB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;sDACjB,6LAAC;sDAAM,KAAK,KAAK;;;;;;;;;;;;;;;;;;sCAIrB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,4IAAA,CAAA,UAAU;oCACT,QAAQ,KAAK,GAAG;oCAChB,cAAc,KAAK,KAAK;oCACxB,iBAAiB;;;;;;8CAEnB,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAM,CAAC,OAAO,EAAE,KAAK,GAAG,EAAE;oCAC1B,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb;KAtHM;uCAwHS", "debugId": null}}, {"offset": {"line": 1075, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/CategoryCard.tsx"], "sourcesContent": ["import React from 'react';\nimport Link from 'next/link';\n\ninterface CategoryCardProps {\n  category: {\n    _id: string;\n    name: string;\n    slug: string;\n    description: string;\n    icon?: string;\n    color?: string;\n    toolCount: number;\n  };\n}\n\nconst CategoryCard: React.FC<CategoryCardProps> = ({ category }) => {\n  return (\n    <Link href={`/categories/${category.slug}`}>\n      <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-all duration-200 hover:scale-105 cursor-pointer\">\n        <div className=\"p-6\">\n          <div className=\"flex items-center space-x-4 mb-4\">\n            <div \n              className=\"w-12 h-12 rounded-lg flex items-center justify-center text-2xl\"\n              style={{ backgroundColor: category.color || '#3B82F6' }}\n            >\n              <span className=\"text-white\">\n                {category.icon || '🔧'}\n              </span>\n            </div>\n            <div>\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-1\">\n                {category.name}\n              </h3>\n              <p className=\"text-sm text-gray-500\">\n                {category.toolCount} 个工具\n              </p>\n            </div>\n          </div>\n          \n          <p className=\"text-gray-600 text-sm\">\n            {category.description}\n          </p>\n        </div>\n      </div>\n    </Link>\n  );\n};\n\nexport default CategoryCard;\n"], "names": [], "mappings": ";;;;AACA;;;AAcA,MAAM,eAA4C,CAAC,EAAE,QAAQ,EAAE;IAC7D,qBACE,6LAAC,+JAAA,CAAA,UAAI;QAAC,MAAM,CAAC,YAAY,EAAE,SAAS,IAAI,EAAE;kBACxC,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,WAAU;gCACV,OAAO;oCAAE,iBAAiB,SAAS,KAAK,IAAI;gCAAU;0CAEtD,cAAA,6LAAC;oCAAK,WAAU;8CACb,SAAS,IAAI,IAAI;;;;;;;;;;;0CAGtB,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDACX,SAAS,IAAI;;;;;;kDAEhB,6LAAC;wCAAE,WAAU;;4CACV,SAAS,SAAS;4CAAC;;;;;;;;;;;;;;;;;;;kCAK1B,6LAAC;wBAAE,WAAU;kCACV,SAAS,WAAW;;;;;;;;;;;;;;;;;;;;;;AAMjC;KA/BM;uCAiCS", "debugId": null}}, {"offset": {"line": 1182, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/LoadingSpinner.tsx"], "sourcesContent": ["'use client';\n\ninterface LoadingSpinnerProps {\n  size?: 'sm' | 'md' | 'lg';\n  className?: string;\n}\n\nexport default function LoadingSpinner({ size = 'md', className = '' }: LoadingSpinnerProps) {\n  const sizeClasses = {\n    sm: 'w-4 h-4',\n    md: 'w-8 h-8',\n    lg: 'w-12 h-12'\n  };\n\n  return (\n    <div className={`flex justify-center items-center ${className}`}>\n      <div className={`${sizeClasses[size]} border-4 border-gray-200 border-t-blue-600 rounded-full animate-spin`}></div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAOe,SAAS,eAAe,EAAE,OAAO,IAAI,EAAE,YAAY,EAAE,EAAuB;IACzF,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAC,iCAAiC,EAAE,WAAW;kBAC7D,cAAA,6LAAC;YAAI,WAAW,GAAG,WAAW,CAAC,KAAK,CAAC,qEAAqE,CAAC;;;;;;;;;;;AAGjH;KAZwB", "debugId": null}}, {"offset": {"line": 1221, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/ErrorMessage.tsx"], "sourcesContent": ["'use client';\n\nimport { AlertCircle, X } from 'lucide-react';\n\ninterface ErrorMessageProps {\n  message: string;\n  onClose?: () => void;\n  className?: string;\n}\n\nexport default function ErrorMessage({ message, onClose, className = '' }: ErrorMessageProps) {\n  return (\n    <div className={`bg-red-50 border border-red-200 rounded-lg p-4 ${className}`}>\n      <div className=\"flex items-start\">\n        <AlertCircle className=\"w-5 h-5 text-red-500 mt-0.5 mr-3 flex-shrink-0\" />\n        <div className=\"flex-1\">\n          <p className=\"text-red-800 text-sm\">{message}</p>\n        </div>\n        {onClose && (\n          <button\n            onClick={onClose}\n            className=\"ml-3 text-red-400 hover:text-red-600 transition-colors\"\n          >\n            <X className=\"w-4 h-4\" />\n          </button>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAFA;;;AAUe,SAAS,aAAa,EAAE,OAAO,EAAE,OAAO,EAAE,YAAY,EAAE,EAAqB;IAC1F,qBACE,6LAAC;QAAI,WAAW,CAAC,+CAA+C,EAAE,WAAW;kBAC3E,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC,uNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;8BACvB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;gBAEtC,yBACC,6LAAC;oBACC,SAAS;oBACT,WAAU;8BAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;wBAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;AAMzB;KAnBwB", "debugId": null}}, {"offset": {"line": 1297, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/lib/api.ts"], "sourcesContent": ["// API客户端工具类\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3001/api';\n\nexport interface ApiResponse<T = any> {\n  success: boolean;\n  data?: T;\n  error?: string;\n  message?: string;\n  details?: string[];\n}\n\nexport interface PaginationInfo {\n  currentPage: number;\n  totalPages: number;\n  totalItems: number;\n  itemsPerPage: number;\n  hasNextPage: boolean;\n  hasPrevPage: boolean;\n}\n\nexport interface Tool {\n  _id: string;\n  name: string;\n  description: string;\n  longDescription?: string;\n  website: string;\n  logo?: string;\n  category: string;\n  tags: string[];\n  pricing: 'free' | 'freemium' | 'paid';\n  pricingDetails?: string;\n  features: string[];\n  screenshots?: string[];\n  submittedBy: string;\n  submittedAt: string;\n  publishedAt?: string;\n  status: 'pending' | 'approved' | 'rejected';\n  reviewNotes?: string;\n  reviewedBy?: string;\n  reviewedAt?: string;\n  views: number;\n  likes: number;\n  isActive: boolean;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface ToolsResponse {\n  tools: Tool[];\n  pagination: PaginationInfo;\n  stats?: {\n    total: number;\n    pending: number;\n    approved: number;\n    rejected: number;\n  };\n}\n\nexport interface AdminStats {\n  overview: {\n    totalTools: number;\n    pendingTools: number;\n    approvedTools: number;\n    rejectedTools: number;\n    totalViews: number;\n    totalLikes: number;\n    recentSubmissions: number;\n    recentApprovals: number;\n    recentRejections: number;\n    avgReviewTime: number;\n  };\n  categoryStats: Array<{\n    _id: string;\n    count: number;\n    totalViews: number;\n    totalLikes: number;\n  }>;\n  topTools: Array<{\n    _id: string;\n    name: string;\n    category: string;\n    views: number;\n    likes: number;\n  }>;\n  recentActivity: Array<{\n    _id: string;\n    name: string;\n    submittedBy: string;\n    submittedAt: string;\n    status: string;\n    reviewedAt?: string;\n    reviewedBy?: string;\n  }>;\n  dailyStats: Array<{\n    date: string;\n    day: string;\n    submissions: number;\n    approvals: number;\n    rejections: number;\n  }>;\n  timeRange: string;\n}\n\nexport interface Category {\n  id: string;\n  name: string;\n  count: number;\n  totalViews: number;\n  totalLikes: number;\n}\n\nexport interface CategoriesResponse {\n  categories: Category[];\n  overview: {\n    totalTools: number;\n    totalViews: number;\n    totalLikes: number;\n  };\n}\n\nclass ApiClient {\n  private baseURL: string;\n\n  constructor(baseURL: string = API_BASE_URL) {\n    this.baseURL = baseURL;\n  }\n\n  private async request<T>(\n    endpoint: string,\n    options: RequestInit = {}\n  ): Promise<ApiResponse<T>> {\n    try {\n      const url = `${this.baseURL}${endpoint}`;\n      const config: RequestInit = {\n        headers: {\n          'Content-Type': 'application/json',\n          ...options.headers,\n        },\n        ...options,\n      };\n\n      const response = await fetch(url, config);\n      const data = await response.json();\n\n      if (!response.ok) {\n        throw new Error(data.error || `HTTP error! status: ${response.status}`);\n      }\n\n      return data;\n    } catch (error) {\n      console.error('API request failed:', error);\n      return {\n        success: false,\n        error: error instanceof Error ? error.message : '请求失败',\n      };\n    }\n  }\n\n  // 工具相关API\n  async getTools(params?: {\n    page?: number;\n    limit?: number;\n    category?: string;\n    status?: string;\n    search?: string;\n    sort?: string;\n    order?: string;\n  }): Promise<ApiResponse<ToolsResponse>> {\n    const searchParams = new URLSearchParams();\n    if (params) {\n      Object.entries(params).forEach(([key, value]) => {\n        if (value !== undefined) {\n          searchParams.append(key, value.toString());\n        }\n      });\n    }\n    const query = searchParams.toString();\n    return this.request<ToolsResponse>(`/tools${query ? `?${query}` : ''}`);\n  }\n\n  async getTool(id: string): Promise<ApiResponse<Tool>> {\n    return this.request<Tool>(`/tools/${id}`);\n  }\n\n  async createTool(toolData: Partial<Tool>): Promise<ApiResponse<Tool>> {\n    return this.request<Tool>('/tools', {\n      method: 'POST',\n      body: JSON.stringify(toolData),\n    });\n  }\n\n  async updateTool(id: string, toolData: Partial<Tool>): Promise<ApiResponse<Tool>> {\n    return this.request<Tool>(`/tools/${id}`, {\n      method: 'PUT',\n      body: JSON.stringify(toolData),\n    });\n  }\n\n  async deleteTool(id: string): Promise<ApiResponse<void>> {\n    return this.request<void>(`/tools/${id}`, {\n      method: 'DELETE',\n    });\n  }\n\n  // 管理员API\n  async getAdminTools(params?: {\n    page?: number;\n    limit?: number;\n    status?: string;\n    search?: string;\n    sort?: string;\n    order?: string;\n  }): Promise<ApiResponse<ToolsResponse>> {\n    const searchParams = new URLSearchParams();\n    if (params) {\n      Object.entries(params).forEach(([key, value]) => {\n        if (value !== undefined) {\n          searchParams.append(key, value.toString());\n        }\n      });\n    }\n    const query = searchParams.toString();\n    return this.request<ToolsResponse>(`/admin/tools${query ? `?${query}` : ''}`);\n  }\n\n  async approveTool(id: string, data: {\n    reviewedBy?: string;\n    reviewNotes?: string;\n    publishedAt?: string;\n  }): Promise<ApiResponse<Tool>> {\n    return this.request<Tool>(`/admin/tools/${id}/approve`, {\n      method: 'POST',\n      body: JSON.stringify(data),\n    });\n  }\n\n  async rejectTool(id: string, data: {\n    reviewedBy?: string;\n    rejectReason: string;\n  }): Promise<ApiResponse<Tool>> {\n    return this.request<Tool>(`/admin/tools/${id}/reject`, {\n      method: 'POST',\n      body: JSON.stringify(data),\n    });\n  }\n\n  async getAdminStats(timeRange?: string): Promise<ApiResponse<AdminStats>> {\n    const query = timeRange ? `?timeRange=${timeRange}` : '';\n    return this.request<AdminStats>(`/admin/stats${query}`);\n  }\n\n  // 分类API\n  async getCategories(): Promise<ApiResponse<CategoriesResponse>> {\n    return this.request<CategoriesResponse>('/categories');\n  }\n}\n\n// 创建默认的API客户端实例\nexport const apiClient = new ApiClient();\n\n// 导出API客户端类以便在需要时创建新实例\nexport { ApiClient };\n"], "names": [], "mappings": "AAAA,YAAY;;;;;AACS;AAArB,MAAM,eAAe,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,wBAAwB,IAAI;AAuH7D,MAAM;IACI,QAAgB;IAExB,YAAY,UAAkB,YAAY,CAAE;QAC1C,IAAI,CAAC,OAAO,GAAG;IACjB;IAEA,MAAc,QACZ,QAAgB,EAChB,UAAuB,CAAC,CAAC,EACA;QACzB,IAAI;YACF,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,GAAG,UAAU;YACxC,MAAM,SAAsB;gBAC1B,SAAS;oBACP,gBAAgB;oBAChB,GAAG,QAAQ,OAAO;gBACpB;gBACA,GAAG,OAAO;YACZ;YAEA,MAAM,WAAW,MAAM,MAAM,KAAK;YAClC,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,KAAK,KAAK,IAAI,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YACxE;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uBAAuB;YACrC,OAAO;gBACL,SAAS;gBACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAClD;QACF;IACF;IAEA,UAAU;IACV,MAAM,SAAS,MAQd,EAAuC;QACtC,MAAM,eAAe,IAAI;QACzB,IAAI,QAAQ;YACV,OAAO,OAAO,CAAC,QAAQ,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;gBAC1C,IAAI,UAAU,WAAW;oBACvB,aAAa,MAAM,CAAC,KAAK,MAAM,QAAQ;gBACzC;YACF;QACF;QACA,MAAM,QAAQ,aAAa,QAAQ;QACnC,OAAO,IAAI,CAAC,OAAO,CAAgB,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC,EAAE,OAAO,GAAG,IAAI;IACxE;IAEA,MAAM,QAAQ,EAAU,EAA8B;QACpD,OAAO,IAAI,CAAC,OAAO,CAAO,CAAC,OAAO,EAAE,IAAI;IAC1C;IAEA,MAAM,WAAW,QAAuB,EAA8B;QACpE,OAAO,IAAI,CAAC,OAAO,CAAO,UAAU;YAClC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,WAAW,EAAU,EAAE,QAAuB,EAA8B;QAChF,OAAO,IAAI,CAAC,OAAO,CAAO,CAAC,OAAO,EAAE,IAAI,EAAE;YACxC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,WAAW,EAAU,EAA8B;QACvD,OAAO,IAAI,CAAC,OAAO,CAAO,CAAC,OAAO,EAAE,IAAI,EAAE;YACxC,QAAQ;QACV;IACF;IAEA,SAAS;IACT,MAAM,cAAc,MAOnB,EAAuC;QACtC,MAAM,eAAe,IAAI;QACzB,IAAI,QAAQ;YACV,OAAO,OAAO,CAAC,QAAQ,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;gBAC1C,IAAI,UAAU,WAAW;oBACvB,aAAa,MAAM,CAAC,KAAK,MAAM,QAAQ;gBACzC;YACF;QACF;QACA,MAAM,QAAQ,aAAa,QAAQ;QACnC,OAAO,IAAI,CAAC,OAAO,CAAgB,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC,EAAE,OAAO,GAAG,IAAI;IAC9E;IAEA,MAAM,YAAY,EAAU,EAAE,IAI7B,EAA8B;QAC7B,OAAO,IAAI,CAAC,OAAO,CAAO,CAAC,aAAa,EAAE,GAAG,QAAQ,CAAC,EAAE;YACtD,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,WAAW,EAAU,EAAE,IAG5B,EAA8B;QAC7B,OAAO,IAAI,CAAC,OAAO,CAAO,CAAC,aAAa,EAAE,GAAG,OAAO,CAAC,EAAE;YACrD,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,cAAc,SAAkB,EAAoC;QACxE,MAAM,QAAQ,YAAY,CAAC,WAAW,EAAE,WAAW,GAAG;QACtD,OAAO,IAAI,CAAC,OAAO,CAAa,CAAC,YAAY,EAAE,OAAO;IACxD;IAEA,QAAQ;IACR,MAAM,gBAA0D;QAC9D,OAAO,IAAI,CAAC,OAAO,CAAqB;IAC1C;AACF;AAGO,MAAM,YAAY,IAAI", "debugId": null}}, {"offset": {"line": 1411, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/app/page.tsx"], "sourcesContent": ["'use client';\nimport React, { useState, useEffect } from 'react';\nimport Link from 'next/link';\nimport Layout from '@/components/Layout';\nimport ToolCard from '@/components/ToolCard';\nimport CategoryCard from '@/components/CategoryCard';\nimport LoadingSpinner from '@/components/LoadingSpinner';\nimport ErrorMessage from '@/components/ErrorMessage';\nimport LoginModal from '@/components/auth/LoginModal';\nimport { apiClient, Tool } from '@/lib/api';\nimport { Search, TrendingUp, Star, Zap } from 'lucide-react';\n\n// 分类数据\n\nconst categories = [\n  {\n    _id: '1',\n    name: '文本生成',\n    slug: 'text-generation',\n    description: 'AI tools for generating and editing text content',\n    icon: '📝',\n    color: '#3B82F6',\n    toolCount: 25\n  },\n  {\n    _id: '2',\n    name: '图像生成',\n    slug: 'image-generation',\n    description: 'AI tools for creating and editing images',\n    icon: '🎨',\n    color: '#8B5CF6',\n    toolCount: 18\n  },\n  {\n    _id: '3',\n    name: '代码生成',\n    slug: 'code-generation',\n    description: 'AI tools for writing and debugging code',\n    icon: '💻',\n    color: '#F59E0B',\n    toolCount: 12\n  },\n  {\n    _id: '4',\n    name: '数据分析',\n    slug: 'data-analysis',\n    description: 'AI tools for analyzing and visualizing data',\n    icon: '📊',\n    color: '#06B6D4',\n    toolCount: 15\n  }\n];\n\nexport default function Home() {\n  const [featuredTools, setFeaturedTools] = useState<Tool[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [isLoginModalOpen, setIsLoginModalOpen] = useState(false);\n\n  useEffect(() => {\n    fetchFeaturedTools();\n  }, []);\n\n  const fetchFeaturedTools = async () => {\n    try {\n      setLoading(true);\n      setError('');\n\n      const response = await apiClient.getTools({\n        status: 'approved',\n        limit: 6,\n        sort: 'views',\n        order: 'desc'\n      });\n\n      if (response.success && response.data) {\n        setFeaturedTools(response.data.tools);\n      } else {\n        setError(response.error || '获取工具列表失败');\n      }\n    } catch (err) {\n      setError('网络错误，请重试');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  if (loading) {\n    return (\n      <Layout>\n        <div className=\"min-h-screen flex items-center justify-center\">\n          <LoadingSpinner size=\"lg\" />\n        </div>\n      </Layout>\n    );\n  }\n\n  return (\n    <Layout>\n      {/* Error Message */}\n      {error && (\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-8\">\n          <ErrorMessage\n            message={error}\n            onClose={() => setError('')}\n          />\n        </div>\n      )}\n\n      {/* Hero Section */}\n      <section className=\"bg-gradient-to-br from-blue-50 to-indigo-100 py-20\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center\">\n            <h1 className=\"text-4xl md:text-6xl font-bold text-gray-900 mb-6\">\n              发现最好的\n              <span className=\"text-blue-600\"> AI 工具</span>\n            </h1>\n            <p className=\"text-xl text-gray-600 mb-8 max-w-3xl mx-auto\">\n              探索精选的人工智能工具集合，提升您的工作效率和创造力。从文本生成到图像创作，找到适合您需求的完美工具。\n            </p>\n\n            {/* Search Bar */}\n            <div className=\"max-w-2xl mx-auto mb-8\">\n              <div className=\"relative\">\n                <input\n                  type=\"text\"\n                  placeholder=\"搜索 AI 工具、分类或功能...\"\n                  className=\"w-full pl-12 pr-4 py-4 text-lg border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent shadow-lg\"\n                />\n                <Search className=\"absolute left-4 top-4 h-6 w-6 text-gray-400\" />\n              </div>\n            </div>\n\n            {/* CTA Buttons */}\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n              <Link\n                href=\"/tools\"\n                className=\"inline-flex items-center px-8 py-3 border border-transparent text-base font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 transition-colors\"\n              >\n                <Zap className=\"mr-2 h-5 w-5\" />\n                浏览所有工具\n              </Link>\n              <Link\n                href=\"/submit\"\n                className=\"inline-flex items-center px-8 py-3 border border-gray-300 text-base font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 transition-colors\"\n              >\n                提交您的工具\n              </Link>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Featured Tools Section */}\n      <section className=\"py-16 bg-white\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center mb-12\">\n            <h2 className=\"text-3xl font-bold text-gray-900 mb-4\">\n              <TrendingUp className=\"inline-block mr-2 h-8 w-8 text-blue-600\" />\n              热门工具\n            </h2>\n            <p className=\"text-lg text-gray-600\">\n              最受欢迎和评价最高的 AI 工具\n            </p>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n            {featuredTools.map((tool) => (\n              <ToolCard\n                key={tool._id}\n                tool={tool}\n                onLoginRequired={() => setIsLoginModalOpen(true)}\n              />\n            ))}\n          </div>\n\n          <div className=\"text-center mt-8\">\n            <Link\n              href=\"/tools\"\n              className=\"inline-flex items-center px-6 py-3 border border-blue-600 text-base font-medium rounded-lg text-blue-600 hover:bg-blue-50 transition-colors\"\n            >\n              查看更多工具\n            </Link>\n          </div>\n        </div>\n      </section>\n\n      {/* Categories Section */}\n      <section className=\"py-16 bg-gray-50\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center mb-12\">\n            <h2 className=\"text-3xl font-bold text-gray-900 mb-4\">\n              <Star className=\"inline-block mr-2 h-8 w-8 text-blue-600\" />\n              热门分类\n            </h2>\n            <p className=\"text-lg text-gray-600\">\n              按功能分类浏览 AI 工具\n            </p>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n            {categories.map((category) => (\n              <CategoryCard key={category._id} category={category} />\n            ))}\n          </div>\n\n          <div className=\"text-center mt-8\">\n            <Link\n              href=\"/categories\"\n              className=\"inline-flex items-center px-6 py-3 border border-blue-600 text-base font-medium rounded-lg text-blue-600 hover:bg-blue-50 transition-colors\"\n            >\n              查看所有分类\n            </Link>\n          </div>\n        </div>\n      </section>\n\n      {/* Stats Section */}\n      <section className=\"py-16 bg-blue-600\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8 text-center\">\n            <div>\n              <div className=\"text-4xl font-bold text-white mb-2\">500+</div>\n              <div className=\"text-blue-100\">AI 工具</div>\n            </div>\n            <div>\n              <div className=\"text-4xl font-bold text-white mb-2\">50+</div>\n              <div className=\"text-blue-100\">工具分类</div>\n            </div>\n            <div>\n              <div className=\"text-4xl font-bold text-white mb-2\">10K+</div>\n              <div className=\"text-blue-100\">用户访问</div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Login Modal */}\n      <LoginModal\n        isOpen={isLoginModalOpen}\n        onClose={() => setIsLoginModalOpen(false)}\n      />\n    </Layout>\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;;;AAVA;;;;;;;;;;;AAYA,OAAO;AAEP,MAAM,aAAa;IACjB;QACE,KAAK;QACL,MAAM;QACN,MAAM;QACN,aAAa;QACb,MAAM;QACN,OAAO;QACP,WAAW;IACb;IACA;QACE,KAAK;QACL,MAAM;QACN,MAAM;QACN,aAAa;QACb,MAAM;QACN,OAAO;QACP,WAAW;IACb;IACA;QACE,KAAK;QACL,MAAM;QACN,MAAM;QACN,aAAa;QACb,MAAM;QACN,OAAO;QACP,WAAW;IACb;IACA;QACE,KAAK;QACL,MAAM;QACN,MAAM;QACN,aAAa;QACb,MAAM;QACN,OAAO;QACP,WAAW;IACb;CACD;AAEc,SAAS;;IACtB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR;QACF;yBAAG,EAAE;IAEL,MAAM,qBAAqB;QACzB,IAAI;YACF,WAAW;YACX,SAAS;YAET,MAAM,WAAW,MAAM,oHAAA,CAAA,YAAS,CAAC,QAAQ,CAAC;gBACxC,QAAQ;gBACR,OAAO;gBACP,MAAM;gBACN,OAAO;YACT;YAEA,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;gBACrC,iBAAiB,SAAS,IAAI,CAAC,KAAK;YACtC,OAAO;gBACL,SAAS,SAAS,KAAK,IAAI;YAC7B;QACF,EAAE,OAAO,KAAK;YACZ,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC,+HAAA,CAAA,UAAM;sBACL,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,uIAAA,CAAA,UAAc;oBAAC,MAAK;;;;;;;;;;;;;;;;IAI7B;IAEA,qBACE,6LAAC,+HAAA,CAAA,UAAM;;YAEJ,uBACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,qIAAA,CAAA,UAAY;oBACX,SAAS;oBACT,SAAS,IAAM,SAAS;;;;;;;;;;;0BAM9B,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;;oCAAoD;kDAEhE,6LAAC;wCAAK,WAAU;kDAAgB;;;;;;;;;;;;0CAElC,6LAAC;gCAAE,WAAU;0CAA+C;;;;;;0CAK5D,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,MAAK;4CACL,aAAY;4CACZ,WAAU;;;;;;sDAEZ,6LAAC,yMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAKtB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;;0DAEV,6LAAC,mMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAGlC,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAST,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC,qNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;wCAA4C;;;;;;;8CAGpE,6LAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;sCAKvC,6LAAC;4BAAI,WAAU;sCACZ,cAAc,GAAG,CAAC,CAAC,qBAClB,6LAAC,iIAAA,CAAA,UAAQ;oCAEP,MAAM;oCACN,iBAAiB,IAAM,oBAAoB;mCAFtC,KAAK,GAAG;;;;;;;;;;sCAOnB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;0BAQP,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAA4C;;;;;;;8CAG9D,6LAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;sCAKvC,6LAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC,yBACf,6LAAC,qIAAA,CAAA,UAAY;oCAAoB,UAAU;mCAAxB,SAAS,GAAG;;;;;;;;;;sCAInC,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;0BAQP,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC;wCAAI,WAAU;kDAAqC;;;;;;kDACpD,6LAAC;wCAAI,WAAU;kDAAgB;;;;;;;;;;;;0CAEjC,6LAAC;;kDACC,6LAAC;wCAAI,WAAU;kDAAqC;;;;;;kDACpD,6LAAC;wCAAI,WAAU;kDAAgB;;;;;;;;;;;;0CAEjC,6LAAC;;kDACC,6LAAC;wCAAI,WAAU;kDAAqC;;;;;;kDACpD,6LAAC;wCAAI,WAAU;kDAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOvC,6LAAC,2IAAA,CAAA,UAAU;gBACT,QAAQ;gBACR,SAAS,IAAM,oBAAoB;;;;;;;;;;;;AAI3C;GA/LwB;KAAA", "debugId": null}}]}
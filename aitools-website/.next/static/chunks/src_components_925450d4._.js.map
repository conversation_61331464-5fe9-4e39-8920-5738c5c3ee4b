{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/providers/SessionProvider.tsx"], "sourcesContent": ["'use client';\n\nimport { SessionProvider as NextAuthSessionProvider } from 'next-auth/react';\nimport { ReactNode } from 'react';\n\ninterface SessionProviderProps {\n  children: ReactNode;\n}\n\nexport default function SessionProvider({ children }: SessionProviderProps) {\n  return (\n    <NextAuthSessionProvider>\n      {children}\n    </NextAuthSessionProvider>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AASe,SAAS,gBAAgB,EAAE,QAAQ,EAAwB;IACxE,qBACE,6LAAC,iJAAA,CAAA,kBAAuB;kBACrB;;;;;;AAGP;KANwB", "debugId": null}}, {"offset": {"line": 36, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/auth/LoginModal.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { signIn } from 'next-auth/react';\nimport { FaGoogle, FaGithub, FaEnvelope, FaTimes } from 'react-icons/fa';\n\ninterface LoginModalProps {\n  isOpen: boolean;\n  onClose: () => void;\n}\n\ntype LoginStep = 'method' | 'email' | 'code';\n\nexport default function LoginModal({ isOpen, onClose }: LoginModalProps) {\n  const [step, setStep] = useState<LoginStep>('method');\n  const [email, setEmail] = useState('');\n  const [verificationToken, setVerificationToken] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n  const [emailError, setEmailError] = useState('');\n\n  const showToast = (message: string, type: 'success' | 'error' = 'success') => {\n    // Simple toast implementation\n    const toast = document.createElement('div');\n    toast.className = `fixed top-4 right-4 p-4 rounded-lg text-white z-50 ${\n      type === 'success' ? 'bg-green-500' : 'bg-red-500'\n    }`;\n    toast.textContent = message;\n    document.body.appendChild(toast);\n    setTimeout(() => document.body.removeChild(toast), 3000);\n  };\n\n  const handleClose = () => {\n    setStep('method');\n    setEmail('');\n    setVerificationToken('');\n    setEmailError('');\n    onClose();\n  };\n\n  const handleOAuthLogin = async (provider: 'google' | 'github') => {\n    try {\n      setIsLoading(true);\n      await signIn(provider, { callbackUrl: '/' });\n    } catch (error) {\n      showToast('登录失败，请稍后重试', 'error');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleEmailSubmit = async () => {\n    if (!email) {\n      setEmailError('请输入邮箱地址');\n      return;\n    }\n\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    if (!emailRegex.test(email)) {\n      setEmailError('请输入有效的邮箱地址');\n      return;\n    }\n\n    setEmailError('');\n    setIsLoading(true);\n\n    try {\n      const response = await fetch('/api/auth/send-code', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ email }),\n      });\n\n      const data = await response.json();\n\n      if (data.success) {\n        setVerificationToken(data.token);\n        setStep('code');\n        showToast('验证码已发送，请查看您的邮箱');\n      } else {\n        showToast(data.error || '发送失败，请稍后重试', 'error');\n      }\n    } catch (error) {\n      showToast('网络错误，请检查网络连接', 'error');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleCodeVerify = async (code: string) => {\n    if (code.length !== 6) return;\n\n    setIsLoading(true);\n\n    try {\n      const result = await signIn('email-code', {\n        email,\n        code,\n        token: verificationToken,\n        redirect: false,\n      });\n\n      if (result?.ok) {\n        showToast('登录成功，欢迎回来！');\n        handleClose();\n        // NextAuth会自动更新session，不需要手动刷新页面\n      } else {\n        showToast(result?.error || '验证码错误', 'error');\n      }\n    } catch (error) {\n      showToast('网络错误，请检查网络连接', 'error');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const renderMethodStep = () => (\n    <div className=\"space-y-4\">\n      <p className=\"text-gray-600 text-center\">\n        选择登录方式\n      </p>\n\n      <div className=\"space-y-3\">\n        <button\n          className=\"w-full flex items-center justify-center gap-3 px-4 py-3 border border-red-300 rounded-lg text-red-700 hover:bg-red-50 transition-colors disabled:opacity-50\"\n          onClick={() => handleOAuthLogin('google')}\n          disabled={isLoading}\n        >\n          <FaGoogle />\n          使用 Google 登录\n        </button>\n\n        <button\n          className=\"w-full flex items-center justify-center gap-3 px-4 py-3 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors disabled:opacity-50\"\n          onClick={() => handleOAuthLogin('github')}\n          disabled={isLoading}\n        >\n          <FaGithub />\n          使用 GitHub 登录\n        </button>\n      </div>\n\n      <div className=\"relative\">\n        <div className=\"absolute inset-0 flex items-center\">\n          <div className=\"w-full border-t border-gray-300\" />\n        </div>\n        <div className=\"relative flex justify-center text-sm\">\n          <span className=\"px-2 bg-white text-gray-500\">或</span>\n        </div>\n      </div>\n\n      <button\n        className=\"w-full flex items-center justify-center gap-3 px-4 py-3 border border-blue-300 rounded-lg text-blue-700 hover:bg-blue-50 transition-colors\"\n        onClick={() => setStep('email')}\n      >\n        <FaEnvelope />\n        使用邮箱登录\n      </button>\n    </div>\n  );\n\n  const renderEmailStep = () => (\n    <div className=\"space-y-4\">\n      <p className=\"text-gray-600 text-center\">\n        输入您的邮箱地址，我们将发送验证码\n      </p>\n\n      <div>\n        <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n          邮箱地址\n        </label>\n        <input\n          type=\"email\"\n          value={email}\n          onChange={(e) => setEmail(e.target.value)}\n          placeholder=\"请输入邮箱地址\"\n          onKeyPress={(e) => e.key === 'Enter' && handleEmailSubmit()}\n          className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n        />\n        {emailError && (\n          <p className=\"mt-1 text-sm text-red-600\">{emailError}</p>\n        )}\n      </div>\n\n      <div className=\"space-y-3\">\n        <button\n          className=\"w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50\"\n          onClick={handleEmailSubmit}\n          disabled={isLoading}\n        >\n          {isLoading ? '发送中...' : '发送验证码'}\n        </button>\n\n        <button\n          className=\"w-full px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors\"\n          onClick={() => setStep('method')}\n        >\n          返回\n        </button>\n      </div>\n    </div>\n  );\n\n  const handleCodeInputChange = (index: number, value: string) => {\n    if (value.length > 1) return;\n\n    const inputs = document.querySelectorAll('.code-input') as NodeListOf<HTMLInputElement>;\n    inputs[index].value = value;\n\n    // Auto-focus next input\n    if (value && index < 5) {\n      inputs[index + 1]?.focus();\n    }\n\n    // Check if all inputs are filled\n    const code = Array.from(inputs).map(input => input.value).join('');\n    if (code.length === 6) {\n      handleCodeVerify(code);\n    }\n  };\n\n  const renderCodeStep = () => (\n    <div className=\"space-y-4\">\n      <p className=\"text-gray-600 text-center\">\n        请输入发送到 {email} 的6位验证码\n      </p>\n\n      <div>\n        <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n          验证码\n        </label>\n        <div className=\"flex justify-center gap-2\">\n          {[0, 1, 2, 3, 4, 5].map((index) => (\n            <input\n              key={index}\n              type=\"text\"\n              maxLength={1}\n              onChange={(e) => handleCodeInputChange(index, e.target.value)}\n              disabled={isLoading}\n              className=\"code-input w-12 h-12 text-center text-lg border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:opacity-50\"\n            />\n          ))}\n        </div>\n      </div>\n\n      <div className=\"space-y-3\">\n        <button\n          className=\"w-full px-4 py-2 text-sm text-gray-600 hover:text-gray-800 transition-colors\"\n          onClick={() => setStep('email')}\n        >\n          重新发送验证码\n        </button>\n\n        <button\n          className=\"w-full px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors\"\n          onClick={() => setStep('method')}\n        >\n          返回\n        </button>\n      </div>\n    </div>\n  );\n\n  if (!isOpen) return null;\n\n  return (\n    <div className=\"fixed inset-0 z-50 flex items-center justify-center\">\n      {/* Overlay */}\n      <div\n        className=\"absolute inset-0 bg-black bg-opacity-50\"\n        onClick={handleClose}\n      />\n\n      {/* Modal */}\n      <div className=\"relative bg-white rounded-lg shadow-xl max-w-md w-full mx-4\">\n        {/* Header */}\n        <div className=\"flex items-center justify-between p-6 border-b\">\n          <h2 className=\"text-xl font-semibold text-gray-900 text-center flex-1\">\n            {step === 'method' && '登录 AI Tools Directory'}\n            {step === 'email' && '邮箱登录'}\n            {step === 'code' && '输入验证码'}\n          </h2>\n          <button\n            onClick={handleClose}\n            className=\"text-gray-400 hover:text-gray-600 transition-colors\"\n          >\n            <FaTimes />\n          </button>\n        </div>\n\n        {/* Body */}\n        <div className=\"p-6\">\n          {step === 'method' && renderMethodStep()}\n          {step === 'email' && renderEmailStep()}\n          {step === 'code' && renderCodeStep()}\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAae,SAAS,WAAW,EAAE,MAAM,EAAE,OAAO,EAAmB;;IACrE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa;IAC5C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,YAAY,CAAC,SAAiB,OAA4B,SAAS;QACvE,8BAA8B;QAC9B,MAAM,QAAQ,SAAS,aAAa,CAAC;QACrC,MAAM,SAAS,GAAG,CAAC,mDAAmD,EACpE,SAAS,YAAY,iBAAiB,cACtC;QACF,MAAM,WAAW,GAAG;QACpB,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,WAAW,IAAM,SAAS,IAAI,CAAC,WAAW,CAAC,QAAQ;IACrD;IAEA,MAAM,cAAc;QAClB,QAAQ;QACR,SAAS;QACT,qBAAqB;QACrB,cAAc;QACd;IACF;IAEA,MAAM,mBAAmB,OAAO;QAC9B,IAAI;YACF,aAAa;YACb,MAAM,CAAA,GAAA,iJAAA,CAAA,SAAM,AAAD,EAAE,UAAU;gBAAE,aAAa;YAAI;QAC5C,EAAE,OAAO,OAAO;YACd,UAAU,cAAc;QAC1B,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,oBAAoB;QACxB,IAAI,CAAC,OAAO;YACV,cAAc;YACd;QACF;QAEA,MAAM,aAAa;QACnB,IAAI,CAAC,WAAW,IAAI,CAAC,QAAQ;YAC3B,cAAc;YACd;QACF;QAEA,cAAc;QACd,aAAa;QAEb,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,uBAAuB;gBAClD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE;gBAAM;YAC/B;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,qBAAqB,KAAK,KAAK;gBAC/B,QAAQ;gBACR,UAAU;YACZ,OAAO;gBACL,UAAU,KAAK,KAAK,IAAI,cAAc;YACxC;QACF,EAAE,OAAO,OAAO;YACd,UAAU,gBAAgB;QAC5B,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,mBAAmB,OAAO;QAC9B,IAAI,KAAK,MAAM,KAAK,GAAG;QAEvB,aAAa;QAEb,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,iJAAA,CAAA,SAAM,AAAD,EAAE,cAAc;gBACxC;gBACA;gBACA,OAAO;gBACP,UAAU;YACZ;YAEA,IAAI,QAAQ,IAAI;gBACd,UAAU;gBACV;YACA,iCAAiC;YACnC,OAAO;gBACL,UAAU,QAAQ,SAAS,SAAS;YACtC;QACF,EAAE,OAAO,OAAO;YACd,UAAU,gBAAgB;QAC5B,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,mBAAmB,kBACvB,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAE,WAAU;8BAA4B;;;;;;8BAIzC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BACC,WAAU;4BACV,SAAS,IAAM,iBAAiB;4BAChC,UAAU;;8CAEV,6LAAC,iJAAA,CAAA,WAAQ;;;;;gCAAG;;;;;;;sCAId,6LAAC;4BACC,WAAU;4BACV,SAAS,IAAM,iBAAiB;4BAChC,UAAU;;8CAEV,6LAAC,iJAAA,CAAA,WAAQ;;;;;gCAAG;;;;;;;;;;;;;8BAKhB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;;;;;;;;;;sCAEjB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAK,WAAU;0CAA8B;;;;;;;;;;;;;;;;;8BAIlD,6LAAC;oBACC,WAAU;oBACV,SAAS,IAAM,QAAQ;;sCAEvB,6LAAC,iJAAA,CAAA,aAAU;;;;;wBAAG;;;;;;;;;;;;;IAMpB,MAAM,kBAAkB,kBACtB,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAE,WAAU;8BAA4B;;;;;;8BAIzC,6LAAC;;sCACC,6LAAC;4BAAM,WAAU;sCAA+C;;;;;;sCAGhE,6LAAC;4BACC,MAAK;4BACL,OAAO;4BACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;4BACxC,aAAY;4BACZ,YAAY,CAAC,IAAM,EAAE,GAAG,KAAK,WAAW;4BACxC,WAAU;;;;;;wBAEX,4BACC,6LAAC;4BAAE,WAAU;sCAA6B;;;;;;;;;;;;8BAI9C,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BACC,WAAU;4BACV,SAAS;4BACT,UAAU;sCAET,YAAY,WAAW;;;;;;sCAG1B,6LAAC;4BACC,WAAU;4BACV,SAAS,IAAM,QAAQ;sCACxB;;;;;;;;;;;;;;;;;;IAOP,MAAM,wBAAwB,CAAC,OAAe;QAC5C,IAAI,MAAM,MAAM,GAAG,GAAG;QAEtB,MAAM,SAAS,SAAS,gBAAgB,CAAC;QACzC,MAAM,CAAC,MAAM,CAAC,KAAK,GAAG;QAEtB,wBAAwB;QACxB,IAAI,SAAS,QAAQ,GAAG;YACtB,MAAM,CAAC,QAAQ,EAAE,EAAE;QACrB;QAEA,iCAAiC;QACjC,MAAM,OAAO,MAAM,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAA,QAAS,MAAM,KAAK,EAAE,IAAI,CAAC;QAC/D,IAAI,KAAK,MAAM,KAAK,GAAG;YACrB,iBAAiB;QACnB;IACF;IAEA,MAAM,iBAAiB,kBACrB,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAE,WAAU;;wBAA4B;wBAC/B;wBAAM;;;;;;;8BAGhB,6LAAC;;sCACC,6LAAC;4BAAM,WAAU;sCAA+C;;;;;;sCAGhE,6LAAC;4BAAI,WAAU;sCACZ;gCAAC;gCAAG;gCAAG;gCAAG;gCAAG;gCAAG;6BAAE,CAAC,GAAG,CAAC,CAAC,sBACvB,6LAAC;oCAEC,MAAK;oCACL,WAAW;oCACX,UAAU,CAAC,IAAM,sBAAsB,OAAO,EAAE,MAAM,CAAC,KAAK;oCAC5D,UAAU;oCACV,WAAU;mCALL;;;;;;;;;;;;;;;;8BAWb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BACC,WAAU;4BACV,SAAS,IAAM,QAAQ;sCACxB;;;;;;sCAID,6LAAC;4BACC,WAAU;4BACV,SAAS,IAAM,QAAQ;sCACxB;;;;;;;;;;;;;;;;;;IAOP,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBACC,WAAU;gBACV,SAAS;;;;;;0BAIX,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;;oCACX,SAAS,YAAY;oCACrB,SAAS,WAAW;oCACpB,SAAS,UAAU;;;;;;;0CAEtB,6LAAC;gCACC,SAAS;gCACT,WAAU;0CAEV,cAAA,6LAAC,iJAAA,CAAA,UAAO;;;;;;;;;;;;;;;;kCAKZ,6LAAC;wBAAI,WAAU;;4BACZ,SAAS,YAAY;4BACrB,SAAS,WAAW;4BACpB,SAAS,UAAU;;;;;;;;;;;;;;;;;;;AAK9B;GA/RwB;KAAA", "debugId": null}}, {"offset": {"line": 527, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/auth/UserMenu.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { useSession, signOut } from 'next-auth/react';\nimport { FaUser, FaHeart, FaPlus, FaCog, FaSignOutAlt, FaSignInAlt, FaChevronDown } from 'react-icons/fa';\nimport { useRouter } from 'next/navigation';\nimport LoginModal from './LoginModal';\n\nexport default function UserMenu() {\n  const { data: session, status } = useSession();\n  const router = useRouter();\n  const [isLoginModalOpen, setIsLoginModalOpen] = useState(false);\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n\n  const handleSignOut = async () => {\n    await signOut({ callbackUrl: '/' });\n  };\n\n  const handleNavigation = (path: string) => {\n    setIsMenuOpen(false);\n    router.push(path);\n  };\n\n  // 如果正在加载，显示加载状态\n  if (status === 'loading') {\n    return (\n      <button\n        className=\"px-3 py-2 text-sm text-gray-600 bg-gray-100 rounded-lg animate-pulse\"\n        disabled\n      >\n        加载中...\n      </button>\n    );\n  }\n\n  // 如果未登录，显示登录按钮\n  if (!session) {\n    return (\n      <>\n        <button\n          className=\"flex items-center gap-2 px-3 py-2 text-sm border border-blue-300 text-blue-700 rounded-lg hover:bg-blue-50 transition-colors\"\n          onClick={() => setIsLoginModalOpen(true)}\n        >\n          <FaSignInAlt />\n          登录\n        </button>\n        <LoginModal isOpen={isLoginModalOpen} onClose={() => setIsLoginModalOpen(false)} />\n      </>\n    );\n  }\n\n  // 如果已登录，显示用户菜单\n  return (\n    <div className=\"relative\">\n      <button\n        className=\"flex items-center gap-2 p-1 rounded-lg hover:bg-gray-100 transition-colors\"\n        onClick={() => setIsMenuOpen(!isMenuOpen)}\n      >\n        <div className=\"w-8 h-8 rounded-full bg-gray-300 flex items-center justify-center overflow-hidden\">\n          {session.user?.image ? (\n            <img\n              src={session.user.image}\n              alt={session.user.name || ''}\n              className=\"w-full h-full object-cover\"\n            />\n          ) : (\n            <span className=\"text-sm font-medium text-gray-600\">\n              {session.user?.name?.charAt(0) || 'U'}\n            </span>\n          )}\n        </div>\n        <span className=\"text-sm hidden md:block\">\n          {session.user?.name}\n        </span>\n        <FaChevronDown className={`text-xs transition-transform ${isMenuOpen ? 'rotate-180' : ''}`} />\n      </button>\n\n      {isMenuOpen && (\n        <>\n          {/* Backdrop */}\n          <div\n            className=\"fixed inset-0 z-10\"\n            onClick={() => setIsMenuOpen(false)}\n          />\n\n          {/* Menu */}\n          <div className=\"absolute right-0 mt-2 w-64 bg-white rounded-lg shadow-lg border z-20\">\n            {/* 用户信息 */}\n            <div className=\"p-4 border-b\">\n              <div className=\"flex items-center gap-3\">\n                <div className=\"w-10 h-10 rounded-full bg-gray-300 flex items-center justify-center overflow-hidden\">\n                  {session.user?.image ? (\n                    <img\n                      src={session.user.image}\n                      alt={session.user.name || ''}\n                      className=\"w-full h-full object-cover\"\n                    />\n                  ) : (\n                    <span className=\"text-lg font-medium text-gray-600\">\n                      {session.user?.name?.charAt(0) || 'U'}\n                    </span>\n                  )}\n                </div>\n                <div>\n                  <p className=\"font-medium text-sm\">\n                    {session.user?.name}\n                  </p>\n                  <p className=\"text-gray-500 text-xs\">\n                    {session.user?.email}\n                  </p>\n                  {session.user?.role === 'admin' && (\n                    <span className=\"inline-block mt-1 px-2 py-1 text-xs bg-red-100 text-red-800 rounded\">\n                      管理员\n                    </span>\n                  )}\n                </div>\n              </div>\n            </div>\n\n            {/* 用户功能 */}\n            <div className=\"py-2\">\n              <button\n                className=\"w-full flex items-center gap-3 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors\"\n                onClick={() => handleNavigation('/profile')}\n              >\n                <FaUser />\n                个人资料\n              </button>\n\n              <button\n                className=\"w-full flex items-center gap-3 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors\"\n                onClick={() => handleNavigation('/profile/liked')}\n              >\n                <FaHeart />\n                我的收藏\n              </button>\n\n              <button\n                className=\"w-full flex items-center gap-3 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors\"\n                onClick={() => handleNavigation('/submit')}\n              >\n                <FaPlus />\n                提交工具\n              </button>\n            </div>\n\n            {/* 管理员功能 */}\n            {session.user?.role === 'admin' && (\n              <>\n                <div className=\"border-t py-2\">\n                  <button\n                    className=\"w-full flex items-center gap-3 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors\"\n                    onClick={() => handleNavigation('/admin')}\n                  >\n                    <FaCog />\n                    管理后台\n                  </button>\n                </div>\n              </>\n            )}\n\n            {/* 设置和登出 */}\n            <div className=\"border-t py-2\">\n              <button\n                className=\"w-full flex items-center gap-3 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors\"\n                onClick={() => handleNavigation('/settings')}\n              >\n                <FaCog />\n                设置\n              </button>\n\n              <button\n                className=\"w-full flex items-center gap-3 px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors\"\n                onClick={handleSignOut}\n              >\n                <FaSignOutAlt />\n                退出登录\n              </button>\n            </div>\n          </div>\n        </>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;;;AANA;;;;;;AAQe,SAAS;;IACtB,MAAM,EAAE,MAAM,OAAO,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD;IAC3C,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,gBAAgB;QACpB,MAAM,CAAA,GAAA,iJAAA,CAAA,UAAO,AAAD,EAAE;YAAE,aAAa;QAAI;IACnC;IAEA,MAAM,mBAAmB,CAAC;QACxB,cAAc;QACd,OAAO,IAAI,CAAC;IACd;IAEA,gBAAgB;IAChB,IAAI,WAAW,WAAW;QACxB,qBACE,6LAAC;YACC,WAAU;YACV,QAAQ;sBACT;;;;;;IAIL;IAEA,eAAe;IACf,IAAI,CAAC,SAAS;QACZ,qBACE;;8BACE,6LAAC;oBACC,WAAU;oBACV,SAAS,IAAM,oBAAoB;;sCAEnC,6LAAC,iJAAA,CAAA,cAAW;;;;;wBAAG;;;;;;;8BAGjB,6LAAC,2IAAA,CAAA,UAAU;oBAAC,QAAQ;oBAAkB,SAAS,IAAM,oBAAoB;;;;;;;;IAG/E;IAEA,eAAe;IACf,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBACC,WAAU;gBACV,SAAS,IAAM,cAAc,CAAC;;kCAE9B,6LAAC;wBAAI,WAAU;kCACZ,QAAQ,IAAI,EAAE,sBACb,6LAAC;4BACC,KAAK,QAAQ,IAAI,CAAC,KAAK;4BACvB,KAAK,QAAQ,IAAI,CAAC,IAAI,IAAI;4BAC1B,WAAU;;;;;iDAGZ,6LAAC;4BAAK,WAAU;sCACb,QAAQ,IAAI,EAAE,MAAM,OAAO,MAAM;;;;;;;;;;;kCAIxC,6LAAC;wBAAK,WAAU;kCACb,QAAQ,IAAI,EAAE;;;;;;kCAEjB,6LAAC,iJAAA,CAAA,gBAAa;wBAAC,WAAW,CAAC,6BAA6B,EAAE,aAAa,eAAe,IAAI;;;;;;;;;;;;YAG3F,4BACC;;kCAEE,6LAAC;wBACC,WAAU;wBACV,SAAS,IAAM,cAAc;;;;;;kCAI/B,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACZ,QAAQ,IAAI,EAAE,sBACb,6LAAC;gDACC,KAAK,QAAQ,IAAI,CAAC,KAAK;gDACvB,KAAK,QAAQ,IAAI,CAAC,IAAI,IAAI;gDAC1B,WAAU;;;;;qEAGZ,6LAAC;gDAAK,WAAU;0DACb,QAAQ,IAAI,EAAE,MAAM,OAAO,MAAM;;;;;;;;;;;sDAIxC,6LAAC;;8DACC,6LAAC;oDAAE,WAAU;8DACV,QAAQ,IAAI,EAAE;;;;;;8DAEjB,6LAAC;oDAAE,WAAU;8DACV,QAAQ,IAAI,EAAE;;;;;;gDAEhB,QAAQ,IAAI,EAAE,SAAS,yBACtB,6LAAC;oDAAK,WAAU;8DAAsE;;;;;;;;;;;;;;;;;;;;;;;0CAS9F,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,WAAU;wCACV,SAAS,IAAM,iBAAiB;;0DAEhC,6LAAC,iJAAA,CAAA,SAAM;;;;;4CAAG;;;;;;;kDAIZ,6LAAC;wCACC,WAAU;wCACV,SAAS,IAAM,iBAAiB;;0DAEhC,6LAAC,iJAAA,CAAA,UAAO;;;;;4CAAG;;;;;;;kDAIb,6LAAC;wCACC,WAAU;wCACV,SAAS,IAAM,iBAAiB;;0DAEhC,6LAAC,iJAAA,CAAA,SAAM;;;;;4CAAG;;;;;;;;;;;;;4BAMb,QAAQ,IAAI,EAAE,SAAS,yBACtB;0CACE,cAAA,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCACC,WAAU;wCACV,SAAS,IAAM,iBAAiB;;0DAEhC,6LAAC,iJAAA,CAAA,QAAK;;;;;4CAAG;;;;;;;;;;;;;0CAQjB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,WAAU;wCACV,SAAS,IAAM,iBAAiB;;0DAEhC,6LAAC,iJAAA,CAAA,QAAK;;;;;4CAAG;;;;;;;kDAIX,6LAAC;wCACC,WAAU;wCACV,SAAS;;0DAET,6LAAC,iJAAA,CAAA,eAAY;;;;;4CAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;AAShC;GAhLwB;;QACY,iJAAA,CAAA,aAAU;QAC7B,qIAAA,CAAA,YAAS;;;KAFF", "debugId": null}}, {"offset": {"line": 894, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/layout/Header.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport NextLink from 'next/link';\nimport { useRouter } from 'next/navigation';\nimport { FaBars, FaTimes, FaSearch } from 'react-icons/fa';\nimport UserMenu from '../auth/UserMenu';\n\nconst NavLink = ({ children, href }: { children: React.ReactNode; href: string }) => (\n  <NextLink\n    href={href}\n    className=\"px-2 py-1 rounded-md text-gray-700 hover:text-gray-900 hover:bg-gray-200 transition-colors\"\n  >\n    {children}\n  </NextLink>\n);\n\nconst Links = [\n  { name: '首页', href: '/' },\n  { name: '工具目录', href: '/tools' },\n  { name: '分类', href: '/categories' },\n  { name: '提交工具', href: '/submit' },\n];\n\nexport default function Header() {\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n  const router = useRouter();\n\n  const handleSearch = (e: React.FormEvent<HTMLFormElement>) => {\n    e.preventDefault();\n    const formData = new FormData(e.currentTarget);\n    const query = formData.get('search') as string;\n    if (query.trim()) {\n      router.push(`/search?q=${encodeURIComponent(query.trim())}`);\n    }\n  };\n\n  return (\n    <>\n      <header className=\"bg-white px-4 shadow-sm border-b border-gray-200\">\n        <div className=\"flex h-16 items-center justify-between\">\n          {/* Logo */}\n          <div className=\"flex items-center space-x-8\">\n            <NextLink href=\"/\" className=\"flex items-center space-x-2 hover:no-underline\">\n              <div className=\"w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center\">\n                <span className=\"text-white font-bold text-sm\">AI</span>\n              </div>\n              <span className=\"text-xl font-bold text-gray-900\">AI Tools</span>\n            </NextLink>\n\n            {/* Desktop Navigation */}\n            <nav className=\"hidden md:flex space-x-4\">\n              {Links.map((link) => (\n                <NavLink key={link.name} href={link.href}>\n                  {link.name}\n                </NavLink>\n              ))}\n            </nav>\n          </div>\n\n          {/* Search Bar */}\n          <div className=\"flex-1 max-w-md mx-8 hidden md:block\">\n            <form onSubmit={handleSearch}>\n              <div className=\"relative\">\n                <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                  <FaSearch className=\"text-gray-400\" />\n                </div>\n                <input\n                  name=\"search\"\n                  type=\"text\"\n                  placeholder=\"搜索 AI 工具...\"\n                  className=\"w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                />\n              </div>\n            </form>\n          </div>\n\n          {/* Right side */}\n          <div className=\"flex items-center\">\n            {/* User Menu */}\n            <UserMenu />\n\n            {/* Mobile menu button */}\n            <button\n              className=\"md:hidden ml-2 p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100\"\n              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}\n              aria-label=\"Open Menu\"\n            >\n              {isMobileMenuOpen ? <FaTimes /> : <FaBars />}\n            </button>\n          </div>\n        </div>\n\n        {/* Mobile Navigation */}\n        {isMobileMenuOpen && (\n          <div className=\"md:hidden pb-4\">\n            <nav className=\"space-y-4\">\n              {Links.map((link) => (\n                <NavLink key={link.name} href={link.href}>\n                  {link.name}\n                </NavLink>\n              ))}\n\n              {/* Mobile Search */}\n              <div className=\"pt-4\">\n                <form onSubmit={handleSearch}>\n                  <div className=\"relative\">\n                    <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                      <FaSearch className=\"text-gray-400\" />\n                    </div>\n                    <input\n                      name=\"search\"\n                      type=\"text\"\n                      placeholder=\"搜索 AI 工具...\"\n                      className=\"w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                    />\n                  </div>\n                </form>\n              </div>\n            </nav>\n          </div>\n        )}\n      </header>\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;;;AANA;;;;;;AAQA,MAAM,UAAU,CAAC,EAAE,QAAQ,EAAE,IAAI,EAA+C,iBAC9E,6LAAC,+JAAA,CAAA,UAAQ;QACP,MAAM;QACN,WAAU;kBAET;;;;;;KALC;AASN,MAAM,QAAQ;IACZ;QAAE,MAAM;QAAM,MAAM;IAAI;IACxB;QAAE,MAAM;QAAQ,MAAM;IAAS;IAC/B;QAAE,MAAM;QAAM,MAAM;IAAc;IAClC;QAAE,MAAM;QAAQ,MAAM;IAAU;CACjC;AAEc,SAAS;;IACtB,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,MAAM,WAAW,IAAI,SAAS,EAAE,aAAa;QAC7C,MAAM,QAAQ,SAAS,GAAG,CAAC;QAC3B,IAAI,MAAM,IAAI,IAAI;YAChB,OAAO,IAAI,CAAC,CAAC,UAAU,EAAE,mBAAmB,MAAM,IAAI,KAAK;QAC7D;IACF;IAEA,qBACE;kBACE,cAAA,6LAAC;YAAO,WAAU;;8BAChB,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,+JAAA,CAAA,UAAQ;oCAAC,MAAK;oCAAI,WAAU;;sDAC3B,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAU;0DAA+B;;;;;;;;;;;sDAEjD,6LAAC;4CAAK,WAAU;sDAAkC;;;;;;;;;;;;8CAIpD,6LAAC;oCAAI,WAAU;8CACZ,MAAM,GAAG,CAAC,CAAC,qBACV,6LAAC;4CAAwB,MAAM,KAAK,IAAI;sDACrC,KAAK,IAAI;2CADE,KAAK,IAAI;;;;;;;;;;;;;;;;sCAQ7B,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAK,UAAU;0CACd,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,iJAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;sDAEtB,6LAAC;4CACC,MAAK;4CACL,MAAK;4CACL,aAAY;4CACZ,WAAU;;;;;;;;;;;;;;;;;;;;;;sCAOlB,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC,yIAAA,CAAA,UAAQ;;;;;8CAGT,6LAAC;oCACC,WAAU;oCACV,SAAS,IAAM,oBAAoB,CAAC;oCACpC,cAAW;8CAEV,iCAAmB,6LAAC,iJAAA,CAAA,UAAO;;;;6DAAM,6LAAC,iJAAA,CAAA,SAAM;;;;;;;;;;;;;;;;;;;;;;gBAM9C,kCACC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;4BACZ,MAAM,GAAG,CAAC,CAAC,qBACV,6LAAC;oCAAwB,MAAM,KAAK,IAAI;8CACrC,KAAK,IAAI;mCADE,KAAK,IAAI;;;;;0CAMzB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAK,UAAU;8CACd,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,iJAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;;;;;;0DAEtB,6LAAC;gDACC,MAAK;gDACL,MAAK;gDACL,aAAY;gDACZ,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWhC;GArGwB;;QAEP,qIAAA,CAAA,YAAS;;;MAFF", "debugId": null}}]}
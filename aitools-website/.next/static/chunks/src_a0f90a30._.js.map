{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/Layout.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Link from 'next/link';\nimport { Search, Menu, X } from 'lucide-react';\n\ninterface LayoutProps {\n  children: React.ReactNode;\n}\n\nconst Layout: React.FC<LayoutProps> = ({ children }) => {\n  const [isMenuOpen, setIsMenuOpen] = React.useState(false);\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header */}\n      <header className=\"bg-white shadow-sm border-b border-gray-200\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center h-16\">\n            {/* Logo */}\n            <div className=\"flex items-center\">\n              <Link href=\"/\" className=\"flex items-center space-x-2\">\n                <div className=\"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center\">\n                  <span className=\"text-white font-bold text-sm\">AI</span>\n                </div>\n                <span className=\"text-xl font-bold text-gray-900\">AI Tools</span>\n              </Link>\n            </div>\n\n            {/* Desktop Navigation */}\n            <nav className=\"hidden md:flex items-center space-x-8\">\n              <Link href=\"/\" className=\"text-gray-700 hover:text-blue-600 transition-colors\">\n                首页\n              </Link>\n              <Link href=\"/tools\" className=\"text-gray-700 hover:text-blue-600 transition-colors\">\n                工具目录\n              </Link>\n              <Link href=\"/categories\" className=\"text-gray-700 hover:text-blue-600 transition-colors\">\n                分类\n              </Link>\n              <Link href=\"/submit\" className=\"text-gray-700 hover:text-blue-600 transition-colors\">\n                提交工具\n              </Link>\n              <Link href=\"/dashboard\" className=\"text-gray-700 hover:text-blue-600 transition-colors\">\n                仪表板\n              </Link>\n              {/* Admin Navigation */}\n              <div className=\"relative group\">\n                <button className=\"text-gray-700 hover:text-blue-600 transition-colors flex items-center\">\n                  管理员\n                  <svg className=\"w-4 h-4 ml-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 9l-7 7-7-7\" />\n                  </svg>\n                </button>\n                <div className=\"absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg border border-gray-200 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50\">\n                  <div className=\"py-1\">\n                    <Link href=\"/admin\" className=\"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors\">\n                      审核中心\n                    </Link>\n                    <Link href=\"/admin/dashboard\" className=\"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors\">\n                      统计面板\n                    </Link>\n                  </div>\n                </div>\n              </div>\n            </nav>\n\n            {/* Search Bar */}\n            <div className=\"hidden md:flex items-center flex-1 max-w-md mx-8\">\n              <div className=\"relative w-full\">\n                <input\n                  type=\"text\"\n                  placeholder=\"搜索 AI 工具...\"\n                  className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                />\n                <Search className=\"absolute left-3 top-2.5 h-5 w-5 text-gray-400\" />\n              </div>\n            </div>\n\n            {/* Mobile menu button */}\n            <div className=\"md:hidden\">\n              <button\n                onClick={() => setIsMenuOpen(!isMenuOpen)}\n                className=\"text-gray-700 hover:text-blue-600\"\n              >\n                {isMenuOpen ? <X className=\"h-6 w-6\" /> : <Menu className=\"h-6 w-6\" />}\n              </button>\n            </div>\n          </div>\n        </div>\n\n        {/* Mobile Navigation */}\n        {isMenuOpen && (\n          <div className=\"md:hidden bg-white border-t border-gray-200\">\n            <div className=\"px-4 py-2 space-y-1\">\n              <Link\n                href=\"/\"\n                className=\"block px-3 py-2 text-gray-700 hover:text-blue-600 hover:bg-gray-50 rounded-md\"\n              >\n                首页\n              </Link>\n              <Link\n                href=\"/tools\"\n                className=\"block px-3 py-2 text-gray-700 hover:text-blue-600 hover:bg-gray-50 rounded-md\"\n              >\n                工具目录\n              </Link>\n              <Link\n                href=\"/categories\"\n                className=\"block px-3 py-2 text-gray-700 hover:text-blue-600 hover:bg-gray-50 rounded-md\"\n              >\n                分类\n              </Link>\n              <Link\n                href=\"/submit\"\n                className=\"block px-3 py-2 text-gray-700 hover:text-blue-600 hover:bg-gray-50 rounded-md\"\n              >\n                提交工具\n              </Link>\n              <Link\n                href=\"/dashboard\"\n                className=\"block px-3 py-2 text-gray-700 hover:text-blue-600 hover:bg-gray-50 rounded-md\"\n              >\n                仪表板\n              </Link>\n              {/* Admin Links */}\n              <div className=\"border-t border-gray-200 pt-4 mt-4\">\n                <div className=\"px-3 py-2 text-sm font-medium text-gray-500\">管理员</div>\n                <Link\n                  href=\"/admin\"\n                  className=\"block px-3 py-2 text-gray-700 hover:text-blue-600 hover:bg-gray-50 rounded-md\"\n                >\n                  审核中心\n                </Link>\n                <Link\n                  href=\"/admin/dashboard\"\n                  className=\"block px-3 py-2 text-gray-700 hover:text-blue-600 hover:bg-gray-50 rounded-md\"\n                >\n                  统计面板\n                </Link>\n              </div>\n              {/* Mobile Search */}\n              <div className=\"px-3 py-2\">\n                <div className=\"relative\">\n                  <input\n                    type=\"text\"\n                    placeholder=\"搜索 AI 工具...\"\n                    className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                  />\n                  <Search className=\"absolute left-3 top-2.5 h-5 w-5 text-gray-400\" />\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n      </header>\n\n      {/* Main Content */}\n      <main className=\"flex-1\">\n        {children}\n      </main>\n\n      {/* Footer */}\n      <footer className=\"bg-white border-t border-gray-200 mt-16\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n          <div className=\"grid grid-cols-1 md:grid-cols-4 gap-8\">\n            <div className=\"col-span-1 md:col-span-2\">\n              <div className=\"flex items-center space-x-2 mb-4\">\n                <div className=\"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center\">\n                  <span className=\"text-white font-bold text-sm\">AI</span>\n                </div>\n                <span className=\"text-xl font-bold text-gray-900\">AI Tools</span>\n              </div>\n              <p className=\"text-gray-600 mb-4\">\n                发现最新最好的 AI 工具，提升您的工作效率和创造力。\n              </p>\n            </div>\n            \n            <div>\n              <h3 className=\"text-sm font-semibold text-gray-900 tracking-wider uppercase mb-4\">\n                快速链接\n              </h3>\n              <ul className=\"space-y-2\">\n                <li>\n                  <Link href=\"/tools\" className=\"text-gray-600 hover:text-blue-600\">\n                    工具目录\n                  </Link>\n                </li>\n                <li>\n                  <Link href=\"/categories\" className=\"text-gray-600 hover:text-blue-600\">\n                    分类浏览\n                  </Link>\n                </li>\n                <li>\n                  <Link href=\"/submit\" className=\"text-gray-600 hover:text-blue-600\">\n                    提交工具\n                  </Link>\n                </li>\n              </ul>\n            </div>\n            \n            <div>\n              <h3 className=\"text-sm font-semibold text-gray-900 tracking-wider uppercase mb-4\">\n                支持\n              </h3>\n              <ul className=\"space-y-2\">\n                <li>\n                  <a href=\"#\" className=\"text-gray-600 hover:text-blue-600\">\n                    帮助中心\n                  </a>\n                </li>\n                <li>\n                  <a href=\"#\" className=\"text-gray-600 hover:text-blue-600\">\n                    联系我们\n                  </a>\n                </li>\n                <li>\n                  <a href=\"#\" className=\"text-gray-600 hover:text-blue-600\">\n                    隐私政策\n                  </a>\n                </li>\n              </ul>\n            </div>\n          </div>\n          \n          <div className=\"border-t border-gray-200 mt-8 pt-8\">\n            <p className=\"text-center text-gray-600\">\n              © 2024 AI Tools Directory. All rights reserved.\n            </p>\n          </div>\n        </div>\n      </footer>\n    </div>\n  );\n};\n\nexport default Layout;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;;;AAJA;;;;AAUA,MAAM,SAAgC,CAAC,EAAE,QAAQ,EAAE;;IACjD,MAAM,CAAC,YAAY,cAAc,GAAG,6JAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IAEnD,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAO,WAAU;;kCAChB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAI,WAAU;;0DACvB,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAK,WAAU;8DAA+B;;;;;;;;;;;0DAEjD,6LAAC;gDAAK,WAAU;0DAAkC;;;;;;;;;;;;;;;;;8CAKtD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAI,WAAU;sDAAsD;;;;;;sDAG/E,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAS,WAAU;sDAAsD;;;;;;sDAGpF,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAc,WAAU;sDAAsD;;;;;;sDAGzF,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAU,WAAU;sDAAsD;;;;;;sDAGrF,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAa,WAAU;sDAAsD;;;;;;sDAIxF,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAO,WAAU;;wDAAwE;sEAExF,6LAAC;4DAAI,WAAU;4DAAe,MAAK;4DAAO,QAAO;4DAAe,SAAQ;sEACtE,cAAA,6LAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAG,GAAE;;;;;;;;;;;;;;;;;8DAGzE,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,+JAAA,CAAA,UAAI;gEAAC,MAAK;gEAAS,WAAU;0EAA4E;;;;;;0EAG1G,6LAAC,+JAAA,CAAA,UAAI;gEAAC,MAAK;gEAAmB,WAAU;0EAA4E;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAS5H,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,MAAK;gDACL,aAAY;gDACZ,WAAU;;;;;;0DAEZ,6LAAC,yMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;;;;;;;;;;;;8CAKtB,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCACC,SAAS,IAAM,cAAc,CAAC;wCAC9B,WAAU;kDAET,2BAAa,6LAAC,+LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;iEAAe,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAOjE,4BACC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAID,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAA8C;;;;;;sDAC7D,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;sDAGD,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;;;;;;;8CAKH,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,MAAK;gDACL,aAAY;gDACZ,WAAU;;;;;;0DAEZ,6LAAC,yMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS9B,6LAAC;gBAAK,WAAU;0BACb;;;;;;0BAIH,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAK,WAAU;kEAA+B;;;;;;;;;;;8DAEjD,6LAAC;oDAAK,WAAU;8DAAkC;;;;;;;;;;;;sDAEpD,6LAAC;4CAAE,WAAU;sDAAqB;;;;;;;;;;;;8CAKpC,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAoE;;;;;;sDAGlF,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;8DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAS,WAAU;kEAAoC;;;;;;;;;;;8DAIpE,6LAAC;8DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAc,WAAU;kEAAoC;;;;;;;;;;;8DAIzE,6LAAC;8DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAU,WAAU;kEAAoC;;;;;;;;;;;;;;;;;;;;;;;8CAOzE,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAoE;;;;;;sDAGlF,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;8DACC,cAAA,6LAAC;wDAAE,MAAK;wDAAI,WAAU;kEAAoC;;;;;;;;;;;8DAI5D,6LAAC;8DACC,cAAA,6LAAC;wDAAE,MAAK;wDAAI,WAAU;kEAAoC;;;;;;;;;;;8DAI5D,6LAAC;8DACC,cAAA,6LAAC;wDAAE,MAAK;wDAAI,WAAU;kEAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQlE,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAE,WAAU;0CAA4B;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQrD;GAhOM;KAAA;uCAkOS", "debugId": null}}, {"offset": {"line": 666, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/app/admin/dashboard/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport Layout from '@/components/Layout';\nimport { \n  BarChart3,\n  TrendingUp,\n  Users,\n  Clock,\n  CheckCircle,\n  XCircle,\n  Eye,\n  Heart,\n  Calendar,\n  Activity,\n  AlertCircle,\n  Star\n} from 'lucide-react';\n\n// Mock data for admin dashboard\nconst mockStats = {\n  overview: {\n    totalTools: 156,\n    pendingReview: 12,\n    approvedToday: 8,\n    rejectedToday: 2,\n    totalUsers: 2847,\n    activeUsers: 1234,\n    totalViews: 45678,\n    totalLikes: 8901\n  },\n  recentActivity: [\n    {\n      id: '1',\n      type: 'submission',\n      message: '张三 提交了新工具 \"AI写作助手\"',\n      timestamp: '2024-06-26T10:30:00Z',\n      status: 'pending'\n    },\n    {\n      id: '2',\n      type: 'approval',\n      message: '管理员批准了工具 \"CodeAI\"',\n      timestamp: '2024-06-26T09:15:00Z',\n      status: 'approved'\n    },\n    {\n      id: '3',\n      type: 'rejection',\n      message: '管理员拒绝了工具 \"FakeAI\" - 功能描述不准确',\n      timestamp: '2024-06-26T08:45:00Z',\n      status: 'rejected'\n    },\n    {\n      id: '4',\n      type: 'submission',\n      message: '李四 提交了新工具 \"ImageGen Pro\"',\n      timestamp: '2024-06-25T16:20:00Z',\n      status: 'pending'\n    },\n    {\n      id: '5',\n      type: 'approval',\n      message: '管理员批准了工具 \"DataAnalyzer\"',\n      timestamp: '2024-06-25T14:10:00Z',\n      status: 'approved'\n    }\n  ],\n  topTools: [\n    {\n      id: '1',\n      name: 'ChatGPT',\n      category: 'text-generation',\n      views: 12456,\n      likes: 2341,\n      status: 'approved'\n    },\n    {\n      id: '2',\n      name: 'Midjourney',\n      category: 'image-generation',\n      views: 9876,\n      likes: 1987,\n      status: 'approved'\n    },\n    {\n      id: '3',\n      name: 'GitHub Copilot',\n      category: 'code-generation',\n      views: 8765,\n      likes: 1654,\n      status: 'approved'\n    },\n    {\n      id: '4',\n      name: 'Notion AI',\n      category: 'productivity',\n      views: 7654,\n      likes: 1432,\n      status: 'approved'\n    },\n    {\n      id: '5',\n      name: 'Grammarly',\n      category: 'text-generation',\n      views: 6543,\n      likes: 1234,\n      status: 'approved'\n    }\n  ],\n  weeklyStats: [\n    { day: '周一', submissions: 5, approvals: 3, rejections: 1 },\n    { day: '周二', submissions: 8, approvals: 6, rejections: 2 },\n    { day: '周三', submissions: 6, approvals: 4, rejections: 1 },\n    { day: '周四', submissions: 9, approvals: 7, rejections: 2 },\n    { day: '周五', submissions: 7, approvals: 5, rejections: 1 },\n    { day: '周六', submissions: 3, approvals: 2, rejections: 0 },\n    { day: '周日', submissions: 4, approvals: 3, rejections: 1 }\n  ]\n};\n\nconst categoryLabels: Record<string, string> = {\n  'text-generation': '文本生成',\n  'image-generation': '图像生成',\n  'code-generation': '代码生成',\n  'data-analysis': '数据分析',\n  'audio-processing': '音频处理',\n  'video-editing': '视频编辑',\n  'translation': '语言翻译',\n  'search-engines': '搜索引擎',\n  'education': '教育学习',\n  'marketing': '营销工具',\n  'productivity': '生产力工具',\n  'customer-service': '客户服务'\n};\n\nexport default function AdminDashboardPage() {\n  const [timeRange, setTimeRange] = useState('7d');\n\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString('zh-CN', {\n      month: 'short',\n      day: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n\n  const getActivityIcon = (type: string) => {\n    switch (type) {\n      case 'submission':\n        return <Clock className=\"w-4 h-4 text-blue-600\" />;\n      case 'approval':\n        return <CheckCircle className=\"w-4 h-4 text-green-600\" />;\n      case 'rejection':\n        return <XCircle className=\"w-4 h-4 text-red-600\" />;\n      default:\n        return <Activity className=\"w-4 h-4 text-gray-600\" />;\n    }\n  };\n\n  const getActivityBgColor = (type: string) => {\n    switch (type) {\n      case 'submission':\n        return 'bg-blue-50 border-blue-200';\n      case 'approval':\n        return 'bg-green-50 border-green-200';\n      case 'rejection':\n        return 'bg-red-50 border-red-200';\n      default:\n        return 'bg-gray-50 border-gray-200';\n    }\n  };\n\n  return (\n    <Layout>\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {/* Header */}\n        <div className=\"mb-8\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <h1 className=\"text-3xl font-bold text-gray-900 flex items-center\">\n                <BarChart3 className=\"mr-3 h-8 w-8 text-blue-600\" />\n                管理员统计面板\n              </h1>\n              <p className=\"mt-2 text-lg text-gray-600\">\n                查看网站运营数据和审核统计\n              </p>\n            </div>\n            \n            <div className=\"flex items-center space-x-3\">\n              <select\n                value={timeRange}\n                onChange={(e) => setTimeRange(e.target.value)}\n                className=\"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              >\n                <option value=\"1d\">今天</option>\n                <option value=\"7d\">最近7天</option>\n                <option value=\"30d\">最近30天</option>\n                <option value=\"90d\">最近90天</option>\n              </select>\n            </div>\n          </div>\n        </div>\n\n        {/* Overview Stats */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\">\n          <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-sm font-medium text-gray-600\">总工具数</p>\n                <p className=\"text-2xl font-bold text-gray-900\">{mockStats.overview.totalTools}</p>\n              </div>\n              <div className=\"p-3 bg-blue-100 rounded-lg\">\n                <BarChart3 className=\"w-6 h-6 text-blue-600\" />\n              </div>\n            </div>\n            <div className=\"mt-4 flex items-center text-sm\">\n              <TrendingUp className=\"w-4 h-4 text-green-500 mr-1\" />\n              <span className=\"text-green-600\">+12%</span>\n              <span className=\"text-gray-500 ml-1\">vs 上周</span>\n            </div>\n          </div>\n\n          <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-sm font-medium text-gray-600\">待审核</p>\n                <p className=\"text-2xl font-bold text-yellow-600\">{mockStats.overview.pendingReview}</p>\n              </div>\n              <div className=\"p-3 bg-yellow-100 rounded-lg\">\n                <Clock className=\"w-6 h-6 text-yellow-600\" />\n              </div>\n            </div>\n            <div className=\"mt-4 flex items-center text-sm\">\n              <AlertCircle className=\"w-4 h-4 text-yellow-500 mr-1\" />\n              <span className=\"text-yellow-600\">需要关注</span>\n            </div>\n          </div>\n\n          <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-sm font-medium text-gray-600\">活跃用户</p>\n                <p className=\"text-2xl font-bold text-gray-900\">{mockStats.overview.activeUsers.toLocaleString()}</p>\n              </div>\n              <div className=\"p-3 bg-green-100 rounded-lg\">\n                <Users className=\"w-6 h-6 text-green-600\" />\n              </div>\n            </div>\n            <div className=\"mt-4 flex items-center text-sm\">\n              <TrendingUp className=\"w-4 h-4 text-green-500 mr-1\" />\n              <span className=\"text-green-600\">+8%</span>\n              <span className=\"text-gray-500 ml-1\">vs 上周</span>\n            </div>\n          </div>\n\n          <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-sm font-medium text-gray-600\">总浏览量</p>\n                <p className=\"text-2xl font-bold text-gray-900\">{mockStats.overview.totalViews.toLocaleString()}</p>\n              </div>\n              <div className=\"p-3 bg-purple-100 rounded-lg\">\n                <Eye className=\"w-6 h-6 text-purple-600\" />\n              </div>\n            </div>\n            <div className=\"mt-4 flex items-center text-sm\">\n              <TrendingUp className=\"w-4 h-4 text-green-500 mr-1\" />\n              <span className=\"text-green-600\">+15%</span>\n              <span className=\"text-gray-500 ml-1\">vs 上周</span>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8\">\n          {/* Weekly Stats Chart */}\n          <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n            <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">本周审核统计</h3>\n            <div className=\"space-y-4\">\n              {mockStats.weeklyStats.map((stat, index) => (\n                <div key={index} className=\"flex items-center justify-between\">\n                  <span className=\"text-sm font-medium text-gray-600 w-12\">{stat.day}</span>\n                  <div className=\"flex-1 mx-4\">\n                    <div className=\"flex space-x-1\">\n                      <div \n                        className=\"bg-blue-200 h-6 rounded flex items-center justify-center text-xs text-blue-800\"\n                        style={{ width: `${(stat.submissions / 10) * 100}%`, minWidth: '20px' }}\n                      >\n                        {stat.submissions}\n                      </div>\n                      <div \n                        className=\"bg-green-200 h-6 rounded flex items-center justify-center text-xs text-green-800\"\n                        style={{ width: `${(stat.approvals / 10) * 100}%`, minWidth: '20px' }}\n                      >\n                        {stat.approvals}\n                      </div>\n                      <div \n                        className=\"bg-red-200 h-6 rounded flex items-center justify-center text-xs text-red-800\"\n                        style={{ width: `${(stat.rejections / 10) * 100}%`, minWidth: '20px' }}\n                      >\n                        {stat.rejections}\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              ))}\n            </div>\n            <div className=\"flex items-center justify-center space-x-6 mt-6 text-sm\">\n              <div className=\"flex items-center\">\n                <div className=\"w-3 h-3 bg-blue-200 rounded mr-2\"></div>\n                <span className=\"text-gray-600\">提交</span>\n              </div>\n              <div className=\"flex items-center\">\n                <div className=\"w-3 h-3 bg-green-200 rounded mr-2\"></div>\n                <span className=\"text-gray-600\">批准</span>\n              </div>\n              <div className=\"flex items-center\">\n                <div className=\"w-3 h-3 bg-red-200 rounded mr-2\"></div>\n                <span className=\"text-gray-600\">拒绝</span>\n              </div>\n            </div>\n          </div>\n\n          {/* Top Tools */}\n          <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n            <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">热门工具</h3>\n            <div className=\"space-y-4\">\n              {mockStats.topTools.map((tool, index) => (\n                <div key={tool.id} className=\"flex items-center justify-between p-3 bg-gray-50 rounded-lg\">\n                  <div className=\"flex items-center space-x-3\">\n                    <div className=\"flex items-center justify-center w-8 h-8 bg-blue-100 text-blue-600 rounded-full text-sm font-bold\">\n                      {index + 1}\n                    </div>\n                    <div>\n                      <div className=\"font-medium text-gray-900\">{tool.name}</div>\n                      <div className=\"text-sm text-gray-500\">{categoryLabels[tool.category]}</div>\n                    </div>\n                  </div>\n                  <div className=\"flex items-center space-x-4 text-sm text-gray-600\">\n                    <div className=\"flex items-center\">\n                      <Eye className=\"w-4 h-4 mr-1\" />\n                      {tool.views.toLocaleString()}\n                    </div>\n                    <div className=\"flex items-center\">\n                      <Heart className=\"w-4 h-4 mr-1\" />\n                      {tool.likes.toLocaleString()}\n                    </div>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n\n        {/* Recent Activity */}\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n          <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">最近活动</h3>\n          <div className=\"space-y-4\">\n            {mockStats.recentActivity.map((activity) => (\n              <div \n                key={activity.id} \n                className={`flex items-start space-x-3 p-4 rounded-lg border ${getActivityBgColor(activity.type)}`}\n              >\n                <div className=\"flex-shrink-0 mt-0.5\">\n                  {getActivityIcon(activity.type)}\n                </div>\n                <div className=\"flex-1 min-w-0\">\n                  <p className=\"text-sm text-gray-900\">{activity.message}</p>\n                  <p className=\"text-xs text-gray-500 mt-1\">{formatDate(activity.timestamp)}</p>\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n      </div>\n    </Layout>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAJA;;;;AAmBA,gCAAgC;AAChC,MAAM,YAAY;IAChB,UAAU;QACR,YAAY;QACZ,eAAe;QACf,eAAe;QACf,eAAe;QACf,YAAY;QACZ,aAAa;QACb,YAAY;QACZ,YAAY;IACd;IACA,gBAAgB;QACd;YACE,IAAI;YACJ,MAAM;YACN,SAAS;YACT,WAAW;YACX,QAAQ;QACV;QACA;YACE,IAAI;YACJ,MAAM;YACN,SAAS;YACT,WAAW;YACX,QAAQ;QACV;QACA;YACE,IAAI;YACJ,MAAM;YACN,SAAS;YACT,WAAW;YACX,QAAQ;QACV;QACA;YACE,IAAI;YACJ,MAAM;YACN,SAAS;YACT,WAAW;YACX,QAAQ;QACV;QACA;YACE,IAAI;YACJ,MAAM;YACN,SAAS;YACT,WAAW;YACX,QAAQ;QACV;KACD;IACD,UAAU;QACR;YACE,IAAI;YACJ,MAAM;YACN,UAAU;YACV,OAAO;YACP,OAAO;YACP,QAAQ;QACV;QACA;YACE,IAAI;YACJ,MAAM;YACN,UAAU;YACV,OAAO;YACP,OAAO;YACP,QAAQ;QACV;QACA;YACE,IAAI;YACJ,MAAM;YACN,UAAU;YACV,OAAO;YACP,OAAO;YACP,QAAQ;QACV;QACA;YACE,IAAI;YACJ,MAAM;YACN,UAAU;YACV,OAAO;YACP,OAAO;YACP,QAAQ;QACV;QACA;YACE,IAAI;YACJ,MAAM;YACN,UAAU;YACV,OAAO;YACP,OAAO;YACP,QAAQ;QACV;KACD;IACD,aAAa;QACX;YAAE,KAAK;YAAM,aAAa;YAAG,WAAW;YAAG,YAAY;QAAE;QACzD;YAAE,KAAK;YAAM,aAAa;YAAG,WAAW;YAAG,YAAY;QAAE;QACzD;YAAE,KAAK;YAAM,aAAa;YAAG,WAAW;YAAG,YAAY;QAAE;QACzD;YAAE,KAAK;YAAM,aAAa;YAAG,WAAW;YAAG,YAAY;QAAE;QACzD;YAAE,KAAK;YAAM,aAAa;YAAG,WAAW;YAAG,YAAY;QAAE;QACzD;YAAE,KAAK;YAAM,aAAa;YAAG,WAAW;YAAG,YAAY;QAAE;QACzD;YAAE,KAAK;YAAM,aAAa;YAAG,WAAW;YAAG,YAAY;QAAE;KAC1D;AACH;AAEA,MAAM,iBAAyC;IAC7C,mBAAmB;IACnB,oBAAoB;IACpB,mBAAmB;IACnB,iBAAiB;IACjB,oBAAoB;IACpB,iBAAiB;IACjB,eAAe;IACf,kBAAkB;IAClB,aAAa;IACb,aAAa;IACb,gBAAgB;IAChB,oBAAoB;AACtB;AAEe,SAAS;;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;YACtD,OAAO;YACP,KAAK;YACL,MAAM;YACN,QAAQ;QACV;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,OAAQ;YACN,KAAK;gBACH,qBAAO,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B,KAAK;gBACH,qBAAO,6LAAC,8NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;gBACH,qBAAO,6LAAC,+MAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;YAC5B;gBACE,qBAAO,6LAAC,6MAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;QAC/B;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,6LAAC,+HAAA,CAAA,UAAM;kBACL,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC,qNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;4CAA+B;;;;;;;kDAGtD,6LAAC;wCAAE,WAAU;kDAA6B;;;;;;;;;;;;0CAK5C,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCACC,OAAO;oCACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;oCAC5C,WAAU;;sDAEV,6LAAC;4CAAO,OAAM;sDAAK;;;;;;sDACnB,6LAAC;4CAAO,OAAM;sDAAK;;;;;;sDACnB,6LAAC;4CAAO,OAAM;sDAAM;;;;;;sDACpB,6LAAC;4CAAO,OAAM;sDAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAO5B,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,6LAAC;oDAAE,WAAU;8DAAoC,UAAU,QAAQ,CAAC,UAAU;;;;;;;;;;;;sDAEhF,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,qNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;;;;;;;;;;;;8CAGzB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;sDACtB,6LAAC;4CAAK,WAAU;sDAAiB;;;;;;sDACjC,6LAAC;4CAAK,WAAU;sDAAqB;;;;;;;;;;;;;;;;;;sCAIzC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,6LAAC;oDAAE,WAAU;8DAAsC,UAAU,QAAQ,CAAC,aAAa;;;;;;;;;;;;sDAErF,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;;;;;;;8CAGrB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,uNAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;sDACvB,6LAAC;4CAAK,WAAU;sDAAkB;;;;;;;;;;;;;;;;;;sCAItC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,6LAAC;oDAAE,WAAU;8DAAoC,UAAU,QAAQ,CAAC,WAAW,CAAC,cAAc;;;;;;;;;;;;sDAEhG,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;;;;;;;8CAGrB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;sDACtB,6LAAC;4CAAK,WAAU;sDAAiB;;;;;;sDACjC,6LAAC;4CAAK,WAAU;sDAAqB;;;;;;;;;;;;;;;;;;sCAIzC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,6LAAC;oDAAE,WAAU;8DAAoC,UAAU,QAAQ,CAAC,UAAU,CAAC,cAAc;;;;;;;;;;;;sDAE/F,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,mMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;;;;;;;;;;;;8CAGnB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;sDACtB,6LAAC;4CAAK,WAAU;sDAAiB;;;;;;sDACjC,6LAAC;4CAAK,WAAU;sDAAqB;;;;;;;;;;;;;;;;;;;;;;;;8BAK3C,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAA2C;;;;;;8CACzD,6LAAC;oCAAI,WAAU;8CACZ,UAAU,WAAW,CAAC,GAAG,CAAC,CAAC,MAAM,sBAChC,6LAAC;4CAAgB,WAAU;;8DACzB,6LAAC;oDAAK,WAAU;8DAA0C,KAAK,GAAG;;;;;;8DAClE,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEACC,WAAU;gEACV,OAAO;oEAAE,OAAO,GAAG,AAAC,KAAK,WAAW,GAAG,KAAM,IAAI,CAAC,CAAC;oEAAE,UAAU;gEAAO;0EAErE,KAAK,WAAW;;;;;;0EAEnB,6LAAC;gEACC,WAAU;gEACV,OAAO;oEAAE,OAAO,GAAG,AAAC,KAAK,SAAS,GAAG,KAAM,IAAI,CAAC,CAAC;oEAAE,UAAU;gEAAO;0EAEnE,KAAK,SAAS;;;;;;0EAEjB,6LAAC;gEACC,WAAU;gEACV,OAAO;oEAAE,OAAO,GAAG,AAAC,KAAK,UAAU,GAAG,KAAM,IAAI,CAAC,CAAC;oEAAE,UAAU;gEAAO;0EAEpE,KAAK,UAAU;;;;;;;;;;;;;;;;;;2CApBd;;;;;;;;;;8CA2Bd,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;;;;;8DACf,6LAAC;oDAAK,WAAU;8DAAgB;;;;;;;;;;;;sDAElC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;;;;;8DACf,6LAAC;oDAAK,WAAU;8DAAgB;;;;;;;;;;;;sDAElC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;;;;;8DACf,6LAAC;oDAAK,WAAU;8DAAgB;;;;;;;;;;;;;;;;;;;;;;;;sCAMtC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAA2C;;;;;;8CACzD,6LAAC;oCAAI,WAAU;8CACZ,UAAU,QAAQ,CAAC,GAAG,CAAC,CAAC,MAAM,sBAC7B,6LAAC;4CAAkB,WAAU;;8DAC3B,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACZ,QAAQ;;;;;;sEAEX,6LAAC;;8EACC,6LAAC;oEAAI,WAAU;8EAA6B,KAAK,IAAI;;;;;;8EACrD,6LAAC;oEAAI,WAAU;8EAAyB,cAAc,CAAC,KAAK,QAAQ,CAAC;;;;;;;;;;;;;;;;;;8DAGzE,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,mMAAA,CAAA,MAAG;oEAAC,WAAU;;;;;;gEACd,KAAK,KAAK,CAAC,cAAc;;;;;;;sEAE5B,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,uMAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;gEAChB,KAAK,KAAK,CAAC,cAAc;;;;;;;;;;;;;;2CAjBtB,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;8BA2BzB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAA2C;;;;;;sCACzD,6LAAC;4BAAI,WAAU;sCACZ,UAAU,cAAc,CAAC,GAAG,CAAC,CAAC,yBAC7B,6LAAC;oCAEC,WAAW,CAAC,iDAAiD,EAAE,mBAAmB,SAAS,IAAI,GAAG;;sDAElG,6LAAC;4CAAI,WAAU;sDACZ,gBAAgB,SAAS,IAAI;;;;;;sDAEhC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;8DAAyB,SAAS,OAAO;;;;;;8DACtD,6LAAC;oDAAE,WAAU;8DAA8B,WAAW,SAAS,SAAS;;;;;;;;;;;;;mCARrE,SAAS,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiBhC;GAlPwB;KAAA", "debugId": null}}]}
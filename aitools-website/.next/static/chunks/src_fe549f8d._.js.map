{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/Layout.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Link from 'next/link';\nimport { Search, Menu, X } from 'lucide-react';\n\ninterface LayoutProps {\n  children: React.ReactNode;\n}\n\nconst Layout: React.FC<LayoutProps> = ({ children }) => {\n  const [isMenuOpen, setIsMenuOpen] = React.useState(false);\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header */}\n      <header className=\"bg-white shadow-sm border-b border-gray-200\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center h-16\">\n            {/* Logo */}\n            <div className=\"flex items-center\">\n              <Link href=\"/\" className=\"flex items-center space-x-2\">\n                <div className=\"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center\">\n                  <span className=\"text-white font-bold text-sm\">AI</span>\n                </div>\n                <span className=\"text-xl font-bold text-gray-900\">AI Tools</span>\n              </Link>\n            </div>\n\n            {/* Desktop Navigation */}\n            <nav className=\"hidden md:flex items-center space-x-8\">\n              <Link href=\"/\" className=\"text-gray-700 hover:text-blue-600 transition-colors\">\n                首页\n              </Link>\n              <Link href=\"/tools\" className=\"text-gray-700 hover:text-blue-600 transition-colors\">\n                工具目录\n              </Link>\n              <Link href=\"/categories\" className=\"text-gray-700 hover:text-blue-600 transition-colors\">\n                分类\n              </Link>\n              <Link href=\"/submit\" className=\"text-gray-700 hover:text-blue-600 transition-colors\">\n                提交工具\n              </Link>\n              <Link href=\"/dashboard\" className=\"text-gray-700 hover:text-blue-600 transition-colors\">\n                仪表板\n              </Link>\n              {/* Admin Navigation */}\n              <div className=\"relative group\">\n                <button className=\"text-gray-700 hover:text-blue-600 transition-colors flex items-center\">\n                  管理员\n                  <svg className=\"w-4 h-4 ml-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 9l-7 7-7-7\" />\n                  </svg>\n                </button>\n                <div className=\"absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg border border-gray-200 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50\">\n                  <div className=\"py-1\">\n                    <Link href=\"/admin\" className=\"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors\">\n                      审核中心\n                    </Link>\n                    <Link href=\"/admin/dashboard\" className=\"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors\">\n                      统计面板\n                    </Link>\n                  </div>\n                </div>\n              </div>\n            </nav>\n\n            {/* Search Bar */}\n            <div className=\"hidden md:flex items-center flex-1 max-w-md mx-8\">\n              <div className=\"relative w-full\">\n                <input\n                  type=\"text\"\n                  placeholder=\"搜索 AI 工具...\"\n                  className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                />\n                <Search className=\"absolute left-3 top-2.5 h-5 w-5 text-gray-400\" />\n              </div>\n            </div>\n\n            {/* Mobile menu button */}\n            <div className=\"md:hidden\">\n              <button\n                onClick={() => setIsMenuOpen(!isMenuOpen)}\n                className=\"text-gray-700 hover:text-blue-600\"\n              >\n                {isMenuOpen ? <X className=\"h-6 w-6\" /> : <Menu className=\"h-6 w-6\" />}\n              </button>\n            </div>\n          </div>\n        </div>\n\n        {/* Mobile Navigation */}\n        {isMenuOpen && (\n          <div className=\"md:hidden bg-white border-t border-gray-200\">\n            <div className=\"px-4 py-2 space-y-1\">\n              <Link\n                href=\"/\"\n                className=\"block px-3 py-2 text-gray-700 hover:text-blue-600 hover:bg-gray-50 rounded-md\"\n              >\n                首页\n              </Link>\n              <Link\n                href=\"/tools\"\n                className=\"block px-3 py-2 text-gray-700 hover:text-blue-600 hover:bg-gray-50 rounded-md\"\n              >\n                工具目录\n              </Link>\n              <Link\n                href=\"/categories\"\n                className=\"block px-3 py-2 text-gray-700 hover:text-blue-600 hover:bg-gray-50 rounded-md\"\n              >\n                分类\n              </Link>\n              <Link\n                href=\"/submit\"\n                className=\"block px-3 py-2 text-gray-700 hover:text-blue-600 hover:bg-gray-50 rounded-md\"\n              >\n                提交工具\n              </Link>\n              <Link\n                href=\"/dashboard\"\n                className=\"block px-3 py-2 text-gray-700 hover:text-blue-600 hover:bg-gray-50 rounded-md\"\n              >\n                仪表板\n              </Link>\n              {/* Admin Links */}\n              <div className=\"border-t border-gray-200 pt-4 mt-4\">\n                <div className=\"px-3 py-2 text-sm font-medium text-gray-500\">管理员</div>\n                <Link\n                  href=\"/admin\"\n                  className=\"block px-3 py-2 text-gray-700 hover:text-blue-600 hover:bg-gray-50 rounded-md\"\n                >\n                  审核中心\n                </Link>\n                <Link\n                  href=\"/admin/dashboard\"\n                  className=\"block px-3 py-2 text-gray-700 hover:text-blue-600 hover:bg-gray-50 rounded-md\"\n                >\n                  统计面板\n                </Link>\n              </div>\n              {/* Mobile Search */}\n              <div className=\"px-3 py-2\">\n                <div className=\"relative\">\n                  <input\n                    type=\"text\"\n                    placeholder=\"搜索 AI 工具...\"\n                    className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                  />\n                  <Search className=\"absolute left-3 top-2.5 h-5 w-5 text-gray-400\" />\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n      </header>\n\n      {/* Main Content */}\n      <main className=\"flex-1\">\n        {children}\n      </main>\n\n      {/* Footer */}\n      <footer className=\"bg-white border-t border-gray-200 mt-16\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n          <div className=\"grid grid-cols-1 md:grid-cols-4 gap-8\">\n            <div className=\"col-span-1 md:col-span-2\">\n              <div className=\"flex items-center space-x-2 mb-4\">\n                <div className=\"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center\">\n                  <span className=\"text-white font-bold text-sm\">AI</span>\n                </div>\n                <span className=\"text-xl font-bold text-gray-900\">AI Tools</span>\n              </div>\n              <p className=\"text-gray-600 mb-4\">\n                发现最新最好的 AI 工具，提升您的工作效率和创造力。\n              </p>\n            </div>\n            \n            <div>\n              <h3 className=\"text-sm font-semibold text-gray-900 tracking-wider uppercase mb-4\">\n                快速链接\n              </h3>\n              <ul className=\"space-y-2\">\n                <li>\n                  <Link href=\"/tools\" className=\"text-gray-600 hover:text-blue-600\">\n                    工具目录\n                  </Link>\n                </li>\n                <li>\n                  <Link href=\"/categories\" className=\"text-gray-600 hover:text-blue-600\">\n                    分类浏览\n                  </Link>\n                </li>\n                <li>\n                  <Link href=\"/submit\" className=\"text-gray-600 hover:text-blue-600\">\n                    提交工具\n                  </Link>\n                </li>\n              </ul>\n            </div>\n            \n            <div>\n              <h3 className=\"text-sm font-semibold text-gray-900 tracking-wider uppercase mb-4\">\n                支持\n              </h3>\n              <ul className=\"space-y-2\">\n                <li>\n                  <a href=\"#\" className=\"text-gray-600 hover:text-blue-600\">\n                    帮助中心\n                  </a>\n                </li>\n                <li>\n                  <a href=\"#\" className=\"text-gray-600 hover:text-blue-600\">\n                    联系我们\n                  </a>\n                </li>\n                <li>\n                  <a href=\"#\" className=\"text-gray-600 hover:text-blue-600\">\n                    隐私政策\n                  </a>\n                </li>\n              </ul>\n            </div>\n          </div>\n          \n          <div className=\"border-t border-gray-200 mt-8 pt-8\">\n            <p className=\"text-center text-gray-600\">\n              © 2024 AI Tools Directory. All rights reserved.\n            </p>\n          </div>\n        </div>\n      </footer>\n    </div>\n  );\n};\n\nexport default Layout;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;;;AAJA;;;;AAUA,MAAM,SAAgC,CAAC,EAAE,QAAQ,EAAE;;IACjD,MAAM,CAAC,YAAY,cAAc,GAAG,6JAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IAEnD,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAO,WAAU;;kCAChB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAI,WAAU;;0DACvB,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAK,WAAU;8DAA+B;;;;;;;;;;;0DAEjD,6LAAC;gDAAK,WAAU;0DAAkC;;;;;;;;;;;;;;;;;8CAKtD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAI,WAAU;sDAAsD;;;;;;sDAG/E,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAS,WAAU;sDAAsD;;;;;;sDAGpF,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAc,WAAU;sDAAsD;;;;;;sDAGzF,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAU,WAAU;sDAAsD;;;;;;sDAGrF,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAa,WAAU;sDAAsD;;;;;;sDAIxF,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAO,WAAU;;wDAAwE;sEAExF,6LAAC;4DAAI,WAAU;4DAAe,MAAK;4DAAO,QAAO;4DAAe,SAAQ;sEACtE,cAAA,6LAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAG,GAAE;;;;;;;;;;;;;;;;;8DAGzE,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,+JAAA,CAAA,UAAI;gEAAC,MAAK;gEAAS,WAAU;0EAA4E;;;;;;0EAG1G,6LAAC,+JAAA,CAAA,UAAI;gEAAC,MAAK;gEAAmB,WAAU;0EAA4E;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAS5H,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,MAAK;gDACL,aAAY;gDACZ,WAAU;;;;;;0DAEZ,6LAAC,yMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;;;;;;;;;;;;8CAKtB,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCACC,SAAS,IAAM,cAAc,CAAC;wCAC9B,WAAU;kDAET,2BAAa,6LAAC,+LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;iEAAe,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAOjE,4BACC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAID,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAA8C;;;;;;sDAC7D,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;sDAGD,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;;;;;;;8CAKH,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,MAAK;gDACL,aAAY;gDACZ,WAAU;;;;;;0DAEZ,6LAAC,yMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS9B,6LAAC;gBAAK,WAAU;0BACb;;;;;;0BAIH,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAK,WAAU;kEAA+B;;;;;;;;;;;8DAEjD,6LAAC;oDAAK,WAAU;8DAAkC;;;;;;;;;;;;sDAEpD,6LAAC;4CAAE,WAAU;sDAAqB;;;;;;;;;;;;8CAKpC,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAoE;;;;;;sDAGlF,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;8DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAS,WAAU;kEAAoC;;;;;;;;;;;8DAIpE,6LAAC;8DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAc,WAAU;kEAAoC;;;;;;;;;;;8DAIzE,6LAAC;8DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAU,WAAU;kEAAoC;;;;;;;;;;;;;;;;;;;;;;;8CAOzE,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAoE;;;;;;sDAGlF,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;8DACC,cAAA,6LAAC;wDAAE,MAAK;wDAAI,WAAU;kEAAoC;;;;;;;;;;;8DAI5D,6LAAC;8DACC,cAAA,6LAAC;wDAAE,MAAK;wDAAI,WAAU;kEAAoC;;;;;;;;;;;8DAI5D,6LAAC;8DACC,cAAA,6LAAC;wDAAE,MAAK;wDAAI,WAAU;kEAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQlE,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAE,WAAU;0CAA4B;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQrD;GAhOM;KAAA;uCAkOS", "debugId": null}}, {"offset": {"line": 666, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/app/admin/tools/%5Bid%5D/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { usePara<PERSON>, useRouter } from 'next/navigation';\nimport Layout from '@/components/Layout';\nimport { \n  ArrowLeft,\n  ExternalLink,\n  Calendar,\n  User,\n  Mail,\n  Tag,\n  Star,\n  CheckCircle,\n  XCircle,\n  Clock,\n  AlertTriangle,\n  Globe,\n  DollarSign\n} from 'lucide-react';\n\n// Mock data - in real app this would come from API\nconst mockTool = {\n  id: '1',\n  name: 'AI写作助手',\n  description: '基于GPT技术的智能写作工具，支持多种文体创作，提供实时语法检查和内容优化建议。这是一个功能强大的AI写作助手，能够帮助用户快速生成高质量的文章、报告、邮件等各种文本内容。工具采用最新的自然语言处理技术，具有强大的语言理解和生成能力。',\n  website: 'https://aiwriter.example.com',\n  logo: 'https://via.placeholder.com/128',\n  category: 'text-generation',\n  pricing: 'freemium',\n  tags: ['写作', 'GPT', '内容创作', '语法检查', '文本生成'],\n  features: [\n    '智能写作：基于AI的内容生成',\n    '语法检查：实时检测和修正语法错误',\n    '多语言支持：支持中文、英文等多种语言',\n    '模板库：提供丰富的写作模板',\n    '协作功能：支持团队协作编辑',\n    '导出功能：支持多种格式导出'\n  ],\n  submitterName: '张三',\n  submitterEmail: '<EMAIL>',\n  submittedAt: '2024-06-25T10:30:00Z',\n  status: 'pending',\n  publishDate: '2024-06-28',\n  screenshots: [\n    'https://via.placeholder.com/600x400',\n    'https://via.placeholder.com/600x400',\n    'https://via.placeholder.com/600x400'\n  ]\n};\n\nconst categoryLabels: Record<string, string> = {\n  'text-generation': '文本生成',\n  'image-generation': '图像生成',\n  'code-generation': '代码生成',\n  'data-analysis': '数据分析',\n  'audio-processing': '音频处理',\n  'video-editing': '视频编辑',\n  'translation': '语言翻译',\n  'search-engines': '搜索引擎',\n  'education': '教育学习',\n  'marketing': '营销工具',\n  'productivity': '生产力工具',\n  'customer-service': '客户服务'\n};\n\nconst pricingLabels: Record<string, string> = {\n  'free': '免费',\n  'freemium': '免费增值',\n  'paid': '付费'\n};\n\nexport default function AdminToolDetailPage() {\n  const params = useParams();\n  const router = useRouter();\n  const [tool] = useState(mockTool);\n  const [showRejectModal, setShowRejectModal] = useState(false);\n  const [rejectReason, setRejectReason] = useState('');\n  const [isProcessing, setIsProcessing] = useState(false);\n\n  const handleApprove = async () => {\n    setIsProcessing(true);\n    try {\n      // 模拟API调用\n      await new Promise(resolve => setTimeout(resolve, 1000));\n      alert('工具已批准！');\n      router.push('/admin');\n    } catch (error) {\n      alert('操作失败，请重试');\n    } finally {\n      setIsProcessing(false);\n    }\n  };\n\n  const handleReject = async () => {\n    if (!rejectReason.trim()) return;\n    \n    setIsProcessing(true);\n    try {\n      // 模拟API调用\n      await new Promise(resolve => setTimeout(resolve, 1000));\n      alert('工具已拒绝！');\n      router.push('/admin');\n    } catch (error) {\n      alert('操作失败，请重试');\n    } finally {\n      setIsProcessing(false);\n      setShowRejectModal(false);\n      setRejectReason('');\n    }\n  };\n\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString('zh-CN', {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n\n  const getStatusBadge = (status: string) => {\n    switch (status) {\n      case 'pending':\n        return (\n          <span className=\"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-yellow-100 text-yellow-800\">\n            <Clock className=\"w-4 h-4 mr-2\" />\n            待审核\n          </span>\n        );\n      case 'approved':\n        return (\n          <span className=\"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800\">\n            <CheckCircle className=\"w-4 h-4 mr-2\" />\n            已批准\n          </span>\n        );\n      case 'rejected':\n        return (\n          <span className=\"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-red-100 text-red-800\">\n            <XCircle className=\"w-4 h-4 mr-2\" />\n            已拒绝\n          </span>\n        );\n      default:\n        return null;\n    }\n  };\n\n  return (\n    <Layout>\n      <div className=\"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {/* Header */}\n        <div className=\"mb-8\">\n          <button\n            onClick={() => router.back()}\n            className=\"flex items-center text-gray-600 hover:text-gray-900 mb-4 transition-colors\"\n          >\n            <ArrowLeft className=\"w-4 h-4 mr-2\" />\n            返回审核列表\n          </button>\n          \n          <div className=\"flex items-start justify-between\">\n            <div className=\"flex items-start space-x-6\">\n              <img\n                src={tool.logo}\n                alt={tool.name}\n                className=\"w-24 h-24 rounded-xl object-cover border border-gray-200 shadow-sm\"\n              />\n              <div>\n                <div className=\"flex items-center space-x-3 mb-2\">\n                  <h1 className=\"text-3xl font-bold text-gray-900\">{tool.name}</h1>\n                  {getStatusBadge(tool.status)}\n                </div>\n                <div className=\"flex items-center space-x-4 text-sm text-gray-600 mb-4\">\n                  <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800\">\n                    {categoryLabels[tool.category]}\n                  </span>\n                  <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800\">\n                    <DollarSign className=\"w-3 h-3 mr-1\" />\n                    {pricingLabels[tool.pricing]}\n                  </span>\n                  <a\n                    href={tool.website}\n                    target=\"_blank\"\n                    rel=\"noopener noreferrer\"\n                    className=\"inline-flex items-center text-blue-600 hover:text-blue-800 transition-colors\"\n                  >\n                    <Globe className=\"w-4 h-4 mr-1\" />\n                    访问网站\n                    <ExternalLink className=\"w-3 h-3 ml-1\" />\n                  </a>\n                </div>\n                <p className=\"text-gray-600 max-w-3xl\">{tool.description}</p>\n              </div>\n            </div>\n\n            {/* Action Buttons */}\n            {tool.status === 'pending' && (\n              <div className=\"flex space-x-3\">\n                <button\n                  onClick={handleApprove}\n                  disabled={isProcessing}\n                  className=\"px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors flex items-center\"\n                >\n                  <CheckCircle className=\"w-4 h-4 mr-2\" />\n                  {isProcessing ? '处理中...' : '批准'}\n                </button>\n                <button\n                  onClick={() => setShowRejectModal(true)}\n                  disabled={isProcessing}\n                  className=\"px-6 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors flex items-center\"\n                >\n                  <XCircle className=\"w-4 h-4 mr-2\" />\n                  拒绝\n                </button>\n              </div>\n            )}\n          </div>\n        </div>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8\">\n          {/* Main Content */}\n          <div className=\"lg:col-span-2 space-y-8\">\n            {/* Features */}\n            <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n              <h2 className=\"text-xl font-semibold text-gray-900 mb-4\">主要功能</h2>\n              <ul className=\"space-y-3\">\n                {tool.features.map((feature, index) => (\n                  <li key={index} className=\"flex items-start\">\n                    <Star className=\"w-5 h-5 text-blue-600 mr-3 mt-0.5 flex-shrink-0\" />\n                    <span className=\"text-gray-700\">{feature}</span>\n                  </li>\n                ))}\n              </ul>\n            </div>\n\n            {/* Tags */}\n            <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n              <h2 className=\"text-xl font-semibold text-gray-900 mb-4\">标签</h2>\n              <div className=\"flex flex-wrap gap-2\">\n                {tool.tags.map((tag, index) => (\n                  <span\n                    key={index}\n                    className=\"inline-flex items-center px-3 py-1 rounded-md text-sm font-medium bg-blue-100 text-blue-800\"\n                  >\n                    <Tag className=\"w-3 h-3 mr-1\" />\n                    {tag}\n                  </span>\n                ))}\n              </div>\n            </div>\n\n            {/* Screenshots */}\n            <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n              <h2 className=\"text-xl font-semibold text-gray-900 mb-4\">截图预览</h2>\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                {tool.screenshots.map((screenshot, index) => (\n                  <img\n                    key={index}\n                    src={screenshot}\n                    alt={`${tool.name} 截图 ${index + 1}`}\n                    className=\"w-full h-48 object-cover rounded-lg border border-gray-200\"\n                  />\n                ))}\n              </div>\n            </div>\n          </div>\n\n          {/* Sidebar */}\n          <div className=\"space-y-6\">\n            {/* Submission Info */}\n            <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">提交信息</h3>\n              <div className=\"space-y-4\">\n                <div className=\"flex items-center\">\n                  <User className=\"w-5 h-5 text-gray-400 mr-3\" />\n                  <div>\n                    <div className=\"text-sm font-medium text-gray-900\">{tool.submitterName}</div>\n                    <div className=\"text-sm text-gray-500\">提交者</div>\n                  </div>\n                </div>\n                \n                <div className=\"flex items-center\">\n                  <Mail className=\"w-5 h-5 text-gray-400 mr-3\" />\n                  <div>\n                    <div className=\"text-sm font-medium text-gray-900\">{tool.submitterEmail}</div>\n                    <div className=\"text-sm text-gray-500\">联系邮箱</div>\n                  </div>\n                </div>\n                \n                <div className=\"flex items-center\">\n                  <Calendar className=\"w-5 h-5 text-gray-400 mr-3\" />\n                  <div>\n                    <div className=\"text-sm font-medium text-gray-900\">{formatDate(tool.submittedAt)}</div>\n                    <div className=\"text-sm text-gray-500\">提交时间</div>\n                  </div>\n                </div>\n                \n                {tool.publishDate && (\n                  <div className=\"flex items-center\">\n                    <Clock className=\"w-5 h-5 text-gray-400 mr-3\" />\n                    <div>\n                      <div className=\"text-sm font-medium text-gray-900\">\n                        {new Date(tool.publishDate).toLocaleDateString('zh-CN')}\n                      </div>\n                      <div className=\"text-sm text-gray-500\">期望发布日期</div>\n                    </div>\n                  </div>\n                )}\n              </div>\n            </div>\n\n            {/* Review Guidelines */}\n            <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-6\">\n              <div className=\"flex items-start\">\n                <AlertTriangle className=\"w-5 h-5 text-blue-600 mr-3 mt-0.5 flex-shrink-0\" />\n                <div>\n                  <h3 className=\"text-sm font-medium text-blue-900 mb-2\">审核指南</h3>\n                  <ul className=\"text-sm text-blue-800 space-y-1\">\n                    <li>• 验证工具网站是否可正常访问</li>\n                    <li>• 检查工具描述是否准确客观</li>\n                    <li>• 确认分类和标签是否合适</li>\n                    <li>• 评估工具质量和实用性</li>\n                    <li>• 检查是否存在重复提交</li>\n                  </ul>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Reject Modal */}\n        {showRejectModal && (\n          <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50\">\n            <div className=\"bg-white rounded-lg max-w-md w-full p-6\">\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">拒绝工具</h3>\n              <p className=\"text-sm text-gray-600 mb-4\">\n                请详细说明拒绝的原因，这将帮助提交者了解问题并改进他们的提交。\n              </p>\n              <textarea\n                value={rejectReason}\n                onChange={(e) => setRejectReason(e.target.value)}\n                rows={4}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                placeholder=\"请输入详细的拒绝原因...\"\n              />\n              <div className=\"flex justify-end space-x-3 mt-6\">\n                <button\n                  onClick={() => {\n                    setShowRejectModal(false);\n                    setRejectReason('');\n                  }}\n                  disabled={isProcessing}\n                  className=\"px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors\"\n                >\n                  取消\n                </button>\n                <button\n                  onClick={handleReject}\n                  disabled={!rejectReason.trim() || isProcessing}\n                  className=\"px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors\"\n                >\n                  {isProcessing ? '处理中...' : '确认拒绝'}\n                </button>\n              </div>\n            </div>\n          </div>\n        )}\n      </div>\n    </Layout>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AALA;;;;;AAqBA,mDAAmD;AACnD,MAAM,WAAW;IACf,IAAI;IACJ,MAAM;IACN,aAAa;IACb,SAAS;IACT,MAAM;IACN,UAAU;IACV,SAAS;IACT,MAAM;QAAC;QAAM;QAAO;QAAQ;QAAQ;KAAO;IAC3C,UAAU;QACR;QACA;QACA;QACA;QACA;QACA;KACD;IACD,eAAe;IACf,gBAAgB;IAChB,aAAa;IACb,QAAQ;IACR,aAAa;IACb,aAAa;QACX;QACA;QACA;KACD;AACH;AAEA,MAAM,iBAAyC;IAC7C,mBAAmB;IACnB,oBAAoB;IACpB,mBAAmB;IACnB,iBAAiB;IACjB,oBAAoB;IACpB,iBAAiB;IACjB,eAAe;IACf,kBAAkB;IAClB,aAAa;IACb,aAAa;IACb,gBAAgB;IAChB,oBAAoB;AACtB;AAEA,MAAM,gBAAwC;IAC5C,QAAQ;IACR,YAAY;IACZ,QAAQ;AACV;AAEe,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,KAAK,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACxB,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,gBAAgB;QACpB,gBAAgB;QAChB,IAAI;YACF,UAAU;YACV,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YACjD,MAAM;YACN,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAO;YACd,MAAM;QACR,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,eAAe;QACnB,IAAI,CAAC,aAAa,IAAI,IAAI;QAE1B,gBAAgB;QAChB,IAAI;YACF,UAAU;YACV,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YACjD,MAAM;YACN,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAO;YACd,MAAM;QACR,SAAU;YACR,gBAAgB;YAChB,mBAAmB;YACnB,gBAAgB;QAClB;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;YACtD,MAAM;YACN,OAAO;YACP,KAAK;YACL,MAAM;YACN,QAAQ;QACV;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,qBACE,6LAAC;oBAAK,WAAU;;sCACd,6LAAC,uMAAA,CAAA,QAAK;4BAAC,WAAU;;;;;;wBAAiB;;;;;;;YAIxC,KAAK;gBACH,qBACE,6LAAC;oBAAK,WAAU;;sCACd,6LAAC,8NAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;wBAAiB;;;;;;;YAI9C,KAAK;gBACH,qBACE,6LAAC;oBAAK,WAAU;;sCACd,6LAAC,+MAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;wBAAiB;;;;;;;YAI1C;gBACE,OAAO;QACX;IACF;IAEA,qBACE,6LAAC,+HAAA,CAAA,UAAM;kBACL,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BACC,SAAS,IAAM,OAAO,IAAI;4BAC1B,WAAU;;8CAEV,6LAAC,mNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;sCAIxC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,KAAK,KAAK,IAAI;4CACd,KAAK,KAAK,IAAI;4CACd,WAAU;;;;;;sDAEZ,6LAAC;;8DACC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEAAoC,KAAK,IAAI;;;;;;wDAC1D,eAAe,KAAK,MAAM;;;;;;;8DAE7B,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEACb,cAAc,CAAC,KAAK,QAAQ,CAAC;;;;;;sEAEhC,6LAAC;4DAAK,WAAU;;8EACd,6LAAC,qNAAA,CAAA,aAAU;oEAAC,WAAU;;;;;;gEACrB,aAAa,CAAC,KAAK,OAAO,CAAC;;;;;;;sEAE9B,6LAAC;4DACC,MAAM,KAAK,OAAO;4DAClB,QAAO;4DACP,KAAI;4DACJ,WAAU;;8EAEV,6LAAC,uMAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;gEAAiB;8EAElC,6LAAC,yNAAA,CAAA,eAAY;oEAAC,WAAU;;;;;;;;;;;;;;;;;;8DAG5B,6LAAC;oDAAE,WAAU;8DAA2B,KAAK,WAAW;;;;;;;;;;;;;;;;;;gCAK3D,KAAK,MAAM,KAAK,2BACf,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,SAAS;4CACT,UAAU;4CACV,WAAU;;8DAEV,6LAAC,8NAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;gDACtB,eAAe,WAAW;;;;;;;sDAE7B,6LAAC;4CACC,SAAS,IAAM,mBAAmB;4CAClC,UAAU;4CACV,WAAU;;8DAEV,6LAAC,+MAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;8BAQ9C,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,6LAAC;4CAAG,WAAU;sDACX,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,sBAC3B,6LAAC;oDAAe,WAAU;;sEACxB,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;sEAChB,6LAAC;4DAAK,WAAU;sEAAiB;;;;;;;mDAF1B;;;;;;;;;;;;;;;;8CASf,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,6LAAC;4CAAI,WAAU;sDACZ,KAAK,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK,sBACnB,6LAAC;oDAEC,WAAU;;sEAEV,6LAAC,mMAAA,CAAA,MAAG;4DAAC,WAAU;;;;;;wDACd;;mDAJI;;;;;;;;;;;;;;;;8CAWb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,6LAAC;4CAAI,WAAU;sDACZ,KAAK,WAAW,CAAC,GAAG,CAAC,CAAC,YAAY,sBACjC,6LAAC;oDAEC,KAAK;oDACL,KAAK,GAAG,KAAK,IAAI,CAAC,IAAI,EAAE,QAAQ,GAAG;oDACnC,WAAU;mDAHL;;;;;;;;;;;;;;;;;;;;;;sCAWf,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;sEAChB,6LAAC;;8EACC,6LAAC;oEAAI,WAAU;8EAAqC,KAAK,aAAa;;;;;;8EACtE,6LAAC;oEAAI,WAAU;8EAAwB;;;;;;;;;;;;;;;;;;8DAI3C,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;sEAChB,6LAAC;;8EACC,6LAAC;oEAAI,WAAU;8EAAqC,KAAK,cAAc;;;;;;8EACvE,6LAAC;oEAAI,WAAU;8EAAwB;;;;;;;;;;;;;;;;;;8DAI3C,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,6MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;sEACpB,6LAAC;;8EACC,6LAAC;oEAAI,WAAU;8EAAqC,WAAW,KAAK,WAAW;;;;;;8EAC/E,6LAAC;oEAAI,WAAU;8EAAwB;;;;;;;;;;;;;;;;;;gDAI1C,KAAK,WAAW,kBACf,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,uMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;sEACjB,6LAAC;;8EACC,6LAAC;oEAAI,WAAU;8EACZ,IAAI,KAAK,KAAK,WAAW,EAAE,kBAAkB,CAAC;;;;;;8EAEjD,6LAAC;oEAAI,WAAU;8EAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAQjD,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,2NAAA,CAAA,gBAAa;gDAAC,WAAU;;;;;;0DACzB,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAAyC;;;;;;kEACvD,6LAAC;wDAAG,WAAU;;0EACZ,6LAAC;0EAAG;;;;;;0EACJ,6LAAC;0EAAG;;;;;;0EACJ,6LAAC;0EAAG;;;;;;0EACJ,6LAAC;0EAAG;;;;;;0EACJ,6LAAC;0EAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBASf,iCACC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA2C;;;;;;0CACzD,6LAAC;gCAAE,WAAU;0CAA6B;;;;;;0CAG1C,6LAAC;gCACC,OAAO;gCACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;gCAC/C,MAAM;gCACN,WAAU;gCACV,aAAY;;;;;;0CAEd,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,SAAS;4CACP,mBAAmB;4CACnB,gBAAgB;wCAClB;wCACA,UAAU;wCACV,WAAU;kDACX;;;;;;kDAGD,6LAAC;wCACC,SAAS;wCACT,UAAU,CAAC,aAAa,IAAI,MAAM;wCAClC,WAAU;kDAET,eAAe,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS7C;GA7SwB;;QACP,qIAAA,CAAA,YAAS;QACT,qIAAA,CAAA,YAAS;;;KAFF", "debugId": null}}]}
{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/Layout.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Link from 'next/link';\nimport { Search, Menu, X } from 'lucide-react';\n\ninterface LayoutProps {\n  children: React.ReactNode;\n}\n\nconst Layout: React.FC<LayoutProps> = ({ children }) => {\n  const [isMenuOpen, setIsMenuOpen] = React.useState(false);\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header */}\n      <header className=\"bg-white shadow-sm border-b border-gray-200\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center h-16\">\n            {/* Logo */}\n            <div className=\"flex items-center\">\n              <Link href=\"/\" className=\"flex items-center space-x-2\">\n                <div className=\"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center\">\n                  <span className=\"text-white font-bold text-sm\">AI</span>\n                </div>\n                <span className=\"text-xl font-bold text-gray-900\">AI Tools</span>\n              </Link>\n            </div>\n\n            {/* Desktop Navigation */}\n            <nav className=\"hidden md:flex items-center space-x-8\">\n              <Link href=\"/\" className=\"text-gray-700 hover:text-blue-600 transition-colors\">\n                首页\n              </Link>\n              <Link href=\"/tools\" className=\"text-gray-700 hover:text-blue-600 transition-colors\">\n                工具目录\n              </Link>\n              <Link href=\"/categories\" className=\"text-gray-700 hover:text-blue-600 transition-colors\">\n                分类\n              </Link>\n              <Link href=\"/submit\" className=\"text-gray-700 hover:text-blue-600 transition-colors\">\n                提交工具\n              </Link>\n              <Link href=\"/dashboard\" className=\"text-gray-700 hover:text-blue-600 transition-colors\">\n                仪表板\n              </Link>\n              {/* Admin Navigation */}\n              <div className=\"relative group\">\n                <button className=\"text-gray-700 hover:text-blue-600 transition-colors flex items-center\">\n                  管理员\n                  <svg className=\"w-4 h-4 ml-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 9l-7 7-7-7\" />\n                  </svg>\n                </button>\n                <div className=\"absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg border border-gray-200 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50\">\n                  <div className=\"py-1\">\n                    <Link href=\"/admin\" className=\"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors\">\n                      审核中心\n                    </Link>\n                    <Link href=\"/admin/dashboard\" className=\"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors\">\n                      统计面板\n                    </Link>\n                  </div>\n                </div>\n              </div>\n            </nav>\n\n            {/* Search Bar */}\n            <div className=\"hidden md:flex items-center flex-1 max-w-md mx-8\">\n              <div className=\"relative w-full\">\n                <input\n                  type=\"text\"\n                  placeholder=\"搜索 AI 工具...\"\n                  className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                />\n                <Search className=\"absolute left-3 top-2.5 h-5 w-5 text-gray-400\" />\n              </div>\n            </div>\n\n            {/* Mobile menu button */}\n            <div className=\"md:hidden\">\n              <button\n                onClick={() => setIsMenuOpen(!isMenuOpen)}\n                className=\"text-gray-700 hover:text-blue-600\"\n              >\n                {isMenuOpen ? <X className=\"h-6 w-6\" /> : <Menu className=\"h-6 w-6\" />}\n              </button>\n            </div>\n          </div>\n        </div>\n\n        {/* Mobile Navigation */}\n        {isMenuOpen && (\n          <div className=\"md:hidden bg-white border-t border-gray-200\">\n            <div className=\"px-4 py-2 space-y-1\">\n              <Link\n                href=\"/\"\n                className=\"block px-3 py-2 text-gray-700 hover:text-blue-600 hover:bg-gray-50 rounded-md\"\n              >\n                首页\n              </Link>\n              <Link\n                href=\"/tools\"\n                className=\"block px-3 py-2 text-gray-700 hover:text-blue-600 hover:bg-gray-50 rounded-md\"\n              >\n                工具目录\n              </Link>\n              <Link\n                href=\"/categories\"\n                className=\"block px-3 py-2 text-gray-700 hover:text-blue-600 hover:bg-gray-50 rounded-md\"\n              >\n                分类\n              </Link>\n              <Link\n                href=\"/submit\"\n                className=\"block px-3 py-2 text-gray-700 hover:text-blue-600 hover:bg-gray-50 rounded-md\"\n              >\n                提交工具\n              </Link>\n              <Link\n                href=\"/dashboard\"\n                className=\"block px-3 py-2 text-gray-700 hover:text-blue-600 hover:bg-gray-50 rounded-md\"\n              >\n                仪表板\n              </Link>\n              {/* Admin Links */}\n              <div className=\"border-t border-gray-200 pt-4 mt-4\">\n                <div className=\"px-3 py-2 text-sm font-medium text-gray-500\">管理员</div>\n                <Link\n                  href=\"/admin\"\n                  className=\"block px-3 py-2 text-gray-700 hover:text-blue-600 hover:bg-gray-50 rounded-md\"\n                >\n                  审核中心\n                </Link>\n                <Link\n                  href=\"/admin/dashboard\"\n                  className=\"block px-3 py-2 text-gray-700 hover:text-blue-600 hover:bg-gray-50 rounded-md\"\n                >\n                  统计面板\n                </Link>\n              </div>\n              {/* Mobile Search */}\n              <div className=\"px-3 py-2\">\n                <div className=\"relative\">\n                  <input\n                    type=\"text\"\n                    placeholder=\"搜索 AI 工具...\"\n                    className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                  />\n                  <Search className=\"absolute left-3 top-2.5 h-5 w-5 text-gray-400\" />\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n      </header>\n\n      {/* Main Content */}\n      <main className=\"flex-1\">\n        {children}\n      </main>\n\n      {/* Footer */}\n      <footer className=\"bg-white border-t border-gray-200 mt-16\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n          <div className=\"grid grid-cols-1 md:grid-cols-4 gap-8\">\n            <div className=\"col-span-1 md:col-span-2\">\n              <div className=\"flex items-center space-x-2 mb-4\">\n                <div className=\"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center\">\n                  <span className=\"text-white font-bold text-sm\">AI</span>\n                </div>\n                <span className=\"text-xl font-bold text-gray-900\">AI Tools</span>\n              </div>\n              <p className=\"text-gray-600 mb-4\">\n                发现最新最好的 AI 工具，提升您的工作效率和创造力。\n              </p>\n            </div>\n            \n            <div>\n              <h3 className=\"text-sm font-semibold text-gray-900 tracking-wider uppercase mb-4\">\n                快速链接\n              </h3>\n              <ul className=\"space-y-2\">\n                <li>\n                  <Link href=\"/tools\" className=\"text-gray-600 hover:text-blue-600\">\n                    工具目录\n                  </Link>\n                </li>\n                <li>\n                  <Link href=\"/categories\" className=\"text-gray-600 hover:text-blue-600\">\n                    分类浏览\n                  </Link>\n                </li>\n                <li>\n                  <Link href=\"/submit\" className=\"text-gray-600 hover:text-blue-600\">\n                    提交工具\n                  </Link>\n                </li>\n              </ul>\n            </div>\n            \n            <div>\n              <h3 className=\"text-sm font-semibold text-gray-900 tracking-wider uppercase mb-4\">\n                支持\n              </h3>\n              <ul className=\"space-y-2\">\n                <li>\n                  <a href=\"#\" className=\"text-gray-600 hover:text-blue-600\">\n                    帮助中心\n                  </a>\n                </li>\n                <li>\n                  <a href=\"#\" className=\"text-gray-600 hover:text-blue-600\">\n                    联系我们\n                  </a>\n                </li>\n                <li>\n                  <a href=\"#\" className=\"text-gray-600 hover:text-blue-600\">\n                    隐私政策\n                  </a>\n                </li>\n              </ul>\n            </div>\n          </div>\n          \n          <div className=\"border-t border-gray-200 mt-8 pt-8\">\n            <p className=\"text-center text-gray-600\">\n              © 2024 AI Tools Directory. All rights reserved.\n            </p>\n          </div>\n        </div>\n      </footer>\n    </div>\n  );\n};\n\nexport default Layout;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;;;AAJA;;;;AAUA,MAAM,SAAgC,CAAC,EAAE,QAAQ,EAAE;;IACjD,MAAM,CAAC,YAAY,cAAc,GAAG,6JAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IAEnD,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAO,WAAU;;kCAChB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAI,WAAU;;0DACvB,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAK,WAAU;8DAA+B;;;;;;;;;;;0DAEjD,6LAAC;gDAAK,WAAU;0DAAkC;;;;;;;;;;;;;;;;;8CAKtD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAI,WAAU;sDAAsD;;;;;;sDAG/E,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAS,WAAU;sDAAsD;;;;;;sDAGpF,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAc,WAAU;sDAAsD;;;;;;sDAGzF,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAU,WAAU;sDAAsD;;;;;;sDAGrF,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAa,WAAU;sDAAsD;;;;;;sDAIxF,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAO,WAAU;;wDAAwE;sEAExF,6LAAC;4DAAI,WAAU;4DAAe,MAAK;4DAAO,QAAO;4DAAe,SAAQ;sEACtE,cAAA,6LAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAG,GAAE;;;;;;;;;;;;;;;;;8DAGzE,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,+JAAA,CAAA,UAAI;gEAAC,MAAK;gEAAS,WAAU;0EAA4E;;;;;;0EAG1G,6LAAC,+JAAA,CAAA,UAAI;gEAAC,MAAK;gEAAmB,WAAU;0EAA4E;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAS5H,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,MAAK;gDACL,aAAY;gDACZ,WAAU;;;;;;0DAEZ,6LAAC,yMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;;;;;;;;;;;;8CAKtB,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCACC,SAAS,IAAM,cAAc,CAAC;wCAC9B,WAAU;kDAET,2BAAa,6LAAC,+LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;iEAAe,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAOjE,4BACC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAID,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAA8C;;;;;;sDAC7D,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;sDAGD,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;;;;;;;8CAKH,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,MAAK;gDACL,aAAY;gDACZ,WAAU;;;;;;0DAEZ,6LAAC,yMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS9B,6LAAC;gBAAK,WAAU;0BACb;;;;;;0BAIH,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAK,WAAU;kEAA+B;;;;;;;;;;;8DAEjD,6LAAC;oDAAK,WAAU;8DAAkC;;;;;;;;;;;;sDAEpD,6LAAC;4CAAE,WAAU;sDAAqB;;;;;;;;;;;;8CAKpC,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAoE;;;;;;sDAGlF,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;8DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAS,WAAU;kEAAoC;;;;;;;;;;;8DAIpE,6LAAC;8DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAc,WAAU;kEAAoC;;;;;;;;;;;8DAIzE,6LAAC;8DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAU,WAAU;kEAAoC;;;;;;;;;;;;;;;;;;;;;;;8CAOzE,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAoE;;;;;;sDAGlF,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;8DACC,cAAA,6LAAC;wDAAE,MAAK;wDAAI,WAAU;kEAAoC;;;;;;;;;;;8DAI5D,6LAAC;8DACC,cAAA,6LAAC;wDAAE,MAAK;wDAAI,WAAU;kEAAoC;;;;;;;;;;;8DAI5D,6LAAC;8DACC,cAAA,6LAAC;wDAAE,MAAK;wDAAI,WAAU;kEAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQlE,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAE,WAAU;0CAA4B;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQrD;GAhOM;KAAA;uCAkOS", "debugId": null}}, {"offset": {"line": 666, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/app/submit/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport Layout from '@/components/Layout';\nimport LoadingSpinner from '@/components/LoadingSpinner';\nimport ErrorMessage from '@/components/ErrorMessage';\nimport SuccessMessage from '@/components/SuccessMessage';\nimport { apiClient } from '@/lib/api';\nimport {\n  Upload,\n  Link as LinkIcon,\n  Tag,\n  Calendar,\n  Info,\n  CheckCircle,\n  AlertCircle\n} from 'lucide-react';\n\nconst categories = [\n  { value: 'text-generation', label: '文本生成' },\n  { value: 'image-generation', label: '图像生成' },\n  { value: 'code-generation', label: '代码生成' },\n  { value: 'data-analysis', label: '数据分析' },\n  { value: 'audio-processing', label: '音频处理' },\n  { value: 'video-editing', label: '视频编辑' },\n  { value: 'translation', label: '语言翻译' },\n  { value: 'search-engines', label: '搜索引擎' },\n  { value: 'education', label: '教育学习' },\n  { value: 'marketing', label: '营销工具' },\n  { value: 'productivity', label: '生产力工具' },\n  { value: 'customer-service', label: '客户服务' }\n];\n\nconst pricingOptions = [\n  { value: 'free', label: '免费' },\n  { value: 'freemium', label: '免费增值' },\n  { value: 'paid', label: '付费' }\n];\n\ninterface FormData {\n  name: string;\n  description: string;\n  website: string;\n  logo: string;\n  category: string;\n  tags: string[];\n  pricing: string;\n  features: string[];\n  submitterName: string;\n  submitterEmail: string;\n  publishDate: string;\n}\n\nexport default function SubmitPage() {\n  const [formData, setFormData] = useState<FormData>({\n    name: '',\n    description: '',\n    website: '',\n    logo: '',\n    category: '',\n    tags: [],\n    pricing: '',\n    features: [],\n    submitterName: '',\n    submitterEmail: '',\n    publishDate: ''\n  });\n\n  const [currentTag, setCurrentTag] = useState('');\n  const [currentFeature, setCurrentFeature] = useState('');\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle');\n  const [errors, setErrors] = useState<Record<string, string>>({});\n  const [submitMessage, setSubmitMessage] = useState('');\n\n  const validateForm = (): boolean => {\n    const newErrors: Record<string, string> = {};\n\n    if (!formData.name.trim()) newErrors.name = '工具名称是必填项';\n    if (!formData.description.trim()) newErrors.description = '工具描述是必填项';\n    if (!formData.website.trim()) newErrors.website = '官方网站是必填项';\n    if (!formData.category) newErrors.category = '请选择一个分类';\n    if (!formData.pricing) newErrors.pricing = '请选择价格模式';\n    if (!formData.submitterName.trim()) newErrors.submitterName = '提交者姓名是必填项';\n    if (!formData.submitterEmail.trim()) newErrors.submitterEmail = '邮箱地址是必填项';\n\n    // URL validation\n    if (formData.website && !formData.website.match(/^https?:\\/\\/.+/)) {\n      newErrors.website = '请输入有效的网站地址（以 http:// 或 https:// 开头）';\n    }\n\n    // Email validation\n    if (formData.submitterEmail && !formData.submitterEmail.match(/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/)) {\n      newErrors.submitterEmail = '请输入有效的邮箱地址';\n    }\n\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n\n    if (!validateForm()) return;\n\n    setIsSubmitting(true);\n    setSubmitStatus('idle');\n\n    try {\n      // 模拟 API 调用\n      await new Promise(resolve => setTimeout(resolve, 2000));\n\n      // 在实际应用中，这里会调用 API\n      console.log('Submitting tool:', formData);\n\n      setSubmitStatus('success');\n      // 重置表单\n      setFormData({\n        name: '',\n        description: '',\n        website: '',\n        logo: '',\n        category: '',\n        tags: [],\n        pricing: '',\n        features: [],\n        submitterName: '',\n        submitterEmail: '',\n        publishDate: ''\n      });\n    } catch (error) {\n      setSubmitStatus('error');\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  const addTag = () => {\n    if (currentTag.trim() && !formData.tags.includes(currentTag.trim())) {\n      setFormData(prev => ({\n        ...prev,\n        tags: [...prev.tags, currentTag.trim()]\n      }));\n      setCurrentTag('');\n    }\n  };\n\n  const removeTag = (tagToRemove: string) => {\n    setFormData(prev => ({\n      ...prev,\n      tags: prev.tags.filter(tag => tag !== tagToRemove)\n    }));\n  };\n\n  const addFeature = () => {\n    if (currentFeature.trim() && !formData.features.includes(currentFeature.trim())) {\n      setFormData(prev => ({\n        ...prev,\n        features: [...prev.features, currentFeature.trim()]\n      }));\n      setCurrentFeature('');\n    }\n  };\n\n  const removeFeature = (featureToRemove: string) => {\n    setFormData(prev => ({\n      ...prev,\n      features: prev.features.filter(feature => feature !== featureToRemove)\n    }));\n  };\n\n  return (\n    <Layout>\n      <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {/* Header */}\n        <div className=\"text-center mb-8\">\n          <h1 className=\"text-3xl font-bold text-gray-900 mb-4\">\n            <Upload className=\"inline-block mr-3 h-8 w-8 text-blue-600\" />\n            提交 AI 工具\n          </h1>\n          <p className=\"text-lg text-gray-600 max-w-2xl mx-auto\">\n            分享您发现或开发的优秀 AI 工具，帮助更多人发现和使用这些工具。我们会在审核通过后发布您的提交。\n          </p>\n        </div>\n\n        {/* Success/Error Messages */}\n        {submitStatus === 'success' && (\n          <div className=\"bg-green-50 border border-green-200 rounded-lg p-4 mb-6\">\n            <div className=\"flex items-center\">\n              <CheckCircle className=\"h-5 w-5 text-green-600 mr-2\" />\n              <span className=\"text-green-800\">\n                工具提交成功！我们会在 1-3 个工作日内审核您的提交。\n              </span>\n            </div>\n          </div>\n        )}\n\n        {submitStatus === 'error' && (\n          <div className=\"bg-red-50 border border-red-200 rounded-lg p-4 mb-6\">\n            <div className=\"flex items-center\">\n              <AlertCircle className=\"h-5 w-5 text-red-600 mr-2\" />\n              <span className=\"text-red-800\">\n                提交失败，请检查网络连接后重试。\n              </span>\n            </div>\n          </div>\n        )}\n\n        {/* Form */}\n        <form onSubmit={handleSubmit} className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-8\">\n          {/* Basic Information */}\n          <div className=\"mb-8\">\n            <h2 className=\"text-xl font-semibold text-gray-900 mb-4\">基本信息</h2>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  工具名称 *\n                </label>\n                <input\n                  type=\"text\"\n                  value={formData.name}\n                  onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}\n                  className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${\n                    errors.name ? 'border-red-300' : 'border-gray-300'\n                  }`}\n                  placeholder=\"例如：ChatGPT\"\n                />\n                {errors.name && <p className=\"text-red-600 text-sm mt-1\">{errors.name}</p>}\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  官方网站 *\n                </label>\n                <div className=\"relative\">\n                  <input\n                    type=\"url\"\n                    value={formData.website}\n                    onChange={(e) => setFormData(prev => ({ ...prev, website: e.target.value }))}\n                    className={`w-full pl-10 pr-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${\n                      errors.website ? 'border-red-300' : 'border-gray-300'\n                    }`}\n                    placeholder=\"https://example.com\"\n                  />\n                  <LinkIcon className=\"absolute left-3 top-2.5 h-5 w-5 text-gray-400\" />\n                </div>\n                {errors.website && <p className=\"text-red-600 text-sm mt-1\">{errors.website}</p>}\n              </div>\n            </div>\n\n            <div className=\"mt-6\">\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                工具描述 *\n              </label>\n              <textarea\n                value={formData.description}\n                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}\n                rows={4}\n                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${\n                  errors.description ? 'border-red-300' : 'border-gray-300'\n                }`}\n                placeholder=\"详细描述这个 AI 工具的功能和特点...\"\n              />\n              {errors.description && <p className=\"text-red-600 text-sm mt-1\">{errors.description}</p>}\n            </div>\n\n            <div className=\"mt-6\">\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Logo URL（可选）\n              </label>\n              <input\n                type=\"url\"\n                value={formData.logo}\n                onChange={(e) => setFormData(prev => ({ ...prev, logo: e.target.value }))}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                placeholder=\"https://example.com/logo.png\"\n              />\n            </div>\n          </div>\n\n          {/* Category and Pricing */}\n          <div className=\"mb-8\">\n            <h2 className=\"text-xl font-semibold text-gray-900 mb-4\">分类和定价</h2>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  工具分类 *\n                </label>\n                <select\n                  value={formData.category}\n                  onChange={(e) => setFormData(prev => ({ ...prev, category: e.target.value }))}\n                  className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${\n                    errors.category ? 'border-red-300' : 'border-gray-300'\n                  }`}\n                >\n                  <option value=\"\">请选择分类</option>\n                  {categories.map(category => (\n                    <option key={category.value} value={category.value}>\n                      {category.label}\n                    </option>\n                  ))}\n                </select>\n                {errors.category && <p className=\"text-red-600 text-sm mt-1\">{errors.category}</p>}\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  价格模式 *\n                </label>\n                <select\n                  value={formData.pricing}\n                  onChange={(e) => setFormData(prev => ({ ...prev, pricing: e.target.value }))}\n                  className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${\n                    errors.pricing ? 'border-red-300' : 'border-gray-300'\n                  }`}\n                >\n                  <option value=\"\">请选择价格模式</option>\n                  {pricingOptions.map(option => (\n                    <option key={option.value} value={option.value}>\n                      {option.label}\n                    </option>\n                  ))}\n                </select>\n                {errors.pricing && <p className=\"text-red-600 text-sm mt-1\">{errors.pricing}</p>}\n              </div>\n            </div>\n          </div>\n\n          {/* Tags */}\n          <div className=\"mb-8\">\n            <h2 className=\"text-xl font-semibold text-gray-900 mb-4\">标签</h2>\n\n            <div className=\"mb-4\">\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                添加标签（可选）\n              </label>\n              <div className=\"flex gap-2\">\n                <div className=\"relative flex-1\">\n                  <input\n                    type=\"text\"\n                    value={currentTag}\n                    onChange={(e) => setCurrentTag(e.target.value)}\n                    onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addTag())}\n                    className=\"w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                    placeholder=\"输入标签，按回车添加\"\n                  />\n                  <Tag className=\"absolute left-3 top-2.5 h-5 w-5 text-gray-400\" />\n                </div>\n                <button\n                  type=\"button\"\n                  onClick={addTag}\n                  className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n                >\n                  添加\n                </button>\n              </div>\n            </div>\n\n            {formData.tags.length > 0 && (\n              <div className=\"flex flex-wrap gap-2\">\n                {formData.tags.map((tag, index) => (\n                  <span\n                    key={index}\n                    className=\"inline-flex items-center px-3 py-1 rounded-md text-sm font-medium bg-blue-100 text-blue-800\"\n                  >\n                    {tag}\n                    <button\n                      type=\"button\"\n                      onClick={() => removeTag(tag)}\n                      className=\"ml-2 text-blue-600 hover:text-blue-800\"\n                    >\n                      ×\n                    </button>\n                  </span>\n                ))}\n              </div>\n            )}\n          </div>\n\n          {/* Features */}\n          <div className=\"mb-8\">\n            <h2 className=\"text-xl font-semibold text-gray-900 mb-4\">主要功能</h2>\n\n            <div className=\"mb-4\">\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                添加功能特点（可选）\n              </label>\n              <div className=\"flex gap-2\">\n                <input\n                  type=\"text\"\n                  value={currentFeature}\n                  onChange={(e) => setCurrentFeature(e.target.value)}\n                  onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addFeature())}\n                  className=\"flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                  placeholder=\"描述一个主要功能，按回车添加\"\n                />\n                <button\n                  type=\"button\"\n                  onClick={addFeature}\n                  className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n                >\n                  添加\n                </button>\n              </div>\n            </div>\n\n            {formData.features.length > 0 && (\n              <ul className=\"space-y-2\">\n                {formData.features.map((feature, index) => (\n                  <li\n                    key={index}\n                    className=\"flex items-center justify-between p-3 bg-gray-50 rounded-lg\"\n                  >\n                    <span className=\"text-gray-700\">{feature}</span>\n                    <button\n                      type=\"button\"\n                      onClick={() => removeFeature(feature)}\n                      className=\"text-red-600 hover:text-red-800\"\n                    >\n                      删除\n                    </button>\n                  </li>\n                ))}\n              </ul>\n            )}\n          </div>\n\n          {/* Submitter Information */}\n          <div className=\"mb-8\">\n            <h2 className=\"text-xl font-semibold text-gray-900 mb-4\">提交者信息</h2>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  您的姓名 *\n                </label>\n                <input\n                  type=\"text\"\n                  value={formData.submitterName}\n                  onChange={(e) => setFormData(prev => ({ ...prev, submitterName: e.target.value }))}\n                  className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${\n                    errors.submitterName ? 'border-red-300' : 'border-gray-300'\n                  }`}\n                  placeholder=\"请输入您的姓名\"\n                />\n                {errors.submitterName && <p className=\"text-red-600 text-sm mt-1\">{errors.submitterName}</p>}\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  邮箱地址 *\n                </label>\n                <input\n                  type=\"email\"\n                  value={formData.submitterEmail}\n                  onChange={(e) => setFormData(prev => ({ ...prev, submitterEmail: e.target.value }))}\n                  className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${\n                    errors.submitterEmail ? 'border-red-300' : 'border-gray-300'\n                  }`}\n                  placeholder=\"<EMAIL>\"\n                />\n                {errors.submitterEmail && <p className=\"text-red-600 text-sm mt-1\">{errors.submitterEmail}</p>}\n              </div>\n            </div>\n\n            <div className=\"mt-6\">\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                期望发布日期（可选）\n              </label>\n              <div className=\"relative\">\n                <input\n                  type=\"date\"\n                  value={formData.publishDate}\n                  onChange={(e) => setFormData(prev => ({ ...prev, publishDate: e.target.value }))}\n                  className=\"w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                />\n                <Calendar className=\"absolute left-3 top-2.5 h-5 w-5 text-gray-400\" />\n              </div>\n              <p className=\"text-sm text-gray-500 mt-1\">\n                如果您希望在特定日期发布，请选择日期。否则我们会在审核通过后立即发布。\n              </p>\n            </div>\n          </div>\n\n          {/* Guidelines */}\n          <div className=\"mb-8 bg-blue-50 border border-blue-200 rounded-lg p-6\">\n            <div className=\"flex items-start\">\n              <Info className=\"h-5 w-5 text-blue-600 mr-3 mt-0.5 flex-shrink-0\" />\n              <div>\n                <h3 className=\"text-sm font-medium text-blue-900 mb-2\">提交指南</h3>\n                <ul className=\"text-sm text-blue-800 space-y-1\">\n                  <li>• 请确保提交的是真实存在且可正常访问的 AI 工具</li>\n                  <li>• 工具描述应该准确、客观，避免过度营销</li>\n                  <li>• 我们会在 1-3 个工作日内审核您的提交</li>\n                  <li>• 审核通过后，工具将出现在我们的目录中</li>\n                  <li>• 如有问题，我们会通过邮箱联系您</li>\n                </ul>\n              </div>\n            </div>\n          </div>\n\n          {/* Submit Button */}\n          <div className=\"flex justify-end\">\n            <button\n              type=\"submit\"\n              disabled={isSubmitting}\n              className={`px-8 py-3 rounded-lg font-medium transition-colors ${\n                isSubmitting\n                  ? 'bg-gray-400 text-gray-700 cursor-not-allowed'\n                  : 'bg-blue-600 text-white hover:bg-blue-700'\n              }`}\n            >\n              {isSubmitting ? '提交中...' : '提交工具'}\n            </button>\n          </div>\n        </form>\n      </div>\n    </Layout>\n  );\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AAKA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AARA;;;;AAkBA,MAAM,aAAa;IACjB;QAAE,OAAO;QAAmB,OAAO;IAAO;IAC1C;QAAE,OAAO;QAAoB,OAAO;IAAO;IAC3C;QAAE,OAAO;QAAmB,OAAO;IAAO;IAC1C;QAAE,OAAO;QAAiB,OAAO;IAAO;IACxC;QAAE,OAAO;QAAoB,OAAO;IAAO;IAC3C;QAAE,OAAO;QAAiB,OAAO;IAAO;IACxC;QAAE,OAAO;QAAe,OAAO;IAAO;IACtC;QAAE,OAAO;QAAkB,OAAO;IAAO;IACzC;QAAE,OAAO;QAAa,OAAO;IAAO;IACpC;QAAE,OAAO;QAAa,OAAO;IAAO;IACpC;QAAE,OAAO;QAAgB,OAAO;IAAQ;IACxC;QAAE,OAAO;QAAoB,OAAO;IAAO;CAC5C;AAED,MAAM,iBAAiB;IACrB;QAAE,OAAO;QAAQ,OAAO;IAAK;IAC7B;QAAE,OAAO;QAAY,OAAO;IAAO;IACnC;QAAE,OAAO;QAAQ,OAAO;IAAK;CAC9B;AAgBc,SAAS;;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY;QACjD,MAAM;QACN,aAAa;QACb,SAAS;QACT,MAAM;QACN,UAAU;QACV,MAAM,EAAE;QACR,SAAS;QACT,UAAU,EAAE;QACZ,eAAe;QACf,gBAAgB;QAChB,aAAa;IACf;IAEA,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgC;IAC/E,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAC9D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,MAAM,eAAe;QACnB,MAAM,YAAoC,CAAC;QAE3C,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,IAAI,UAAU,IAAI,GAAG;QAC5C,IAAI,CAAC,SAAS,WAAW,CAAC,IAAI,IAAI,UAAU,WAAW,GAAG;QAC1D,IAAI,CAAC,SAAS,OAAO,CAAC,IAAI,IAAI,UAAU,OAAO,GAAG;QAClD,IAAI,CAAC,SAAS,QAAQ,EAAE,UAAU,QAAQ,GAAG;QAC7C,IAAI,CAAC,SAAS,OAAO,EAAE,UAAU,OAAO,GAAG;QAC3C,IAAI,CAAC,SAAS,aAAa,CAAC,IAAI,IAAI,UAAU,aAAa,GAAG;QAC9D,IAAI,CAAC,SAAS,cAAc,CAAC,IAAI,IAAI,UAAU,cAAc,GAAG;QAEhE,iBAAiB;QACjB,IAAI,SAAS,OAAO,IAAI,CAAC,SAAS,OAAO,CAAC,KAAK,CAAC,mBAAmB;YACjE,UAAU,OAAO,GAAG;QACtB;QAEA,mBAAmB;QACnB,IAAI,SAAS,cAAc,IAAI,CAAC,SAAS,cAAc,CAAC,KAAK,CAAC,+BAA+B;YAC3F,UAAU,cAAc,GAAG;QAC7B;QAEA,UAAU;QACV,OAAO,OAAO,IAAI,CAAC,WAAW,MAAM,KAAK;IAC3C;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,gBAAgB;QAErB,gBAAgB;QAChB,gBAAgB;QAEhB,IAAI;YACF,YAAY;YACZ,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,mBAAmB;YACnB,QAAQ,GAAG,CAAC,oBAAoB;YAEhC,gBAAgB;YAChB,OAAO;YACP,YAAY;gBACV,MAAM;gBACN,aAAa;gBACb,SAAS;gBACT,MAAM;gBACN,UAAU;gBACV,MAAM,EAAE;gBACR,SAAS;gBACT,UAAU,EAAE;gBACZ,eAAe;gBACf,gBAAgB;gBAChB,aAAa;YACf;QACF,EAAE,OAAO,OAAO;YACd,gBAAgB;QAClB,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,SAAS;QACb,IAAI,WAAW,IAAI,MAAM,CAAC,SAAS,IAAI,CAAC,QAAQ,CAAC,WAAW,IAAI,KAAK;YACnE,YAAY,CAAA,OAAQ,CAAC;oBACnB,GAAG,IAAI;oBACP,MAAM;2BAAI,KAAK,IAAI;wBAAE,WAAW,IAAI;qBAAG;gBACzC,CAAC;YACD,cAAc;QAChB;IACF;IAEA,MAAM,YAAY,CAAC;QACjB,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,MAAM,KAAK,IAAI,CAAC,MAAM,CAAC,CAAA,MAAO,QAAQ;YACxC,CAAC;IACH;IAEA,MAAM,aAAa;QACjB,IAAI,eAAe,IAAI,MAAM,CAAC,SAAS,QAAQ,CAAC,QAAQ,CAAC,eAAe,IAAI,KAAK;YAC/E,YAAY,CAAA,OAAQ,CAAC;oBACnB,GAAG,IAAI;oBACP,UAAU;2BAAI,KAAK,QAAQ;wBAAE,eAAe,IAAI;qBAAG;gBACrD,CAAC;YACD,kBAAkB;QACpB;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,UAAU,KAAK,QAAQ,CAAC,MAAM,CAAC,CAAA,UAAW,YAAY;YACxD,CAAC;IACH;IAEA,qBACE,6LAAC,+HAAA,CAAA,UAAM;kBACL,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;;8CACZ,6LAAC,yMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;gCAA4C;;;;;;;sCAGhE,6LAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;gBAMxD,iBAAiB,2BAChB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,8NAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;0CACvB,6LAAC;gCAAK,WAAU;0CAAiB;;;;;;;;;;;;;;;;;gBAOtC,iBAAiB,yBAChB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,uNAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;0CACvB,6LAAC;gCAAK,WAAU;0CAAe;;;;;;;;;;;;;;;;;8BAQrC,6LAAC;oBAAK,UAAU;oBAAc,WAAU;;sCAEtC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAA2C;;;;;;8CAEzD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,6LAAC;oDACC,MAAK;oDACL,OAAO,SAAS,IAAI;oDACpB,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;gEAAE,GAAG,IAAI;gEAAE,MAAM,EAAE,MAAM,CAAC,KAAK;4DAAC,CAAC;oDACvE,WAAW,CAAC,6FAA6F,EACvG,OAAO,IAAI,GAAG,mBAAmB,mBACjC;oDACF,aAAY;;;;;;gDAEb,OAAO,IAAI,kBAAI,6LAAC;oDAAE,WAAU;8DAA6B,OAAO,IAAI;;;;;;;;;;;;sDAGvE,6LAAC;;8DACC,6LAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DACC,MAAK;4DACL,OAAO,SAAS,OAAO;4DACvB,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;wEAAE,GAAG,IAAI;wEAAE,SAAS,EAAE,MAAM,CAAC,KAAK;oEAAC,CAAC;4DAC1E,WAAW,CAAC,mGAAmG,EAC7G,OAAO,OAAO,GAAG,mBAAmB,mBACpC;4DACF,aAAY;;;;;;sEAEd,6LAAC,qMAAA,CAAA,OAAQ;4DAAC,WAAU;;;;;;;;;;;;gDAErB,OAAO,OAAO,kBAAI,6LAAC;oDAAE,WAAU;8DAA6B,OAAO,OAAO;;;;;;;;;;;;;;;;;;8CAI/E,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,6LAAC;4CACC,OAAO,SAAS,WAAW;4CAC3B,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;wDAAE,GAAG,IAAI;wDAAE,aAAa,EAAE,MAAM,CAAC,KAAK;oDAAC,CAAC;4CAC9E,MAAM;4CACN,WAAW,CAAC,6FAA6F,EACvG,OAAO,WAAW,GAAG,mBAAmB,mBACxC;4CACF,aAAY;;;;;;wCAEb,OAAO,WAAW,kBAAI,6LAAC;4CAAE,WAAU;sDAA6B,OAAO,WAAW;;;;;;;;;;;;8CAGrF,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,6LAAC;4CACC,MAAK;4CACL,OAAO,SAAS,IAAI;4CACpB,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;wDAAE,GAAG,IAAI;wDAAE,MAAM,EAAE,MAAM,CAAC,KAAK;oDAAC,CAAC;4CACvE,WAAU;4CACV,aAAY;;;;;;;;;;;;;;;;;;sCAMlB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAA2C;;;;;;8CAEzD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,6LAAC;oDACC,OAAO,SAAS,QAAQ;oDACxB,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;gEAAE,GAAG,IAAI;gEAAE,UAAU,EAAE,MAAM,CAAC,KAAK;4DAAC,CAAC;oDAC3E,WAAW,CAAC,6FAA6F,EACvG,OAAO,QAAQ,GAAG,mBAAmB,mBACrC;;sEAEF,6LAAC;4DAAO,OAAM;sEAAG;;;;;;wDAChB,WAAW,GAAG,CAAC,CAAA,yBACd,6LAAC;gEAA4B,OAAO,SAAS,KAAK;0EAC/C,SAAS,KAAK;+DADJ,SAAS,KAAK;;;;;;;;;;;gDAK9B,OAAO,QAAQ,kBAAI,6LAAC;oDAAE,WAAU;8DAA6B,OAAO,QAAQ;;;;;;;;;;;;sDAG/E,6LAAC;;8DACC,6LAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,6LAAC;oDACC,OAAO,SAAS,OAAO;oDACvB,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;gEAAE,GAAG,IAAI;gEAAE,SAAS,EAAE,MAAM,CAAC,KAAK;4DAAC,CAAC;oDAC1E,WAAW,CAAC,6FAA6F,EACvG,OAAO,OAAO,GAAG,mBAAmB,mBACpC;;sEAEF,6LAAC;4DAAO,OAAM;sEAAG;;;;;;wDAChB,eAAe,GAAG,CAAC,CAAA,uBAClB,6LAAC;gEAA0B,OAAO,OAAO,KAAK;0EAC3C,OAAO,KAAK;+DADF,OAAO,KAAK;;;;;;;;;;;gDAK5B,OAAO,OAAO,kBAAI,6LAAC;oDAAE,WAAU;8DAA6B,OAAO,OAAO;;;;;;;;;;;;;;;;;;;;;;;;sCAMjF,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAA2C;;;;;;8CAEzD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DACC,MAAK;4DACL,OAAO;4DACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;4DAC7C,YAAY,CAAC,IAAM,EAAE,GAAG,KAAK,WAAW,CAAC,EAAE,cAAc,IAAI,QAAQ;4DACrE,WAAU;4DACV,aAAY;;;;;;sEAEd,6LAAC,mMAAA,CAAA,MAAG;4DAAC,WAAU;;;;;;;;;;;;8DAEjB,6LAAC;oDACC,MAAK;oDACL,SAAS;oDACT,WAAU;8DACX;;;;;;;;;;;;;;;;;;gCAMJ,SAAS,IAAI,CAAC,MAAM,GAAG,mBACtB,6LAAC;oCAAI,WAAU;8CACZ,SAAS,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK,sBACvB,6LAAC;4CAEC,WAAU;;gDAET;8DACD,6LAAC;oDACC,MAAK;oDACL,SAAS,IAAM,UAAU;oDACzB,WAAU;8DACX;;;;;;;2CARI;;;;;;;;;;;;;;;;sCAkBf,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAA2C;;;;;;8CAEzD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,MAAK;oDACL,OAAO;oDACP,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;oDACjD,YAAY,CAAC,IAAM,EAAE,GAAG,KAAK,WAAW,CAAC,EAAE,cAAc,IAAI,YAAY;oDACzE,WAAU;oDACV,aAAY;;;;;;8DAEd,6LAAC;oDACC,MAAK;oDACL,SAAS;oDACT,WAAU;8DACX;;;;;;;;;;;;;;;;;;gCAMJ,SAAS,QAAQ,CAAC,MAAM,GAAG,mBAC1B,6LAAC;oCAAG,WAAU;8CACX,SAAS,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,sBAC/B,6LAAC;4CAEC,WAAU;;8DAEV,6LAAC;oDAAK,WAAU;8DAAiB;;;;;;8DACjC,6LAAC;oDACC,MAAK;oDACL,SAAS,IAAM,cAAc;oDAC7B,WAAU;8DACX;;;;;;;2CARI;;;;;;;;;;;;;;;;sCAkBf,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAA2C;;;;;;8CAEzD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,6LAAC;oDACC,MAAK;oDACL,OAAO,SAAS,aAAa;oDAC7B,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;gEAAE,GAAG,IAAI;gEAAE,eAAe,EAAE,MAAM,CAAC,KAAK;4DAAC,CAAC;oDAChF,WAAW,CAAC,6FAA6F,EACvG,OAAO,aAAa,GAAG,mBAAmB,mBAC1C;oDACF,aAAY;;;;;;gDAEb,OAAO,aAAa,kBAAI,6LAAC;oDAAE,WAAU;8DAA6B,OAAO,aAAa;;;;;;;;;;;;sDAGzF,6LAAC;;8DACC,6LAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,6LAAC;oDACC,MAAK;oDACL,OAAO,SAAS,cAAc;oDAC9B,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;gEAAE,GAAG,IAAI;gEAAE,gBAAgB,EAAE,MAAM,CAAC,KAAK;4DAAC,CAAC;oDACjF,WAAW,CAAC,6FAA6F,EACvG,OAAO,cAAc,GAAG,mBAAmB,mBAC3C;oDACF,aAAY;;;;;;gDAEb,OAAO,cAAc,kBAAI,6LAAC;oDAAE,WAAU;8DAA6B,OAAO,cAAc;;;;;;;;;;;;;;;;;;8CAI7F,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,MAAK;oDACL,OAAO,SAAS,WAAW;oDAC3B,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;gEAAE,GAAG,IAAI;gEAAE,aAAa,EAAE,MAAM,CAAC,KAAK;4DAAC,CAAC;oDAC9E,WAAU;;;;;;8DAEZ,6LAAC,6MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;;;;;;;sDAEtB,6LAAC;4CAAE,WAAU;sDAA6B;;;;;;;;;;;;;;;;;;sCAO9C,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;kDAChB,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAyC;;;;;;0DACvD,6LAAC;gDAAG,WAAU;;kEACZ,6LAAC;kEAAG;;;;;;kEACJ,6LAAC;kEAAG;;;;;;kEACJ,6LAAC;kEAAG;;;;;;kEACJ,6LAAC;kEAAG;;;;;;kEACJ,6LAAC;kEAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAOZ,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,MAAK;gCACL,UAAU;gCACV,WAAW,CAAC,mDAAmD,EAC7D,eACI,iDACA,4CACJ;0CAED,eAAe,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOzC;GApdwB;KAAA", "debugId": null}}]}
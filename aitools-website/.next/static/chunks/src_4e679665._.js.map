{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/Layout.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Link from 'next/link';\nimport { Search, Menu, X } from 'lucide-react';\n\ninterface LayoutProps {\n  children: React.ReactNode;\n}\n\nconst Layout: React.FC<LayoutProps> = ({ children }) => {\n  const [isMenuOpen, setIsMenuOpen] = React.useState(false);\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header */}\n      <header className=\"bg-white shadow-sm border-b border-gray-200\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center h-16\">\n            {/* Logo */}\n            <div className=\"flex items-center\">\n              <Link href=\"/\" className=\"flex items-center space-x-2\">\n                <div className=\"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center\">\n                  <span className=\"text-white font-bold text-sm\">AI</span>\n                </div>\n                <span className=\"text-xl font-bold text-gray-900\">AI Tools</span>\n              </Link>\n            </div>\n\n            {/* Desktop Navigation */}\n            <nav className=\"hidden md:flex items-center space-x-8\">\n              <Link href=\"/\" className=\"text-gray-700 hover:text-blue-600 transition-colors\">\n                首页\n              </Link>\n              <Link href=\"/tools\" className=\"text-gray-700 hover:text-blue-600 transition-colors\">\n                工具目录\n              </Link>\n              <Link href=\"/categories\" className=\"text-gray-700 hover:text-blue-600 transition-colors\">\n                分类\n              </Link>\n              <Link href=\"/submit\" className=\"text-gray-700 hover:text-blue-600 transition-colors\">\n                提交工具\n              </Link>\n              <Link href=\"/dashboard\" className=\"text-gray-700 hover:text-blue-600 transition-colors\">\n                仪表板\n              </Link>\n              {/* Admin Navigation */}\n              <div className=\"relative group\">\n                <button className=\"text-gray-700 hover:text-blue-600 transition-colors flex items-center\">\n                  管理员\n                  <svg className=\"w-4 h-4 ml-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 9l-7 7-7-7\" />\n                  </svg>\n                </button>\n                <div className=\"absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg border border-gray-200 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50\">\n                  <div className=\"py-1\">\n                    <Link href=\"/admin\" className=\"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors\">\n                      审核中心\n                    </Link>\n                    <Link href=\"/admin/dashboard\" className=\"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors\">\n                      统计面板\n                    </Link>\n                  </div>\n                </div>\n              </div>\n            </nav>\n\n            {/* Search Bar */}\n            <div className=\"hidden md:flex items-center flex-1 max-w-md mx-8\">\n              <div className=\"relative w-full\">\n                <input\n                  type=\"text\"\n                  placeholder=\"搜索 AI 工具...\"\n                  className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                />\n                <Search className=\"absolute left-3 top-2.5 h-5 w-5 text-gray-400\" />\n              </div>\n            </div>\n\n            {/* Mobile menu button */}\n            <div className=\"md:hidden\">\n              <button\n                onClick={() => setIsMenuOpen(!isMenuOpen)}\n                className=\"text-gray-700 hover:text-blue-600\"\n              >\n                {isMenuOpen ? <X className=\"h-6 w-6\" /> : <Menu className=\"h-6 w-6\" />}\n              </button>\n            </div>\n          </div>\n        </div>\n\n        {/* Mobile Navigation */}\n        {isMenuOpen && (\n          <div className=\"md:hidden bg-white border-t border-gray-200\">\n            <div className=\"px-4 py-2 space-y-1\">\n              <Link\n                href=\"/\"\n                className=\"block px-3 py-2 text-gray-700 hover:text-blue-600 hover:bg-gray-50 rounded-md\"\n              >\n                首页\n              </Link>\n              <Link\n                href=\"/tools\"\n                className=\"block px-3 py-2 text-gray-700 hover:text-blue-600 hover:bg-gray-50 rounded-md\"\n              >\n                工具目录\n              </Link>\n              <Link\n                href=\"/categories\"\n                className=\"block px-3 py-2 text-gray-700 hover:text-blue-600 hover:bg-gray-50 rounded-md\"\n              >\n                分类\n              </Link>\n              <Link\n                href=\"/submit\"\n                className=\"block px-3 py-2 text-gray-700 hover:text-blue-600 hover:bg-gray-50 rounded-md\"\n              >\n                提交工具\n              </Link>\n              <Link\n                href=\"/dashboard\"\n                className=\"block px-3 py-2 text-gray-700 hover:text-blue-600 hover:bg-gray-50 rounded-md\"\n              >\n                仪表板\n              </Link>\n              {/* Admin Links */}\n              <div className=\"border-t border-gray-200 pt-4 mt-4\">\n                <div className=\"px-3 py-2 text-sm font-medium text-gray-500\">管理员</div>\n                <Link\n                  href=\"/admin\"\n                  className=\"block px-3 py-2 text-gray-700 hover:text-blue-600 hover:bg-gray-50 rounded-md\"\n                >\n                  审核中心\n                </Link>\n                <Link\n                  href=\"/admin/dashboard\"\n                  className=\"block px-3 py-2 text-gray-700 hover:text-blue-600 hover:bg-gray-50 rounded-md\"\n                >\n                  统计面板\n                </Link>\n              </div>\n              {/* Mobile Search */}\n              <div className=\"px-3 py-2\">\n                <div className=\"relative\">\n                  <input\n                    type=\"text\"\n                    placeholder=\"搜索 AI 工具...\"\n                    className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                  />\n                  <Search className=\"absolute left-3 top-2.5 h-5 w-5 text-gray-400\" />\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n      </header>\n\n      {/* Main Content */}\n      <main className=\"flex-1\">\n        {children}\n      </main>\n\n      {/* Footer */}\n      <footer className=\"bg-white border-t border-gray-200 mt-16\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n          <div className=\"grid grid-cols-1 md:grid-cols-4 gap-8\">\n            <div className=\"col-span-1 md:col-span-2\">\n              <div className=\"flex items-center space-x-2 mb-4\">\n                <div className=\"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center\">\n                  <span className=\"text-white font-bold text-sm\">AI</span>\n                </div>\n                <span className=\"text-xl font-bold text-gray-900\">AI Tools</span>\n              </div>\n              <p className=\"text-gray-600 mb-4\">\n                发现最新最好的 AI 工具，提升您的工作效率和创造力。\n              </p>\n            </div>\n            \n            <div>\n              <h3 className=\"text-sm font-semibold text-gray-900 tracking-wider uppercase mb-4\">\n                快速链接\n              </h3>\n              <ul className=\"space-y-2\">\n                <li>\n                  <Link href=\"/tools\" className=\"text-gray-600 hover:text-blue-600\">\n                    工具目录\n                  </Link>\n                </li>\n                <li>\n                  <Link href=\"/categories\" className=\"text-gray-600 hover:text-blue-600\">\n                    分类浏览\n                  </Link>\n                </li>\n                <li>\n                  <Link href=\"/submit\" className=\"text-gray-600 hover:text-blue-600\">\n                    提交工具\n                  </Link>\n                </li>\n              </ul>\n            </div>\n            \n            <div>\n              <h3 className=\"text-sm font-semibold text-gray-900 tracking-wider uppercase mb-4\">\n                支持\n              </h3>\n              <ul className=\"space-y-2\">\n                <li>\n                  <a href=\"#\" className=\"text-gray-600 hover:text-blue-600\">\n                    帮助中心\n                  </a>\n                </li>\n                <li>\n                  <a href=\"#\" className=\"text-gray-600 hover:text-blue-600\">\n                    联系我们\n                  </a>\n                </li>\n                <li>\n                  <a href=\"#\" className=\"text-gray-600 hover:text-blue-600\">\n                    隐私政策\n                  </a>\n                </li>\n              </ul>\n            </div>\n          </div>\n          \n          <div className=\"border-t border-gray-200 mt-8 pt-8\">\n            <p className=\"text-center text-gray-600\">\n              © 2024 AI Tools Directory. All rights reserved.\n            </p>\n          </div>\n        </div>\n      </footer>\n    </div>\n  );\n};\n\nexport default Layout;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;;;AAJA;;;;AAUA,MAAM,SAAgC,CAAC,EAAE,QAAQ,EAAE;;IACjD,MAAM,CAAC,YAAY,cAAc,GAAG,6JAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IAEnD,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAO,WAAU;;kCAChB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAI,WAAU;;0DACvB,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAK,WAAU;8DAA+B;;;;;;;;;;;0DAEjD,6LAAC;gDAAK,WAAU;0DAAkC;;;;;;;;;;;;;;;;;8CAKtD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAI,WAAU;sDAAsD;;;;;;sDAG/E,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAS,WAAU;sDAAsD;;;;;;sDAGpF,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAc,WAAU;sDAAsD;;;;;;sDAGzF,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAU,WAAU;sDAAsD;;;;;;sDAGrF,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAa,WAAU;sDAAsD;;;;;;sDAIxF,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAO,WAAU;;wDAAwE;sEAExF,6LAAC;4DAAI,WAAU;4DAAe,MAAK;4DAAO,QAAO;4DAAe,SAAQ;sEACtE,cAAA,6LAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAG,GAAE;;;;;;;;;;;;;;;;;8DAGzE,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,+JAAA,CAAA,UAAI;gEAAC,MAAK;gEAAS,WAAU;0EAA4E;;;;;;0EAG1G,6LAAC,+JAAA,CAAA,UAAI;gEAAC,MAAK;gEAAmB,WAAU;0EAA4E;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAS5H,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,MAAK;gDACL,aAAY;gDACZ,WAAU;;;;;;0DAEZ,6LAAC,yMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;;;;;;;;;;;;8CAKtB,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCACC,SAAS,IAAM,cAAc,CAAC;wCAC9B,WAAU;kDAET,2BAAa,6LAAC,+LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;iEAAe,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAOjE,4BACC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAID,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAA8C;;;;;;sDAC7D,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;sDAGD,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;;;;;;;8CAKH,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,MAAK;gDACL,aAAY;gDACZ,WAAU;;;;;;0DAEZ,6LAAC,yMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS9B,6LAAC;gBAAK,WAAU;0BACb;;;;;;0BAIH,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAK,WAAU;kEAA+B;;;;;;;;;;;8DAEjD,6LAAC;oDAAK,WAAU;8DAAkC;;;;;;;;;;;;sDAEpD,6LAAC;4CAAE,WAAU;sDAAqB;;;;;;;;;;;;8CAKpC,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAoE;;;;;;sDAGlF,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;8DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAS,WAAU;kEAAoC;;;;;;;;;;;8DAIpE,6LAAC;8DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAc,WAAU;kEAAoC;;;;;;;;;;;8DAIzE,6LAAC;8DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAU,WAAU;kEAAoC;;;;;;;;;;;;;;;;;;;;;;;8CAOzE,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAoE;;;;;;sDAGlF,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;8DACC,cAAA,6LAAC;wDAAE,MAAK;wDAAI,WAAU;kEAAoC;;;;;;;;;;;8DAI5D,6LAAC;8DACC,cAAA,6LAAC;wDAAE,MAAK;wDAAI,WAAU;kEAAoC;;;;;;;;;;;8DAI5D,6LAAC;8DACC,cAAA,6LAAC;wDAAE,MAAK;wDAAI,WAAU;kEAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQlE,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAE,WAAU;0CAA4B;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQrD;GAhOM;KAAA;uCAkOS", "debugId": null}}, {"offset": {"line": 666, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/LoadingSpinner.tsx"], "sourcesContent": ["'use client';\n\ninterface LoadingSpinnerProps {\n  size?: 'sm' | 'md' | 'lg';\n  className?: string;\n}\n\nexport default function LoadingSpinner({ size = 'md', className = '' }: LoadingSpinnerProps) {\n  const sizeClasses = {\n    sm: 'w-4 h-4',\n    md: 'w-8 h-8',\n    lg: 'w-12 h-12'\n  };\n\n  return (\n    <div className={`flex justify-center items-center ${className}`}>\n      <div className={`${sizeClasses[size]} border-4 border-gray-200 border-t-blue-600 rounded-full animate-spin`}></div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAOe,SAAS,eAAe,EAAE,OAAO,IAAI,EAAE,YAAY,EAAE,EAAuB;IACzF,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAC,iCAAiC,EAAE,WAAW;kBAC7D,cAAA,6LAAC;YAAI,WAAW,GAAG,WAAW,CAAC,KAAK,CAAC,qEAAqE,CAAC;;;;;;;;;;;AAGjH;KAZwB", "debugId": null}}, {"offset": {"line": 705, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/ErrorMessage.tsx"], "sourcesContent": ["'use client';\n\nimport { AlertCircle, X } from 'lucide-react';\n\ninterface ErrorMessageProps {\n  message: string;\n  onClose?: () => void;\n  className?: string;\n}\n\nexport default function ErrorMessage({ message, onClose, className = '' }: ErrorMessageProps) {\n  return (\n    <div className={`bg-red-50 border border-red-200 rounded-lg p-4 ${className}`}>\n      <div className=\"flex items-start\">\n        <AlertCircle className=\"w-5 h-5 text-red-500 mt-0.5 mr-3 flex-shrink-0\" />\n        <div className=\"flex-1\">\n          <p className=\"text-red-800 text-sm\">{message}</p>\n        </div>\n        {onClose && (\n          <button\n            onClick={onClose}\n            className=\"ml-3 text-red-400 hover:text-red-600 transition-colors\"\n          >\n            <X className=\"w-4 h-4\" />\n          </button>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAFA;;;AAUe,SAAS,aAAa,EAAE,OAAO,EAAE,OAAO,EAAE,YAAY,EAAE,EAAqB;IAC1F,qBACE,6LAAC;QAAI,WAAW,CAAC,+CAA+C,EAAE,WAAW;kBAC3E,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC,uNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;8BACvB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;gBAEtC,yBACC,6LAAC;oBACC,SAAS;oBACT,WAAU;8BAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;wBAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;AAMzB;KAnBwB", "debugId": null}}, {"offset": {"line": 781, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/lib/api.ts"], "sourcesContent": ["// API客户端工具类\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3001/api';\n\nexport interface ApiResponse<T = any> {\n  success: boolean;\n  data?: T;\n  error?: string;\n  message?: string;\n  details?: string[];\n}\n\nexport interface PaginationInfo {\n  currentPage: number;\n  totalPages: number;\n  totalItems: number;\n  itemsPerPage: number;\n  hasNextPage: boolean;\n  hasPrevPage: boolean;\n}\n\nexport interface Tool {\n  _id: string;\n  name: string;\n  description: string;\n  longDescription?: string;\n  website: string;\n  logo?: string;\n  category: string;\n  tags: string[];\n  pricing: 'free' | 'freemium' | 'paid';\n  pricingDetails?: string;\n  features: string[];\n  screenshots?: string[];\n  submittedBy: string;\n  submittedAt: string;\n  publishedAt?: string;\n  status: 'pending' | 'approved' | 'rejected';\n  reviewNotes?: string;\n  reviewedBy?: string;\n  reviewedAt?: string;\n  views: number;\n  likes: number;\n  isActive: boolean;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface ToolsResponse {\n  tools: Tool[];\n  pagination: PaginationInfo;\n  stats?: {\n    total: number;\n    pending: number;\n    approved: number;\n    rejected: number;\n  };\n}\n\nexport interface AdminStats {\n  overview: {\n    totalTools: number;\n    pendingTools: number;\n    approvedTools: number;\n    rejectedTools: number;\n    totalViews: number;\n    totalLikes: number;\n    recentSubmissions: number;\n    recentApprovals: number;\n    recentRejections: number;\n    avgReviewTime: number;\n  };\n  categoryStats: Array<{\n    _id: string;\n    count: number;\n    totalViews: number;\n    totalLikes: number;\n  }>;\n  topTools: Array<{\n    _id: string;\n    name: string;\n    category: string;\n    views: number;\n    likes: number;\n  }>;\n  recentActivity: Array<{\n    _id: string;\n    name: string;\n    submittedBy: string;\n    submittedAt: string;\n    status: string;\n    reviewedAt?: string;\n    reviewedBy?: string;\n  }>;\n  dailyStats: Array<{\n    date: string;\n    day: string;\n    submissions: number;\n    approvals: number;\n    rejections: number;\n  }>;\n  timeRange: string;\n}\n\nexport interface Category {\n  id: string;\n  name: string;\n  count: number;\n  totalViews: number;\n  totalLikes: number;\n}\n\nexport interface CategoriesResponse {\n  categories: Category[];\n  overview: {\n    totalTools: number;\n    totalViews: number;\n    totalLikes: number;\n  };\n}\n\nclass ApiClient {\n  private baseURL: string;\n\n  constructor(baseURL: string = API_BASE_URL) {\n    this.baseURL = baseURL;\n  }\n\n  private async request<T>(\n    endpoint: string,\n    options: RequestInit = {}\n  ): Promise<ApiResponse<T>> {\n    try {\n      const url = `${this.baseURL}${endpoint}`;\n      const config: RequestInit = {\n        headers: {\n          'Content-Type': 'application/json',\n          ...options.headers,\n        },\n        ...options,\n      };\n\n      const response = await fetch(url, config);\n      const data = await response.json();\n\n      if (!response.ok) {\n        throw new Error(data.error || `HTTP error! status: ${response.status}`);\n      }\n\n      return data;\n    } catch (error) {\n      console.error('API request failed:', error);\n      return {\n        success: false,\n        error: error instanceof Error ? error.message : '请求失败',\n      };\n    }\n  }\n\n  // 工具相关API\n  async getTools(params?: {\n    page?: number;\n    limit?: number;\n    category?: string;\n    status?: string;\n    search?: string;\n    sort?: string;\n    order?: string;\n  }): Promise<ApiResponse<ToolsResponse>> {\n    const searchParams = new URLSearchParams();\n    if (params) {\n      Object.entries(params).forEach(([key, value]) => {\n        if (value !== undefined) {\n          searchParams.append(key, value.toString());\n        }\n      });\n    }\n    const query = searchParams.toString();\n    return this.request<ToolsResponse>(`/tools${query ? `?${query}` : ''}`);\n  }\n\n  async getTool(id: string): Promise<ApiResponse<Tool>> {\n    return this.request<Tool>(`/tools/${id}`);\n  }\n\n  async createTool(toolData: Partial<Tool>): Promise<ApiResponse<Tool>> {\n    return this.request<Tool>('/tools', {\n      method: 'POST',\n      body: JSON.stringify(toolData),\n    });\n  }\n\n  async updateTool(id: string, toolData: Partial<Tool>): Promise<ApiResponse<Tool>> {\n    return this.request<Tool>(`/tools/${id}`, {\n      method: 'PUT',\n      body: JSON.stringify(toolData),\n    });\n  }\n\n  async deleteTool(id: string): Promise<ApiResponse<void>> {\n    return this.request<void>(`/tools/${id}`, {\n      method: 'DELETE',\n    });\n  }\n\n  // 管理员API\n  async getAdminTools(params?: {\n    page?: number;\n    limit?: number;\n    status?: string;\n    search?: string;\n    sort?: string;\n    order?: string;\n  }): Promise<ApiResponse<ToolsResponse>> {\n    const searchParams = new URLSearchParams();\n    if (params) {\n      Object.entries(params).forEach(([key, value]) => {\n        if (value !== undefined) {\n          searchParams.append(key, value.toString());\n        }\n      });\n    }\n    const query = searchParams.toString();\n    return this.request<ToolsResponse>(`/admin/tools${query ? `?${query}` : ''}`);\n  }\n\n  async approveTool(id: string, data: {\n    reviewedBy?: string;\n    reviewNotes?: string;\n    publishedAt?: string;\n  }): Promise<ApiResponse<Tool>> {\n    return this.request<Tool>(`/admin/tools/${id}/approve`, {\n      method: 'POST',\n      body: JSON.stringify(data),\n    });\n  }\n\n  async rejectTool(id: string, data: {\n    reviewedBy?: string;\n    rejectReason: string;\n  }): Promise<ApiResponse<Tool>> {\n    return this.request<Tool>(`/admin/tools/${id}/reject`, {\n      method: 'POST',\n      body: JSON.stringify(data),\n    });\n  }\n\n  async getAdminStats(timeRange?: string): Promise<ApiResponse<AdminStats>> {\n    const query = timeRange ? `?timeRange=${timeRange}` : '';\n    return this.request<AdminStats>(`/admin/stats${query}`);\n  }\n\n  // 分类API\n  async getCategories(): Promise<ApiResponse<CategoriesResponse>> {\n    return this.request<CategoriesResponse>('/categories');\n  }\n}\n\n// 创建默认的API客户端实例\nexport const apiClient = new ApiClient();\n\n// 导出API客户端类以便在需要时创建新实例\nexport { ApiClient };\n"], "names": [], "mappings": "AAAA,YAAY;;;;;AACS;AAArB,MAAM,eAAe,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,wBAAwB,IAAI;AAuH7D,MAAM;IACI,QAAgB;IAExB,YAAY,UAAkB,YAAY,CAAE;QAC1C,IAAI,CAAC,OAAO,GAAG;IACjB;IAEA,MAAc,QACZ,QAAgB,EAChB,UAAuB,CAAC,CAAC,EACA;QACzB,IAAI;YACF,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,GAAG,UAAU;YACxC,MAAM,SAAsB;gBAC1B,SAAS;oBACP,gBAAgB;oBAChB,GAAG,QAAQ,OAAO;gBACpB;gBACA,GAAG,OAAO;YACZ;YAEA,MAAM,WAAW,MAAM,MAAM,KAAK;YAClC,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,KAAK,KAAK,IAAI,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YACxE;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uBAAuB;YACrC,OAAO;gBACL,SAAS;gBACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAClD;QACF;IACF;IAEA,UAAU;IACV,MAAM,SAAS,MAQd,EAAuC;QACtC,MAAM,eAAe,IAAI;QACzB,IAAI,QAAQ;YACV,OAAO,OAAO,CAAC,QAAQ,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;gBAC1C,IAAI,UAAU,WAAW;oBACvB,aAAa,MAAM,CAAC,KAAK,MAAM,QAAQ;gBACzC;YACF;QACF;QACA,MAAM,QAAQ,aAAa,QAAQ;QACnC,OAAO,IAAI,CAAC,OAAO,CAAgB,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC,EAAE,OAAO,GAAG,IAAI;IACxE;IAEA,MAAM,QAAQ,EAAU,EAA8B;QACpD,OAAO,IAAI,CAAC,OAAO,CAAO,CAAC,OAAO,EAAE,IAAI;IAC1C;IAEA,MAAM,WAAW,QAAuB,EAA8B;QACpE,OAAO,IAAI,CAAC,OAAO,CAAO,UAAU;YAClC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,WAAW,EAAU,EAAE,QAAuB,EAA8B;QAChF,OAAO,IAAI,CAAC,OAAO,CAAO,CAAC,OAAO,EAAE,IAAI,EAAE;YACxC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,WAAW,EAAU,EAA8B;QACvD,OAAO,IAAI,CAAC,OAAO,CAAO,CAAC,OAAO,EAAE,IAAI,EAAE;YACxC,QAAQ;QACV;IACF;IAEA,SAAS;IACT,MAAM,cAAc,MAOnB,EAAuC;QACtC,MAAM,eAAe,IAAI;QACzB,IAAI,QAAQ;YACV,OAAO,OAAO,CAAC,QAAQ,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;gBAC1C,IAAI,UAAU,WAAW;oBACvB,aAAa,MAAM,CAAC,KAAK,MAAM,QAAQ;gBACzC;YACF;QACF;QACA,MAAM,QAAQ,aAAa,QAAQ;QACnC,OAAO,IAAI,CAAC,OAAO,CAAgB,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC,EAAE,OAAO,GAAG,IAAI;IAC9E;IAEA,MAAM,YAAY,EAAU,EAAE,IAI7B,EAA8B;QAC7B,OAAO,IAAI,CAAC,OAAO,CAAO,CAAC,aAAa,EAAE,GAAG,QAAQ,CAAC,EAAE;YACtD,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,WAAW,EAAU,EAAE,IAG5B,EAA8B;QAC7B,OAAO,IAAI,CAAC,OAAO,CAAO,CAAC,aAAa,EAAE,GAAG,OAAO,CAAC,EAAE;YACrD,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,cAAc,SAAkB,EAAoC;QACxE,MAAM,QAAQ,YAAY,CAAC,WAAW,EAAE,WAAW,GAAG;QACtD,OAAO,IAAI,CAAC,OAAO,CAAa,CAAC,YAAY,EAAE,OAAO;IACxD;IAEA,QAAQ;IACR,MAAM,gBAA0D;QAC9D,OAAO,IAAI,CAAC,OAAO,CAAqB;IAC1C;AACF;AAGO,MAAM,YAAY,IAAI", "debugId": null}}, {"offset": {"line": 895, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/app/tools/%5Bid%5D/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport Link from 'next/link';\nimport Layout from '@/components/Layout';\nimport LoadingSpinner from '@/components/LoadingSpinner';\nimport ErrorMessage from '@/components/ErrorMessage';\nimport { apiClient, Tool } from '@/lib/api';\nimport {\n  ExternalLink,\n  Heart,\n  Eye,\n  Star,\n  Tag,\n  DollarSign,\n  ArrowLeft,\n  Share2\n} from 'lucide-react';\n\n// 工具详情页面组件\n\ninterface ToolDetailPageProps {\n  params: {\n    id: string;\n  };\n}\n\nexport default function ToolDetailPage({ params }: ToolDetailPageProps) {\n  const [tool, setTool] = useState<Tool | null>(null);\n  const [relatedTools, setRelatedTools] = useState<Tool[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n\n  useEffect(() => {\n    if (params.id) {\n      fetchToolDetails(params.id);\n    }\n  }, [params.id]);\n\n  const fetchToolDetails = async (toolId: string) => {\n    try {\n      setLoading(true);\n      setError('');\n\n      const response = await apiClient.getTool(toolId);\n\n      if (response.success && response.data) {\n        setTool(response.data);\n        // 获取相关工具\n        fetchRelatedTools(response.data.category);\n      } else {\n        setError(response.error || '工具不存在');\n      }\n    } catch (err) {\n      setError('网络错误，请重试');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const fetchRelatedTools = async (category: string) => {\n    try {\n      const response = await apiClient.getTools({\n        category,\n        status: 'approved',\n        limit: 3\n      });\n\n      if (response.success && response.data) {\n        // 排除当前工具\n        const filtered = response.data.tools.filter(t => t._id !== params.id);\n        setRelatedTools(filtered.slice(0, 3));\n      }\n    } catch (err) {\n      // 静默失败，相关工具不是必需的\n    }\n  };\n\n  const getPricingColor = (pricing: string) => {\n    switch (pricing) {\n      case 'free':\n        return 'bg-green-100 text-green-800';\n      case 'freemium':\n        return 'bg-blue-100 text-blue-800';\n      case 'paid':\n        return 'bg-orange-100 text-orange-800';\n      default:\n        return 'bg-gray-100 text-gray-800';\n    }\n  };\n\n  const getPricingText = (pricing: string) => {\n    switch (pricing) {\n      case 'free':\n        return '免费';\n      case 'freemium':\n        return '免费增值';\n      case 'paid':\n        return '付费';\n      default:\n        return pricing;\n    }\n  };\n\n  if (loading) {\n    return (\n      <Layout>\n        <div className=\"min-h-screen flex items-center justify-center\">\n          <LoadingSpinner size=\"lg\" />\n        </div>\n      </Layout>\n    );\n  }\n\n  if (error || !tool) {\n    return (\n      <Layout>\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n          <ErrorMessage\n            message={error || '工具不存在'}\n            onClose={() => setError('')}\n          />\n          <div className=\"text-center mt-8\">\n            <Link\n              href=\"/tools\"\n              className=\"inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n            >\n              <ArrowLeft className=\"w-4 h-4 mr-2\" />\n              返回工具目录\n            </Link>\n          </div>\n        </div>\n      </Layout>\n    );\n  }\n\n  return (\n    <Layout>\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {/* Breadcrumb */}\n        <div className=\"flex items-center space-x-2 text-sm text-gray-500 mb-6\">\n          <Link href=\"/\" className=\"hover:text-blue-600\">首页</Link>\n          <span>/</span>\n          <Link href=\"/tools\" className=\"hover:text-blue-600\">工具目录</Link>\n          <span>/</span>\n          <span className=\"text-gray-900\">{tool.name}</span>\n        </div>\n\n        {/* Back Button */}\n        <div className=\"mb-6\">\n          <Link\n            href=\"/tools\"\n            className=\"inline-flex items-center text-blue-600 hover:text-blue-700\"\n          >\n            <ArrowLeft className=\"mr-2 h-4 w-4\" />\n            返回工具目录\n          </Link>\n        </div>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8\">\n          {/* Main Content */}\n          <div className=\"lg:col-span-2\">\n            {/* Tool Header */}\n            <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6\">\n              <div className=\"flex items-start justify-between mb-6\">\n                <div className=\"flex items-center space-x-4\">\n                  {tool.logo ? (\n                    <img\n                      src={tool.logo}\n                      alt={tool.name}\n                      className=\"w-16 h-16 rounded-lg object-cover\"\n                    />\n                  ) : (\n                    <div className=\"w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center\">\n                      <span className=\"text-white font-bold text-2xl\">\n                        {tool.name.charAt(0).toUpperCase()}\n                      </span>\n                    </div>\n                  )}\n                  <div>\n                    <h1 className=\"text-3xl font-bold text-gray-900 mb-2\">\n                      {tool.name}\n                    </h1>\n                    <div className=\"flex items-center space-x-4\">\n                      <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getPricingColor(tool.pricing)}`}>\n                        <DollarSign className=\"mr-1 h-4 w-4\" />\n                        {getPricingText(tool.pricing)}\n                      </span>\n                      <div className=\"flex items-center space-x-4 text-sm text-gray-500\">\n                        <div className=\"flex items-center space-x-1\">\n                          <Eye className=\"h-4 w-4\" />\n                          <span>{tool.views || 0} 浏览</span>\n                        </div>\n                        <div className=\"flex items-center space-x-1\">\n                          <Heart className=\"h-4 w-4\" />\n                          <span>{tool.likes || 0} 喜欢</span>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n                \n                <div className=\"flex items-center space-x-2\">\n                  <button className=\"p-2 text-gray-400 hover:text-red-500 transition-colors\">\n                    <Heart className=\"h-5 w-5\" />\n                  </button>\n                  <button className=\"p-2 text-gray-400 hover:text-blue-500 transition-colors\">\n                    <Share2 className=\"h-5 w-5\" />\n                  </button>\n                </div>\n              </div>\n\n              {/* Description */}\n              <p className=\"text-gray-600 text-lg leading-relaxed mb-6\">\n                {tool.description}\n              </p>\n\n              {/* Tags */}\n              <div className=\"flex flex-wrap gap-2 mb-6\">\n                {tool.tags?.map((tag, index) => (\n                  <span\n                    key={index}\n                    className=\"inline-flex items-center px-3 py-1 rounded-md text-sm font-medium bg-gray-100 text-gray-700 hover:bg-gray-200 cursor-pointer\"\n                  >\n                    <Tag className=\"mr-1 h-3 w-3\" />\n                    {tag}\n                  </span>\n                ))}\n              </div>\n\n              {/* CTA Button */}\n              <div className=\"flex flex-col sm:flex-row gap-4\">\n                <a\n                  href={tool.website}\n                  target=\"_blank\"\n                  rel=\"noopener noreferrer\"\n                  className=\"inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 transition-colors\"\n                >\n                  <ExternalLink className=\"mr-2 h-5 w-5\" />\n                  访问 {tool.name}\n                </a>\n                <button className=\"inline-flex items-center justify-center px-6 py-3 border border-gray-300 text-base font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 transition-colors\">\n                  <Heart className=\"mr-2 h-5 w-5\" />\n                  添加到收藏\n                </button>\n              </div>\n            </div>\n\n            {/* Features */}\n            {tool.features && tool.features.length > 0 && (\n              <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6\">\n                <h2 className=\"text-xl font-semibold text-gray-900 mb-4\">主要功能</h2>\n                <ul className=\"grid grid-cols-1 md:grid-cols-2 gap-3\">\n                  {tool.features.map((feature, index) => (\n                    <li key={index} className=\"flex items-center space-x-2\">\n                      <Star className=\"h-4 w-4 text-blue-600 flex-shrink-0\" />\n                      <span className=\"text-gray-700\">{feature}</span>\n                    </li>\n                  ))}\n                </ul>\n              </div>\n            )}\n\n            {/* Tool Info - 暂时隐藏pros和cons，因为API中没有这些字段 */}\n          </div>\n\n          {/* Sidebar */}\n          <div className=\"lg:col-span-1\">\n            {/* Tool Info */}\n            <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6\">\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">工具信息</h3>\n              <div className=\"space-y-3\">\n                <div className=\"flex items-center justify-between\">\n                  <span className=\"text-gray-600\">分类</span>\n                  <span className=\"text-gray-900 font-medium\">文本生成</span>\n                </div>\n                <div className=\"flex items-center justify-between\">\n                  <span className=\"text-gray-600\">价格模式</span>\n                  <span className={`px-2 py-1 rounded text-sm font-medium ${getPricingColor(tool.pricing)}`}>\n                    {getPricingText(tool.pricing)}\n                  </span>\n                </div>\n                <div className=\"flex items-center justify-between\">\n                  <span className=\"text-gray-600\">提交日期</span>\n                  <span className=\"text-gray-900\">{tool.submittedAt || tool.createdAt}</span>\n                </div>\n                <div className=\"flex items-center justify-between\">\n                  <span className=\"text-gray-600\">提交者</span>\n                  <span className=\"text-gray-900\">{tool.submittedBy || '未知'}</span>\n                </div>\n              </div>\n            </div>\n\n            {/* Related Tools */}\n            {relatedTools.length > 0 && (\n              <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n                <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">相关工具</h3>\n                <div className=\"space-y-4\">\n                    {relatedTools.map((relatedTool) => (\n                      <div key={relatedTool._id} className=\"border border-gray-200 rounded-lg p-4 hover:shadow-sm transition-shadow\">\n                        <Link href={`/tools/${relatedTool._id}`}>\n                          <h4 className=\"font-medium text-gray-900 hover:text-blue-600 mb-1\">\n                            {relatedTool.name}\n                          </h4>\n                          <p className=\"text-sm text-gray-600 mb-2 line-clamp-2\">\n                            {relatedTool.description}\n                          </p>\n                          <div className=\"flex items-center justify-between text-xs text-gray-500\">\n                            <span className={`px-2 py-1 rounded ${getPricingColor(relatedTool.pricing)}`}>\n                              {getPricingText(relatedTool.pricing)}\n                            </span>\n                            <div className=\"flex items-center space-x-2\">\n                              <span>{relatedTool.views || 0} 浏览</span>\n                              <span>{relatedTool.likes || 0} 喜欢</span>\n                            </div>\n                          </div>\n                        </Link>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n              )}\n          </div>\n        </div>\n      </div>\n    </Layout>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AARA;;;;;;;;AA2Be,SAAS,eAAe,EAAE,MAAM,EAAuB;;IACpE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC3D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,IAAI,OAAO,EAAE,EAAE;gBACb,iBAAiB,OAAO,EAAE;YAC5B;QACF;mCAAG;QAAC,OAAO,EAAE;KAAC;IAEd,MAAM,mBAAmB,OAAO;QAC9B,IAAI;YACF,WAAW;YACX,SAAS;YAET,MAAM,WAAW,MAAM,oHAAA,CAAA,YAAS,CAAC,OAAO,CAAC;YAEzC,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;gBACrC,QAAQ,SAAS,IAAI;gBACrB,SAAS;gBACT,kBAAkB,SAAS,IAAI,CAAC,QAAQ;YAC1C,OAAO;gBACL,SAAS,SAAS,KAAK,IAAI;YAC7B;QACF,EAAE,OAAO,KAAK;YACZ,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,oBAAoB,OAAO;QAC/B,IAAI;YACF,MAAM,WAAW,MAAM,oHAAA,CAAA,YAAS,CAAC,QAAQ,CAAC;gBACxC;gBACA,QAAQ;gBACR,OAAO;YACT;YAEA,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;gBACrC,SAAS;gBACT,MAAM,WAAW,SAAS,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,GAAG,KAAK,OAAO,EAAE;gBACpE,gBAAgB,SAAS,KAAK,CAAC,GAAG;YACpC;QACF,EAAE,OAAO,KAAK;QACZ,iBAAiB;QACnB;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC,+HAAA,CAAA,UAAM;sBACL,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,uIAAA,CAAA,UAAc;oBAAC,MAAK;;;;;;;;;;;;;;;;IAI7B;IAEA,IAAI,SAAS,CAAC,MAAM;QAClB,qBACE,6LAAC,+HAAA,CAAA,UAAM;sBACL,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,qIAAA,CAAA,UAAY;wBACX,SAAS,SAAS;wBAClB,SAAS,IAAM,SAAS;;;;;;kCAE1B,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;;8CAEV,6LAAC,mNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;;;;;;;;;;;IAOlD;IAEA,qBACE,6LAAC,+HAAA,CAAA,UAAM;kBACL,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;sCAAsB;;;;;;sCAC/C,6LAAC;sCAAK;;;;;;sCACN,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAS,WAAU;sCAAsB;;;;;;sCACpD,6LAAC;sCAAK;;;;;;sCACN,6LAAC;4BAAK,WAAU;sCAAiB,KAAK,IAAI;;;;;;;;;;;;8BAI5C,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;wBACH,MAAK;wBACL,WAAU;;0CAEV,6LAAC,mNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;8BAK1C,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;wDACZ,KAAK,IAAI,iBACR,6LAAC;4DACC,KAAK,KAAK,IAAI;4DACd,KAAK,KAAK,IAAI;4DACd,WAAU;;;;;iFAGZ,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAK,WAAU;0EACb,KAAK,IAAI,CAAC,MAAM,CAAC,GAAG,WAAW;;;;;;;;;;;sEAItC,6LAAC;;8EACC,6LAAC;oEAAG,WAAU;8EACX,KAAK,IAAI;;;;;;8EAEZ,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAK,WAAW,CAAC,oEAAoE,EAAE,gBAAgB,KAAK,OAAO,GAAG;;8FACrH,6LAAC,qNAAA,CAAA,aAAU;oFAAC,WAAU;;;;;;gFACrB,eAAe,KAAK,OAAO;;;;;;;sFAE9B,6LAAC;4EAAI,WAAU;;8FACb,6LAAC;oFAAI,WAAU;;sGACb,6LAAC,mMAAA,CAAA,MAAG;4FAAC,WAAU;;;;;;sGACf,6LAAC;;gGAAM,KAAK,KAAK,IAAI;gGAAE;;;;;;;;;;;;;8FAEzB,6LAAC;oFAAI,WAAU;;sGACb,6LAAC,uMAAA,CAAA,QAAK;4FAAC,WAAU;;;;;;sGACjB,6LAAC;;gGAAM,KAAK,KAAK,IAAI;gGAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8DAOjC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAO,WAAU;sEAChB,cAAA,6LAAC,uMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;;;;;;sEAEnB,6LAAC;4DAAO,WAAU;sEAChB,cAAA,6LAAC,6MAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;sDAMxB,6LAAC;4CAAE,WAAU;sDACV,KAAK,WAAW;;;;;;sDAInB,6LAAC;4CAAI,WAAU;sDACZ,KAAK,IAAI,EAAE,IAAI,CAAC,KAAK,sBACpB,6LAAC;oDAEC,WAAU;;sEAEV,6LAAC,mMAAA,CAAA,MAAG;4DAAC,WAAU;;;;;;wDACd;;mDAJI;;;;;;;;;;sDAUX,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,MAAM,KAAK,OAAO;oDAClB,QAAO;oDACP,KAAI;oDACJ,WAAU;;sEAEV,6LAAC,yNAAA,CAAA,eAAY;4DAAC,WAAU;;;;;;wDAAiB;wDACrC,KAAK,IAAI;;;;;;;8DAEf,6LAAC;oDAAO,WAAU;;sEAChB,6LAAC,uMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;;;;;;;;gCAOvC,KAAK,QAAQ,IAAI,KAAK,QAAQ,CAAC,MAAM,GAAG,mBACvC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,6LAAC;4CAAG,WAAU;sDACX,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,sBAC3B,6LAAC;oDAAe,WAAU;;sEACxB,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;sEAChB,6LAAC;4DAAK,WAAU;sEAAiB;;;;;;;mDAF1B;;;;;;;;;;;;;;;;;;;;;;sCAanB,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,6LAAC;4DAAK,WAAU;sEAA4B;;;;;;;;;;;;8DAE9C,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,6LAAC;4DAAK,WAAW,CAAC,sCAAsC,EAAE,gBAAgB,KAAK,OAAO,GAAG;sEACtF,eAAe,KAAK,OAAO;;;;;;;;;;;;8DAGhC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,6LAAC;4DAAK,WAAU;sEAAiB,KAAK,WAAW,IAAI,KAAK,SAAS;;;;;;;;;;;;8DAErE,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,6LAAC;4DAAK,WAAU;sEAAiB,KAAK,WAAW,IAAI;;;;;;;;;;;;;;;;;;;;;;;;gCAM1D,aAAa,MAAM,GAAG,mBACrB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,6LAAC;4CAAI,WAAU;sDACV,aAAa,GAAG,CAAC,CAAC,4BACjB,6LAAC;oDAA0B,WAAU;8DACnC,cAAA,6LAAC,+JAAA,CAAA,UAAI;wDAAC,MAAM,CAAC,OAAO,EAAE,YAAY,GAAG,EAAE;;0EACrC,6LAAC;gEAAG,WAAU;0EACX,YAAY,IAAI;;;;;;0EAEnB,6LAAC;gEAAE,WAAU;0EACV,YAAY,WAAW;;;;;;0EAE1B,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,WAAW,CAAC,kBAAkB,EAAE,gBAAgB,YAAY,OAAO,GAAG;kFACzE,eAAe,YAAY,OAAO;;;;;;kFAErC,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;;oFAAM,YAAY,KAAK,IAAI;oFAAE;;;;;;;0FAC9B,6LAAC;;oFAAM,YAAY,KAAK,IAAI;oFAAE;;;;;;;;;;;;;;;;;;;;;;;;;mDAd5B,YAAY,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4B/C;GA5SwB;KAAA", "debugId": null}}]}